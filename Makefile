.DEFAULT_GOAL := build
SHELL := /bin/bash

LB_VERSION ?= 3.5.3

PG_VERSION ?= 10

PG_JDBC_VERSION ?= 42.1.1

REPO ?= skywindgroup

GC_REGION ?= eu.gcr.io

GC_PROJECT ?= r-stage

APPLICATION ?= sw-db

BRANCH_NAME_NORMALIZED ?= local

# VERSION ?= $(shell git ls-remote --tags  -q | cut -f2 -d'	' | cut -f3 -d/ | head -1)

GREP := $(shell command -v ggrep 2> /dev/null)

.PHONY: build run

build:
	@echo Building ; \
	docker build -t $(REPO)/$(APPLICATION):$(BRANCH_NAME_NORMALIZED) .

run: build
	@echo Running ;\
	$(eval container := $(shell docker run -it --rm -d $(REPO)/$(APPLICATION):$(BRANCH_NAME_NORMALIZED)))
