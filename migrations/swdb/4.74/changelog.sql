--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset dmitriy.palaznik:2021-11-29_SWS-30685_fix_creation_of_new_pk
--comment delete all entries from flat_reports table
SET search_path = swmanagement;

DELETE FROM flat_reports;

RESET search_path;
--rollback select now();


--changeset dmitriy.palaznik:2021-11-19_SWS-30685_update_report_instead_of_creating_new_one
--comment Remove id, game_code, game_group, currency columns
SET search_path = swmanagement;
ALTER TABLE flat_reports DROP COLUMN id;
ALTER TABLE flat_reports DROP COLUMN game_code;
ALTER TABLE flat_reports DROP COLUMN game_group;
ALTER TABLE flat_reports DROP COLUMN currency;

ALTER TABLE flat_reports ADD PRIMARY KEY (entity_id, report_type);

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE flat_reports ADD COLUMN id character varying(255);
--rollback ALTER TABLE flat_reports ADD COLUMN game_code character varying(255);
--rollback ALTER TABLE flat_reports ADD COLUMN game_group character varying(255);
--rollback ALTER TABLE flat_reports ADD COLUMN currency character varying(3);
--rollback COMMENT ON COLUMN flat_reports.game_code IS 'Game code which assosiated with a report';
--rollback COMMENT ON COLUMN flat_reports.game_group IS 'Game group which assosiated with a report';
--rollback COMMENT ON COLUMN flat_reports.currency IS 'Currency which assosiated with a report';
--rollback ALTER TABLE flat_reports DROP PRIMARY KEY;
--rollback ALTER TABLE flat_reports ADD PRIMARY KEY (id);
--rollback RESET search_path;


--changeset pavel.shamshurov:2021-11-19-SWS-31156-add-jokers-wheel-physical-table-type runInTransaction:false
--comment Add jokersWheel physical table type
SET search_path = swmanagement;
ALTER TYPE enum_pht_provider_game_codes_game_type ADD VALUE IF NOT EXISTS 'jokersWheel';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM pg_enum where enumlabel = 'jokersWheel' and enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_pht_provider_game_codes_game_type');
--rollback RESET search_path;


--changeset mikhail.ivanov:2021-11-24-SWS-30995
--comment rename LOGIN audit summary to the LOGIN OLD
SET search_path = swmanagement;
UPDATE audits_summary SET summary = 'LOGIN OLD' WHERE event_name = 'LOGIN' AND summary = 'LOGIN' AND path = 'LOGIN';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE audits_summary SET summary = 'LOGIN' WHERE event_name = 'LOGIN' AND summary = 'LOGIN OLD' AND path = 'LOGIN';
--rollback RESET search_path;
