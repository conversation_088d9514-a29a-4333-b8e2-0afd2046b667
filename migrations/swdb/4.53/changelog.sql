--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset artur.stepovyi:2021-01-13-UASDA-673-login-livestudio-user-by-rf-id
--comment fix add missing index, add rfId
SET search_path = swmanagement;
ALTER TABLE livestudio_users ADD IF NOT EXISTS rf_id VARCHAR(255);
COMMENT ON COLUMN livestudio_users.rf_id IS 'Livestudio rfId to login by NFC card';
CREATE INDEX IF NOT EXISTS idx_livestudio_users_entity_id ON livestudio_users (entity_id);
CREATE INDEX IF NOT EXISTS idx_livestudio_users_rf_id ON livestudio_users (rf_id);
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP INDEX IF EXISTS idx_livestudio_users_rf_id;
--rollback DROP INDEX IF EXISTS idx_livestudio_users_entity_id;
--rollback ALTER TABLE livestudio_users DROP IF EXISTS rf_id;
--rollback RESET search_path;


--changeset nikita.senko:2021-01-14-SWS-23559-test-merchant-player
--comment add new field in test merchant player table
SET search_path = swmanagement;
CREATE TYPE enum_merchant_test_players_source as enum ('integration', 'support');
ALTER TABLE merchant_test_players ADD COLUMN IF NOT EXISTS source enum_merchant_test_players_source;
COMMENT ON COLUMN merchant_test_players.source IS 'who add test users. support - human, or integration - code';
ALTER TABLE merchant_test_players ADD COLUMN IF NOT EXISTS start_date TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now();
COMMENT ON COLUMN merchant_test_players.start_date IS 'start date for test user';
ALTER TABLE merchant_test_players ADD COLUMN IF NOT EXISTS end_date TIMESTAMP WITHOUT TIME ZONE;
COMMENT ON COLUMN merchant_test_players.end_date IS 'end date for test user';
UPDATE merchant_test_players SET source = 'integration' where source is null;
UPDATE merchant_test_players SET start_date = created_at where start_date is null;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE merchant_test_players DROP COLUMN IF EXISTS source;
--rollback ALTER TABLE merchant_test_players DROP COLUMN IF EXISTS start_date;
--rollback ALTER TABLE merchant_test_players DROP COLUMN IF EXISTS end_date;
--rollback DROP TYPE enum_merchant_test_players_source;
--rollback RESET search_path;


--changeset nikita.senko:2021-01-14-SWS=23560-update-status
--comment update status field in entity_games table
SET search_path = swmanagement;
CREATE TYPE enum_entity_games_status as enum ('normal', 'suspended', 'test');
ALTER TABLE entity_games ALTER COLUMN status DROP DEFAULT;
ALTER TABLE entity_games ALTER COLUMN status TYPE enum_entity_games_status USING status::TEXT::enum_entity_games_status;
ALTER TABLE entity_games ALTER COLUMN status SET DEFAULT 'normal'::enum_entity_games_status;
DROP TYPE IF EXISTS "enum_entityGames_status";
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback CREATE TYPE "enum_entityGames_status" as enum ('normal', 'suspended');
--rollback ALTER TABLE entity_games ALTER COLUMN status DROP DEFAULT;
--rollback ALTER TABLE entity_games ALTER COLUMN status TYPE "enum_entityGames_status" USING status::TEXT::"enum_entityGames_status";
--rollback ALTER TABLE entity_games ALTER COLUMN status SET DEFAULT 'normal'::"enum_entityGames_status";
--rollback DROP TYPE IF EXISTS enum_entity_games_status;
--rollback RESET search_path;


--changeset nikita.senko:2021-01-18-SWS=23561-update-entity-status runInTransaction:false
--comment update status field in entity table
SET search_path = swmanagement;
ALTER TYPE enum_entities_status ADD VALUE IF NOT EXISTS 'test';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM pg_enum where enumlabel = 'test' and enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_entities_status');
--rollback RESET search_path;


--changeset aleh.rudzko:2021-01-21-SWS-24258-add-new-methods-for-levels-by-entity
--comment Add new permissions for new api
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'limit-level:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'limit-level:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'limit-level:update' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'entity-game-limit-level:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'entity-game-limit-level:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["entity-game-limit-level:delete","entity-game-limit-level:create","limit-level:create","limit-level:delete","limit-level:update"]' WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'limit-level:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'limit-level:delete' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'limit-level:update' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entity-game-limit-level:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entity-game-limit-level:delete' WHERE id = 1;
--rollback RESET search_path;


--changeset aleh.rudzko:2021-01-21-SWS-24174-add-opportunity-to-control-customization
--comment Add new field - inherited: Boolean
SET search_path = swmanagement;
ALTER TABLE entity_game_limit_levels ADD COLUMN inherited BOOLEAN NOT NULL DEFAULT FALSE;
COMMENT ON COLUMN entity_game_limit_levels.inherited IS 'Flag which means that entity game limit levels will be inherited to child entities';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entity_game_limit_levels DROP COLUMN inherited;
--rollback RESET search_path;

--changeset nikita.senko:2021-02-04-SWS-24815-unique-title-in-physical-table
--comment set title colum unique in pht_physical_tables
SET search_path = swmanagement;
DELETE FROM
pht_physical_tables a
USING pht_physical_tables b
WHERE
a.id < b.id
AND a.title = b.title
AND a.entity_id = b.entity_id;
CREATE UNIQUE INDEX IF NOT EXISTS idx_pht_physical_tables_entity_id_title ON pht_physical_tables (entity_id, title);
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP INDEX IF EXISTS idx_pht_physical_tables_entity_id_title;
--rollback RESET search_path;

--changeset stepanov.aleksey:2021-02-08-SWS-23798
--comment Remove view 'merchant_player_nick_name' from DB
SET search_path = swmanagement;
DROP VIEW IF EXISTS merchant_player_nick_name;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback CREATE OR REPLACE VIEW merchant_player_nick_name AS SELECT * FROM merchant_player_info;
--rollback RESET search_path;


--changeset nikita.senko:2021-02-09-SWS-24992
--comment add new columns in players_info
SET search_path = swmanagement;
ALTER TABLE players_info RENAME COLUMN is_chat_block TO is_public_chat_block;
ALTER TABLE players_info ADD COLUMN is_private_chat_block BOOLEAN NOT NULL DEFAULT FALSE;
COMMENT ON COLUMN players_info.is_private_chat_block IS 'Flag which means that player blocked in private chat';
ALTER TABLE players_info ADD COLUMN has_warn BOOLEAN NOT NULL DEFAULT FALSE;
COMMENT ON COLUMN players_info.has_warn IS 'Flag which means that player has warn in chat';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE players_info RENAME COLUMN is_public_chat_block TO is_chat_block;
--rollback ALTER TABLE players_info DROP COLUMN is_private_chat_block;
--rollback ALTER TABLE players_info DROP COLUMN has_warn;
--rollback RESET search_path;

--changeset nikita.senko:2021-02-10-SWS-24992
--comment add backward compatibility
SET search_path = swmanagement;
ALTER TABLE players_info ADD COLUMN is_chat_block  BOOLEAN NOT NULL DEFAULT FALSE;
UPDATE players_info SET is_chat_block = is_public_chat_block;
COMMENT ON COLUMN players_info.is_chat_block IS 'Flag for backward compatibility. Need delete in next releases';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE players_info DROP COLUMN is_chat_block;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2021-02-09_SWDB-154_sync_arch_tables
--comment Synchronizing tables structure in archive schemes where mismatches were detected according to releases.
ALTER TABLE swmanagement_archive.audits     ADD IF NOT EXISTS initiator_service_name    varchar(255); 
ALTER TABLE swmanagement_archive_ro.audits  ADD IF NOT EXISTS initiator_service_name    varchar(255);
ALTER TABLE swmanagement_archive.spins_history      ADD IF NOT EXISTS debit     numeric;
ALTER TABLE swmanagement_archive_ro.spins_history   ADD IF NOT EXISTS debit     numeric;
ALTER TABLE swmanagement_archive.spins_history      ADD IF NOT EXISTS credit    numeric;
ALTER TABLE swmanagement_archive_ro.spins_history   ADD IF NOT EXISTS credit    numeric;

--rollback select now();


--changeset oleg.rudko:2021-02-11-SWS-21965-use-master-game-limits-without-game-code
--comment add new fields and relations, copy old data to tmp table for rollback, remove entity game relations
SET search_path = swmanagement;

CREATE TABLE IF NOT EXISTS swbackup.entity_game_limit_levels_210211 AS SELECT * FROM entity_game_limit_levels;
COMMENT ON TABLE swbackup.entity_game_limit_levels_210211 IS 'Temporary table for entity game limit levels, should be removed after 4.53';
DELETE FROM entity_game_limit_levels;

ALTER TABLE entity_game_limit_levels DROP COLUMN IF EXISTS entity_game_id;

ALTER TABLE entity_game_limit_levels ADD COLUMN IF NOT EXISTS entity_id INTEGER NOT NULL;
COMMENT ON COLUMN entity_game_limit_levels.entity_id IS 'Reference to entity';
ALTER TABLE entity_game_limit_levels ADD COLUMN IF NOT EXISTS schema_definition_id INTEGER;
COMMENT ON COLUMN entity_game_limit_levels.schema_definition_id IS 'Reference to schema definition';
ALTER TABLE entity_game_limit_levels ADD COLUMN IF NOT EXISTS game_code VARCHAR(255);
COMMENT ON COLUMN entity_game_limit_levels.game_code IS 'Game code';

ALTER TABLE ONLY entity_game_limit_levels
    ADD FOREIGN KEY (entity_id) REFERENCES entities(id) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE ONLY entity_game_limit_levels
    ADD FOREIGN KEY (schema_definition_id) REFERENCES schema_definitions(id) ON UPDATE CASCADE ON DELETE CASCADE;

CREATE INDEX IF NOT EXISTS idx_entity_game_limit_levels_entity_id ON entity_game_limit_levels (entity_id);
CREATE INDEX IF NOT EXISTS idx_entity_game_limit_levels_schema_definition_id ON entity_game_limit_levels (schema_definition_id);

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entity_game_limit_levels DROP column IF EXISTS entity_id;
--rollback ALTER TABLE entity_game_limit_levels DROP COLUMN IF EXISTS schema_definition_id;
--rollback ALTER TABLE entity_game_limit_levels DROP COLUMN IF EXISTS game_code;
--rollback ALTER TABLE entity_game_limit_levels ADD COLUMN IF NOT EXISTS entity_game_id INTEGER;
--rollback COMMENT ON COLUMN entity_game_limit_levels.entity_game_id IS 'Reference to entity game id';
--rollback ALTER TABLE ONLY entity_game_limit_levels ADD FOREIGN KEY (entity_game_id) REFERENCES entity_games(id) ON UPDATE CASCADE ON DELETE CASCADE;
--rollback CREATE INDEX IF NOT EXISTS idx_entity_game_limit_levels_entity_game_id ON entity_game_limit_levels (entity_game_id);
--rollback DELETE FROM entity_game_limit_levels;
--rollback INSERT INTO entity_game_limit_levels SELECT * FROM swbackup.entity_game_limit_levels_210211;
--rollback DROP TABLE IF EXISTS swbackup.entity_game_limit_levels_210211;
--rollback RESET search_path;

--changeset oleg.rudko:2021-02-12-SWS-21965-use-master-game-limits-without-game-code
--comment Return backward compatibility for entity_game_limit_levels table
SET search_path = swmanagement;
ALTER TABLE entity_game_limit_levels ADD COLUMN IF NOT EXISTS entity_game_id INTEGER;
COMMENT ON COLUMN entity_game_limit_levels.entity_game_id IS 'Reference to entity game id';
ALTER TABLE entity_game_limit_levels ALTER COLUMN entity_id DROP NOT NULL;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entity_game_limit_levels DROP COLUMN IF EXISTS entity_game_id;
--rollback ALTER TABLE entity_game_limit_levels ALTER COLUMN entity_id SET NOT NULL;
--rollback RESET search_path;
