--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset dmitriy.palaznik:2023-09-05-SWS-41857
--comment [JPN] Add new column 'info' to jp_instance
SET search_path = swjackpot;

ALTER TABLE jp_instance ADD COLUMN info JSONB;
COMMENT ON COLUMN jp_instance.info IS 'Jackpot instance additional info';

RESET search_path;

--rollback SET search_path = swjackpot;
--rollback ALTER TABLE jp_instance DROP COLUMN info;
--rollback RESET search_path;
