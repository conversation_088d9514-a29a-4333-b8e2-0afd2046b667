--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset dmitriy.palaznik:2023-07-25-SWS-41035-external-history-phase-1
--comment add ctrl field to ext_bet_win_history and create ext_bet_win_history_duplicates table
SET search_path TO swadaptergos;

CREATE SEQUENCE ext_bet_win_history_duplicates_seq;

CREATE TABLE ext_bet_win_history_duplicates (
    id bigint PRIMARY KEY NOT NULL DEFAULT nextval('ext_bet_win_history_duplicates_seq'),
    ext_trx_id character varying(255) NOT NULL,
    bet numeric,
    win numeric,
    currency character(3) NOT NULL,
    brand_id integer NOT NULL,
    player_code character varying(255) NOT NULL,
    round_id bigint NOT NULL,
    game_code character varying(255),
    balance_before numeric,
    balance_after numeric,
    is_test boolean,
    inserted_at timestamp without time zone NOT NULL DEFAULT now(),
    game_provider_code character varying(55) NOT NULL,
    provider_specific_data jsonb,
    is_rollbacked boolean DEFAULT false,
    is_round_closed boolean DEFAULT false,
    recovery_type enum_ext_bet_win_history_recovery_type,
    event_id integer DEFAULT 0,
    ctrl integer
);

COMMENT ON COLUMN ext_bet_win_history_duplicates.ext_trx_id IS 'Transaction id that came from gameprovider';
COMMENT ON COLUMN ext_bet_win_history_duplicates.round_id IS 'Round id that came from gameprovider';
COMMENT ON COLUMN ext_bet_win_history_duplicates.game_provider_code IS 'Code that uniquely identifies gameprovider';
COMMENT ON COLUMN ext_bet_win_history_duplicates.provider_specific_data IS 'Gameprovider specific data, such as links to their history servers & etc';
COMMENT ON COLUMN ext_bet_win_history_duplicates.is_rollbacked IS 'Set true if transaction of this record was rollbacked';
COMMENT ON COLUMN ext_bet_win_history_duplicates.is_round_closed IS 'Determines whether the round is marked as closed by the game provider';
COMMENT ON COLUMN ext_bet_win_history_duplicates.recovery_type IS 'The recovery type if the round was recovered by game provider';
COMMENT ON COLUMN ext_bet_win_history_duplicates.event_id IS 'Represent an internal rounds counter (starts from 0) for a number of occurred "spins".';
COMMENT ON COLUMN ext_bet_win_history_duplicates.ctrl IS 'Control sum (random) to check logical duplicates';

CREATE INDEX IF NOT EXISTS idx_ext_bet_win_history_duplicates_rid ON ext_bet_win_history_duplicates (round_id) WITH (fillfactor='100');
CREATE INDEX IF NOT EXISTS idx_ext_bet_win_duplicates_inserted_at ON ext_bet_win_history_duplicates (inserted_at);

ALTER TABLE ext_bet_win_history ADD COLUMN ctrl integer;
COMMENT ON COLUMN ext_bet_win_history.ctrl IS 'Control sum (random) to check logical duplicates';

RESET search_path;

--rollback SET search_path TO swadaptergos;
--rollback DROP TABLE ext_bet_win_history_duplicates;
--rollback DROP SEQUENCE ext_bet_win_history_duplicates_seq;
--rollback ALTER TABLE ext_bet_win_history DROP COLUMN ctrl;
--rollback RESET search_path;

--changeset valdis.akmens:2023-08-11-SWDB-322-change-first_activity-calculation endDelimiter:# stripComments:false
--comment Change bo_aggr_player_rounds.first_activity calculation
SET search_path TO swmanagement;

ALTER FUNCTION fnc_bo_aggr_refresh_jobs (TIMESTAMP, VARCHAR) RENAME TO fnc_bo_aggr_refresh_jobs_before_5_12;

CREATE OR REPLACE FUNCTION fnc_bo_aggr_refresh_jobs(p_force_end_hour timestamp without time zone, p_work_mem character varying DEFAULT NULL::character varying)
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
*******************************************************************************

   Object Name:   fnc_bo_aggr_refresh_jobs
   Purpose    :   To perform B/O aggregation jobs
   History    :
      1.0.0
         Date    : Feb 03, 2017
         Authors : Timur Luchkin
         Notes   : Release (BYDEVO-260)

      1.0.1
         Date    : Mar 07, 2017
         Authors : Timur Luchkin
         Notes   : Add more details required for watchdog to monitor jobs
                   (BYSWBO-73)

      1.0.2
         Date    : Jun 09, 2017
         Authors : Timur Luchkin
         Notes   : Tables renamed to follow snake style
                   (BYDEVO-578)
                   Add logs history logging

      1.0.3
         Date    : Jul 17, 2017
         Authors : Andrey Shmigiro
         Notes   : Added table "bo_aggr_win_bets" for aggregation data from "wallet_win_bet"
                   (BYDEVO-513)

      1.0.4
         Date    : Jul 24, 2017
         Authors : Andrey Shmigiro
         Notes   : Added table "bo_aggr_player_rounds" for aggregation data about played
                     rounds from "bo_aggr_rounds";
                   Added column "exchange_rate" to table "bo_aggr_win_bets";
                   Changed calculation method for column "played_games_qty" in table "bo_aggr_win_bets";
                   (SWS-1561)

      1.0.5
         Date    : Aug 15, 2017
         Authors : Andrey Shmigiro
         Notes   : Added column "start_balance", "end_balance", "device_code" to table
                   "bo_aggr_player_rounds";
                   (SWS-1651 / SWS-1720)

      1.1.0
         Date    : Sep 28, 2017
         Authors : Timur Luchkin
         Notes   : Fix issues with missed spins (SWS-1873)
                   More logging details

      1.1.1
         Date    : Oct 19, 2017
         Authors : Andrey Shmigiro
         Notes   : Exclude test wallet operations from bo_aggr_win_bets (SWS-1789)
                   Exclude jackpot wins from bo_aggr_win_bets (SWS-1863)
                   Exclude free bets from bo_aggr_win_bets (SWS-1943)

      1.1.2
         Date    : Oct 19, 2017
         Authors : Andrey Shmigiro
         Notes   : Added columns for jackpot's and free_bet's wins to bo_agr_win_bets's tbl (SWS-2805)
                   Exclude NULLs values of start&end balances for bo_aggr_rounds

      1.1.3
            Date    : Jan 12, 2018
            Authors : Valdis Akmens
            Notes   : Change aggregation of wallet_win_bet from payment_date to inserted_at (BYDEVO-1280)

      1.1.4
            Date    : Mar 08, 2018
            Authors : Valdis Akmens
            Notes   : fnc_bo_aggr_refresh_jobs takes too much time after the partitioning has been installed (SWDB-24)
                Added new parameter p_work_mem to set larger work_mem for function to get rid off "Sort Method: external merge  Disk"
                Changed bo_aggr_win_bets aggregation to replace "LEFT JOIN" with sub-queries (because of partitioning, wrong estimations lead to non-optimal JOIN strategies)
                p_work_mem: NULL, 64MB, 128MB, 256MB ..

      1.1.5
            Date    : Jun 11, 2018
            Authors : Valdis Akmens
            Notes   : Change aggregation source for tables "bo_aggr_brand_currency" and "bo_aggr_player_rounds"
                         from "bo_aggr_rounds" to "rounds_history"(SWDB-49)

      1.1.6
            Date    : Sep 17, 2018
            Authors : Valdis Akmens
            Notes   : Remove "bo_aggr_rounds" from aggregation completely (SWDB-69)

      1.1.7
          Date    : Nov 06, 2018
          Authors : Timur Luchkin
          Notes   : Change aggregation logic to allow "bo_aggr_player_rounds" and "bo_aggr_win_bets" tables partitioning (SWDB-44)

      1.1.8
          Date    : Mar 22, 2019
          Authors : Timur Luchkin
          Notes   : Change aggregation source table from rounds_history to rounds_finished

      1.1.9
         Date    : Apr 30, 2019
         Authors : Andrey Shmigiro
         Notes   : Added column "debit", "credit" to table "bo_aggr_win_bets" (DEVOPS-5075);

      1.2.0
         Date    : Jul 30, 2019
         Authors : Valdis Akmens
         Notes   : Change aggregation of wins for table "bo_aggr_win_bets" and "bo_aggr_win_bets_by_brand" because of new wallet_transactions_types (SWS-11908);

      1.2.1
         Date    : Aug 22, 2019
         Authors : Valdis Akmens
         Notes   : Subtract "bet" for table "bo_aggr_win_bets" and "bo_aggr_win_bets_by_brand" aggregation when bet_rollback = TRUE  (SWS-12584);

      1.2.2
         Date    : Apr 15, 2020
         Authors : Valdis Akmens
         Notes   : Replace CTE(DELETE+INSERT) to temporary tables (SWDB-132);

      1.2.2.1
         Date    : Oct 14, 2020
         Authors : Valdis Akmens
         Notes   : Ignore transactions in wallet_win_bet where game_code is null (SWS-22290);

      1.2.3
         Date    : Oct 12, 2020
         Authors : Valdis Akmens
         Notes   : Change aggregation fields for win,bet calculation, based on wallet_win_bet.ggr_calculation (SWB365-254);

      1.2.4
         Date    : Oct 19, 2020
         Authors : Valdis Akmens
         Notes   : Add aditional check for large intervals, COALESCE for bo_aggr_win_bets.total_bets, bo_aggr_win_bets.total_wins (DEVOPS-10732, SWS-22341);

      1.2.5
         Date    : Aug 11, 2023
         Authors : Valdis Akmens
         Notes   : Change bo_aggr_player_rounds.first_activity calculation (SWDB-322);

  Sample run:
      SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL);
      SELECT * FROM fnc_bo_aggr_refresh_jobs ('2016-11-28 23:00:00');

	    SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL, NULL);
      SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL, '64MB');
	    SELECT * FROM fnc_bo_aggr_refresh_jobs ('2017-09-25 06:00:00', '64MB');

*******************************************************************************
*/
DECLARE
   v_last_inserted_at      TIMESTAMP;
   v_new_inserted_at       TIMESTAMP;
   v_time_back_msec        INTEGER;
   v_counter               BIGINT;
   v_job_start_time        TIMESTAMP;
   v_force_end_time        TIMESTAMP;
   v_huge_interval  INTERVAL := '12 hours'::INTERVAL;
BEGIN

     log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job started'; RETURN NEXT;

     /* Check if MDB */
     IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
     END IF;

     /* Change work_mem parameter */
     IF p_work_mem IS NOT NULL THEN
        EXECUTE 'SET work_mem TO '''||p_work_mem||'''' ;
     END IF;

     /* To prevent misses of the lazy offloaded data */
     SELECT split_part(sett,'=',2)::INTEGER
     INTO   v_time_back_msec
     FROM   (SELECT unnest(useconfig) AS sett
             FROM   pg_user
            WHERE  usename = 'redis_game_offloader'
            ) t1
     WHERE sett LIKE 'statement_timeout=%';

     IF NOT FOUND THEN
        v_time_back_msec := 60 * 1000;
     END IF;

     /* manyachello */
     v_time_back_msec := v_time_back_msec + 5000;

    /* ~~~ bo_aggr_brand_currency ~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   v_job_start_time := clock_timestamp();
   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_brand_currency" started'; RETURN NEXT;

   SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_brand_currency' AND conf_key = 'lock_record' FOR UPDATE;

   IF NOT FOUND THEN
      RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_brand_currency/lock_record" pair';
   END IF;

   v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                             ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                             );

    /* Automatically prevent too huge intervals */
     IF (v_force_end_time - v_last_inserted_at) > v_huge_interval THEN
        v_force_end_time := v_last_inserted_at + v_huge_interval;
     END IF;

    WITH fresh_data AS
    (
        SELECT
        Date_Trunc('HOUR', h.finished_at)       AS date_hour
        ,h.brand_id                             AS brand_id
        ,h.currency                             AS currency_code
        ,SUM(h.total_bet)                       AS bet
        ,SUM(h.total_win)                       AS win
        ,SUM(h.total_bet)  - SUM(h.total_win)   AS revenue
        ,COUNT(h.id)::BIGINT                    AS finished_rounds
        ,MAX(COALESCE(inserted_at, started_at)) AS max_inserted_at
    FROM  rounds_finished AS h
    WHERE NOT h.test
      AND COALESCE(inserted_at, started_at) >= v_last_inserted_at
      AND COALESCE(inserted_at, started_at) <  v_force_end_time
    GROUP BY Date_Trunc('HOUR', h.finished_at)
    ,h.brand_id
    ,h.currency
    ),
    cte_upsert AS
    (
        INSERT INTO bo_aggr_brand_currency (date_hour, date_day, brand_id, currency_code, bet, win, revenue, finished_rounds)
        SELECT
                date_hour
                ,Date_Trunc('DAY', date_hour)::DATE     AS date_day
                ,brand_id
                ,currency_code
                ,bet
                ,win
                ,revenue
                ,finished_rounds
        FROM fresh_data
        ON CONFLICT (date_hour, brand_id, currency_code) DO
        UPDATE SET
                bet             = bo_aggr_brand_currency.bet            + EXCLUDED.bet,
                win             = bo_aggr_brand_currency.win            + EXCLUDED.win,
                revenue         = bo_aggr_brand_currency.revenue        + EXCLUDED.revenue,
                finished_rounds = bo_aggr_brand_currency.finished_rounds + EXCLUDED.finished_rounds
    )
   SELECT  MAX(max_inserted_at), COUNT(*)
   FROM   fresh_data
   INTO    v_new_inserted_at, v_counter;

   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_brand_currency" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

   UPDATE bo_aggr_config SET
           conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
   WHERE  aggr_job_name = 'bo_aggr_brand_currency'
   AND  conf_key = 'lock_record';

   -- Log
   INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
      VALUES ('bo_aggr_brand_currency', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time), v_force_end_time);

   /* ~~~ bo_aggr_player_rounds ~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   v_job_start_time := clock_timestamp();
   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_player_rounds" started'; RETURN NEXT;

   SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_player_rounds' AND conf_key = 'lock_record' FOR UPDATE;

   IF NOT FOUND THEN
      RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_player_rounds/lock_record" pair';
   END IF;

   v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                             ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                             );

    /* Automatically prevent too huge intervals */
    IF (v_force_end_time - v_last_inserted_at) > v_huge_interval THEN
    v_force_end_time := v_last_inserted_at + v_huge_interval;
    END IF;

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_rounds_finished_fresh AS
        SELECT Date_Trunc('HOUR', h.finished_at)          AS date_hour
            ,Date_Trunc('HOUR', h.finished_at)::date    AS date_day
            ,h.brand_id                                 AS brand_id
            ,h.player_code
            ,h.game_code
            ,h.currency                                 AS currency_code
            ,MAX(h.device_id)                           AS device_code    /* This is wrong! We should add this field to the primary key!!! */
            ,COUNT(h.id)::BIGINT                        AS rounds_qty
            ,SUM(h.total_events)::INTEGER               AS events_qty
            ,SUM(h.total_bet)                           AS total_bet
            ,SUM(h.total_win)                           AS total_win
            ,SUM(h.total_bet)  - SUM(h.total_win)       AS total_revenue
            ,null::numeric                              AS start_balance
            ,null::numeric                              AS end_balance
            ,MIN(case when h.started_at < Date_Trunc('HOUR', h.finished_at) then h.finished_at else h.started_at end) AS first_activity
            ,MAX(h.finished_at)                         AS last_activity
            ,MAX(COALESCE(inserted_at, started_at))     AS max_inserted_at
        FROM rounds_finished AS h
        WHERE
            NOT  h.test
            AND  COALESCE(inserted_at, started_at) >= v_last_inserted_at
            AND  COALESCE(inserted_at, started_at) <  v_force_end_time
        GROUP BY h.player_code, h.brand_id, h.currency, h.game_code, date_trunc('HOUR', h.finished_at);

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_rounds_finished_deleted AS SELECT * FROM bo_aggr_player_rounds LIMIT 0;
    WITH
    cte_delete_existing_data AS (
        DELETE FROM bo_aggr_player_rounds d
        USING tmp_bo_aggr_refresh_jobs_rounds_finished_fresh f
        WHERE  d.date_hour      = f.date_hour
            AND  d.brand_id       = f.brand_id
            AND  d.game_code      = f.game_code
            AND  d.currency_code  = f.currency_code
            AND  d.player_code    = f.player_code
        RETURNING d.*
    )
    INSERT INTO tmp_bo_aggr_refresh_jobs_rounds_finished_deleted
    SELECT * FROM cte_delete_existing_data;

    INSERT INTO bo_aggr_player_rounds (date_hour, date_day, brand_id, player_code, game_code, device_code, currency_code, exchange_rate, rounds_qty, events_qty, total_bet, total_win, total_revenue, start_balance, end_balance, first_activity, last_activity)
        SELECT fd.date_hour,
            fd.date_hour::DATE,
            fd.brand_id,
            fd.player_code,
            fd.game_code,
            coalesce(fd.device_code, '') AS device_code,
            fd.currency_code,
            fd.exchange_rate,
            fd.rounds_qty,
            fd.events_qty,
            fd.total_bet,
            fd.total_win,
            fd.total_revenue,
            fd.start_balance,
            fd.end_balance,
            fd.first_activity,
            fd.last_activity
        FROM (
                SELECT date_hour
                    ,brand_id
                    ,player_code
                    ,game_code
                    ,currency_code
                    ,MAX(device_code)       AS device_code
                    ,Avg(exchange_rate)     AS exchange_rate
                    ,SUM(rounds_qty   )     AS rounds_qty
                    ,SUM(events_qty   )     AS events_qty
                    ,SUM(total_bet    )     AS total_bet
                    ,SUM(total_win    )     AS total_win
                    ,SUM(total_revenue)     AS total_revenue
                    ,(array_remove(array_agg(start_balance ORDER BY first_activity ASC), NULL))[1] AS start_balance
                    ,(array_remove(array_agg(end_balance ORDER BY last_activity DESC), NULL))[1] AS end_balance
                    ,Min(first_activity)    AS first_activity
                    ,MAX(last_activity)     AS last_activity
                FROM   (
                        SELECT date_hour
                                ,brand_id
                                ,player_code
                                ,game_code
                                ,device_code
                                ,currency_code
                                ,(SELECT cr2.rate
                                FROM   currency_rates cr2
                                WHERE  cr2.currency_code = t0.currency_code
                                    AND  cr2.rate_date <= t0.date_day
                                ORDER BY cr2.rate_date DESC LIMIT 1) AS exchange_rate
                                ,rounds_qty
                                ,events_qty
                                ,total_bet
                                ,total_win
                                ,total_revenue
                                ,start_balance
                                ,end_balance
                                ,first_activity
                                ,last_activity
                        FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_fresh t0
                        UNION ALL
                        SELECT date_hour
                                ,brand_id
                                ,player_code
                                ,game_code
                                ,NULL::VARCHAR AS device_code /* We can't use old value, because insert can fault due to current PK */
                                ,currency_code
                                ,exchange_rate
                                ,rounds_qty
                                ,events_qty
                                ,total_bet
                                ,total_win
                                ,total_revenue
                                ,start_balance
                                ,end_balance
                                ,first_activity
                                ,last_activity
                        FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_deleted
                        ) t
                GROUP BY date_hour, brand_id, game_code, currency_code, player_code
            ) fd;

    SELECT  MAX(max_inserted_at), COUNT(*)
    FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_fresh
    INTO    v_new_inserted_at, v_counter;

    DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_rounds_finished_fresh;
    DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_rounds_finished_deleted;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_player_rounds" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

        UPDATE bo_aggr_config SET
                conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
        WHERE  aggr_job_name = 'bo_aggr_player_rounds'
            AND  conf_key = 'lock_record';

    -- Log
    INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
        VALUES ('bo_aggr_player_rounds', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time), v_force_end_time);

        /* ~~~ bo_aggr_win_bets ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        v_job_start_time := clock_timestamp();
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets" started'; RETURN NEXT;

        SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_win_bets' AND conf_key = 'lock_record' FOR UPDATE;

        IF NOT FOUND THEN
            RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_win_bets/lock_record" pair';
        END IF;

        v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                                ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                                );
        -- Add aditional check to prevent aggregating data of wallet_win_bet for period where data for rounds_finished is not aggregated
        v_force_end_time:= LEAST(v_force_end_time, COALESCE((SELECT MAX(date_hour) FROM bo_aggr_player_rounds) + '1 HOUR'::INTERVAL,v_force_end_time));

        /* Automatically prevent too huge intervals */
        IF (v_force_end_time - v_last_inserted_at) > v_huge_interval THEN
            v_force_end_time := v_last_inserted_at + v_huge_interval;

        END IF;

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh AS
        SELECT date_trunc('HOUR', b.payment_date) AS payment_date_hour,
                b.brand_id,
                b.game_code,
                b.player_code,
                b.currency AS currency_code,
                COUNT(DISTINCT b.game_id) AS played_games_qty,
                COALESCE(SUM(case when b.transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN bet_rollback = FALSE THEN (CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) ELSE (-1)*(CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) END) end), 0) AS total_bets,
                COALESCE(SUM(case when b.transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) end),0) AS total_wins,
                MAX(b.payment_date)::timestamp(0) as last_payment_ts,
                COALESCE(SUM(case when b.transaction_type = 'jackpot' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end),0) as total_jp_wins,
                COALESCE(SUM(case when b.transaction_type = 'free_bet' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end),0) as total_freebet_wins,
                MAX(COALESCE(inserted_at, payment_date)) AS max_inserted_at,
                SUM(debit) AS debit,
                SUM(credit) AS credit
        FROM  wallet_win_bet b
        WHERE COALESCE(inserted_at, payment_date) >= v_last_inserted_at
            AND COALESCE(inserted_at, payment_date) < v_force_end_time
            AND COALESCE(b.is_test, false) = false
            AND b.game_code IS NOT NULL
                -- AND b.transaction_type is null
        GROUP BY b.player_code, b.brand_id, b.currency, b.game_code, date_trunc('HOUR', b.payment_date);

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted AS SELECT * FROM bo_aggr_win_bets LIMIT 0;
    WITH cte_delete_existing_data AS (
            DELETE FROM bo_aggr_win_bets d
            USING tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh f
            WHERE d.payment_date_hour = f.payment_date_hour
            AND d.brand_id          = f.brand_id
            AND d.game_code         = f.game_code
            AND d.currency_code     = f.currency_code
            AND d.player_code       = f.player_code
            RETURNING d.*
        )
    INSERT INTO tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted
    SELECT * FROM cte_delete_existing_data;

    INSERT INTO bo_aggr_win_bets (payment_date_hour, payment_date_day, brand_id, game_code, player_code,
            currency_code, played_games_qty, total_bets, total_wins, last_payment_ts,
            exchange_rate, total_jp_wins, total_freebet_wins, debit, credit)
    SELECT
        d.payment_date_hour,
        d.payment_date_hour::DATE AS payment_date_day,
        d.brand_id,
        d.game_code,
        d.player_code,
        d.currency_code,
        /* bo_aggr_player_rounds - contains only finished rounds. TLU: This value is incorrect and should be fixed. wallet_win_bet should also has finished flags */
        COALESCE( (SELECT pr.rounds_qty
                    FROM   bo_aggr_player_rounds pr
                    WHERE  d.payment_date_hour = pr.date_hour
                    AND  d.brand_id = pr.brand_id
                    AND  d.game_code = pr.game_code
                    AND  d.currency_code = pr.currency_code
                    AND  d.player_code = pr.player_code
                    LIMIT 1), 0) AS played_games_qty,
        d.total_bets,
        d.total_wins,
        d.last_payment_ts,
        (SELECT cr2.rate
        FROM   currency_rates cr2
        WHERE  cr2.currency_code = d.currency_code
            AND  cr2.rate_date <= d.payment_date_hour::DATE
        ORDER BY cr2.rate_date DESC LIMIT 1) AS exchange_rate,
        d.total_jp_wins,
        d.total_freebet_wins,
        d.debit,
        d.credit
        FROM (
                SELECT payment_date_hour
                    ,brand_id
                    ,game_code
                    ,player_code
                    ,currency_code
                    --,SUM(played_games_qty) AS played_games_qty
                    ,SUM(total_bets) AS total_bets
                    ,SUM(total_wins) AS total_wins
                    ,MAX(last_payment_ts) AS last_payment_ts
                    ,SUM(total_jp_wins     ) AS total_jp_wins
                    ,SUM(total_freebet_wins) AS total_freebet_wins
                    ,SUM(debit) AS debit
                    ,SUM(credit) AS credit
                FROM   (
                        SELECT payment_date_hour
                                ,brand_id
                                ,game_code
                                ,player_code
                                ,currency_code
                                --,played_games_qty
                                ,total_bets
                                ,total_wins
                                ,last_payment_ts
                                ,total_jp_wins
                                ,total_freebet_wins
                                ,debit
                                ,credit
                        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh
                        UNION ALL
                        SELECT payment_date_hour
                                ,brand_id
                                ,game_code
                                ,player_code
                                ,currency_code
                                --,played_games_qty
                                ,total_bets
                                ,total_wins
                                ,last_payment_ts
                                ,total_jp_wins
                                ,total_freebet_wins
                                ,debit
                                ,credit
                        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted
                        ) uni_on
                GROUP BY payment_date_hour, brand_id, game_code, currency_code, player_code
            ) d;

        SELECT MAX(max_inserted_at), COUNT(*)
        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh
        INTO   v_new_inserted_at, v_counter;

        DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh;
        DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted;

        -- GET DIAGNOSTICS v_counter = ROW_COUNT;
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

            UPDATE bo_aggr_config SET
                conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
            WHERE  aggr_job_name = 'bo_aggr_win_bets'
            AND  conf_key = 'lock_record';

        -- Log
        INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
            VALUES ('bo_aggr_win_bets', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, v_new_inserted_at, v_force_end_time);


        /* ~~~ bo_aggr_win_bets_by_brand ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        v_job_start_time := clock_timestamp();
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets_by_brand" started'; RETURN NEXT;

        SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_win_bets_by_brand' AND conf_key = 'lock_record' FOR UPDATE;

        IF NOT FOUND THEN
            RAISE EXCEPTION 'Table "bo_aggr_win_bets_by_brand" has no valid record for "bo_aggr_win_bets_by_brand/lock_record" pair';
        END IF;

        /* To limit huge load intervals */
        v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                                ,current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)
                                ,v_last_inserted_at + v_huge_interval
                                );


        WITH fresh_data as
        ( SELECT b.brand_id,
                b.currency as currency_code,
                 COALESCE(SUM(case when transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN bet_rollback = FALSE THEN (CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) ELSE (-1)*(CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) END) end),0) as bet,
                 COALESCE(SUM(case when transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) end),0) as win,
                 COALESCE(SUM(case when transaction_type = 'jackpot' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end),0) as jackpot_win,
                 COALESCE(SUM(case when transaction_type = 'free_bet' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end),0) as free_bet_win,
                COUNT(*) as events_count,
                MAX(b.payment_date) as last_payment_ts,
                MAX(COALESCE(inserted_at, payment_date)) AS max_inserted_at
            FROM wallet_win_bet b
            WHERE COALESCE(inserted_at, payment_date) > v_last_inserted_at
                AND COALESCE(inserted_at, payment_date) < v_force_end_time
                AND coalesce(b.is_test, false) = false
                AND b.game_code IS NOT NULL
            GROUP BY b.brand_id, b.currency
        ),
        upsert_aggr AS
        ( INSERT INTO bo_aggr_win_bets_by_brand (brand_id, currency_code, bet, win, revenue, jackpot_win, free_bet_win)
                SELECT brand_id, currency_code, bet, win, (bet - win) as revenue, jackpot_win, free_bet_win
                FROM fresh_data
        ON CONFLICT ON CONSTRAINT bo_aggr_win_bets_by_brand_pkey DO
                UPDATE SET bet = bo_aggr_win_bets_by_brand.bet + EXCLUDED.bet,
                        win = bo_aggr_win_bets_by_brand.win + EXCLUDED.win,
                        revenue = bo_aggr_win_bets_by_brand.revenue + EXCLUDED.revenue,
                        jackpot_win = bo_aggr_win_bets_by_brand.jackpot_win + EXCLUDED.jackpot_win,
                        free_bet_win = bo_aggr_win_bets_by_brand.free_bet_win + EXCLUDED.free_bet_win
        )
        SELECT MAX(max_inserted_at), SUM(events_count)
        FROM fresh_data
        INTO v_new_inserted_at, v_counter;


        -- GET DIAGNOSTICS v_counter = ROW_COUNT;
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets_by_brand" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

            UPDATE bo_aggr_config SET
                conf_value = coalesce(v_new_inserted_at, v_force_end_time - '1 msec'::INTERVAL)
            WHERE  aggr_job_name = 'bo_aggr_win_bets_by_brand'
            AND  conf_key = 'lock_record';

        -- Log
        INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
            VALUES ('bo_aggr_win_bets_by_brand', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, v_new_inserted_at, v_force_end_time);

        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job finished'; RETURN NEXT;

        /* ~~~ Maintenance ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        -- Clear old logs
        DELETE FROM bo_aggr_history WHERE started_at < (current_date - Interval '1 MONTH');

        -- Reset config parameters
        reset work_mem;

        RETURN;
    END;
$function$
;

--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_aggr_refresh_jobs(TIMESTAMP, VARCHAR);
--rollback ALTER FUNCTION fnc_bo_aggr_refresh_jobs_before_5_12 (TIMESTAMP, VARCHAR) RENAME TO fnc_bo_aggr_refresh_jobs;
--rollback RESET search_path;