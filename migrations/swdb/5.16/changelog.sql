--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset vladimir.minakov:2023-09-03-SWS-42099
--comment [JPN] Add new column 'restricted_ip_countries' to players_info
SET search_path = swmanagement;

ALTER TABLE players_info ADD COLUMN restricted_ip_countries JSONB;
COMMENT ON COLUMN players_info.restricted_ip_countries IS 'Blocked countries by player IP';

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE players_info DROP COLUMN restricted_ip_countries;
--rollback RESET search_path;


--changeset dmitriy.palaznik:2023-10-18-SWS-42830
--comment [<PERSON><PERSON>] Add new column 'info' to jp_win_log, remote_jp_win_log
SET search_path = swjackpot;

ALTER TABLE jp_win_log ADD COLUMN info JSONB;
COMMENT ON COLUMN jp_win_log.info IS 'Jackpot Win additional info';

ALTER TABLE remote_jp_win_log ADD COLUMN info JSONB;
COMMENT ON COLUMN remote_jp_win_log.info IS 'Remote Jackpot Win additional info';

RESET search_path;

--rollback SET search_path = swjackpot;
--rollback ALTER TABLE jp_win_log DROP COLUMN info;
--rollback ALTER TABLE remote_jp_win_log DROP COLUMN info;
--rollback RESET search_path;

