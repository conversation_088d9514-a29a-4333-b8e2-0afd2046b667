--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset emanuel-alin.raileanu:2024-06-20-SWS-41929-add-resolve-round-permissions
--comment Add permissions for round resolve endpoint
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'entity:gameclose:resolve' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["entity:gameclose:resolve"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'entity:gameclose:resolve' WHERE id = 1;
--rollback RESET search_path;
