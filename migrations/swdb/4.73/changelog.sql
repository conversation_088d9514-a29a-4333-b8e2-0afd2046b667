--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset pavel.shamshurov:2021-11-04-SWS-29875-add-nickname-change-attempts
--comment Update table to add column to store attempts a player made to change his/her nickname
SET search_path = swmanagement;

ALTER TABLE players_info ADD COLUMN nickname_change_attempts INTEGER NOT NULL DEFAULT 0;
COMMENT ON COLUMN players_info.nickname_change_attempts IS 'Number of attempts a player makes to change his/her nickname';

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE players_info DROP COLUMN nickname_change_attempts;


--changeset sergey.malkov:2021-11-04_SWS-30984_Converting_the_hidden_field_to_status runInTransaction:false
--comment Update enum_entity_games_status
SET search_path = swmanagement;
ALTER TYPE enum_entity_games_status ADD VALUE IF NOT EXISTS 'hidden';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM pg_enum where enumlabel = 'hidden' and enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_entity_games_status');
--rollback RESET search_path;


--changeset sergey.malkov:2021-11-04_SWS-30984_Converting_the_hidden_field_to_status_migration
--comment Data migration from column hidden to status 'hidden'
SET search_path = swmanagement;
UPDATE entity_games SET status = 'hidden', hidden = NULL WHERE hidden = TRUE;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE entity_games SET status = 'normal', hidden = TRUE WHERE status = 'hidden';
--rollback RESET search_path;

--changeset dmitriy.palaznik:2021-11-11_SWS-30683_flat_report_add_game_group_column
--comment Add game_group column to flat_reports table and allow to set NULL for game_code
SET search_path = swmanagement;
ALTER TABLE flat_reports ADD COLUMN game_group character varying(255);
COMMENT ON COLUMN flat_reports.game_group IS 'Game group which assosiated with a report';
ALTER TABLE flat_reports ALTER COLUMN game_code DROP NOT NULL;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE flat_reports DROP COLUMN game_group;
--rollback ALTER TABLE flat_reports ALTER COLUMN game_code SET NOT NULL;
--rollback RESET search_path;

--changeset oleg.rudko:2021-11-19-SWS-29718-create-new-permission-for-add-live-games
--comment Add new permissions
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'entity:game:add-live-game' where id = 1;
UPDATE roles SET permissions = permissions - 'entity:game:remove-live-game' where id = 1;
UPDATE roles SET permissions = permissions || '["entity:game:remove-live-game", "entity:game:add-live-game"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'entity:game:add-live-game' where id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entity:game:remove-live-game' where id = 1;
--rollback RESET search_path;