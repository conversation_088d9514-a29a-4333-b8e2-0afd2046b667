--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset anastasia.kostyukova:2020-05-05-SWS-XXXX-start-release-4.38.0
--comment label for 4.38.0
SELECT now();
--rollback select now();


--changeset anastasia.kostyukova:2020-05-05-SWS-17711-limit-template
--comment Add limit template table and permissions
SET search_path TO swmanagement;

CREATE SEQUENCE limit_templates_id_seq;
CREATE TABLE IF NOT EXISTS limit_templates (
	id integer NOT NULL DEFAULT nextval('limit_templates_id_seq') PRIMARY KEY,
	name character varying(255) NOT NULL,
	template jsonb NOT NULL,
	created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL,
  UNIQUE (name)
);
COMMENT ON TABLE limit_templates IS 'Templates for game limits';
COMMENT ON COLUMN limit_templates.template IS 'JSON with limits, can be used by entity on limits creation';

--comment Add permissions to manage limit templates
UPDATE ROLES SET permissions = permissions - 'limittemplate' WHERE id = 1;
UPDATE ROLES SET permissions = permissions - 'limittemplate:view' WHERE id = 1;
UPDATE ROLES SET permissions = permissions - 'limittemplate:edit' WHERE id = 1;
UPDATE ROLES SET permissions = permissions - 'limittemplate:remove' WHERE id = 1;
UPDATE ROLES SET permissions = permissions || '["limittemplate", "limittemplate:view", "limittemplate:edit", "limittemplate:remove"]'::jsonb WHERE id = 1;

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP TABLE limit_templates;
--rollback DROP SEQUENCE limit_templates_id_seq;
--rollback UPDATE roles SET permissions = permissions - 'limittemplate' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'limittemplate:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'limittemplate:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'limittemplate:remove' WHERE id = 1;
--rollback RESET search_path;


--changeset anastasia.kostyukova:2020-05-08-SWS-18154-game-group-rename
--comment Add permission to rename game group
SET search_path TO swmanagement;

UPDATE ROLES SET permissions = permissions - 'keyentity:gamegroup:rename' WHERE id = 1;
UPDATE ROLES SET permissions = permissions - 'gamegroup:rename' WHERE id = 1;
UPDATE ROLES SET permissions = permissions || '["keyentity:gamegroup:rename", "gamegroup:rename"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE ROLES SET permissions = permissions - 'keyentity:gamegroup:rename' WHERE id = 1;
--rollback UPDATE ROLES SET permissions = permissions - 'gamegroup:rename' WHERE id = 1;
--rollback RESET search_path;

--changeset valdis.akmens:2020-05-18-SWDB-139-DB-refactoring-data-types-and-columns
--comment Add missed columns for archive table and correct timetampz to timestamp for some tables
SET search_path TO swmanagement;
ALTER TABLE available_sites ALTER COLUMN inserted_at SET DATA TYPE TIMESTAMP WITHOUT TIME ZONE;
ALTER TABLE labels ALTER COLUMN created_at SET DATA TYPE TIMESTAMP WITHOUT TIME ZONE;
ALTER TABLE labels ALTER COLUMN updated_at SET DATA TYPE TIMESTAMP WITHOUT TIME ZONE;
ALTER TABLE notifications ALTER COLUMN ts SET DATA TYPE TIMESTAMP WITHOUT TIME ZONE;
ALTER TABLE notifications ALTER COLUMN lifetime SET DATA TYPE TIMESTAMP WITHOUT TIME ZONE;
ALTER TABLE site_tokens ALTER COLUMN ts SET DATA TYPE TIMESTAMP WITHOUT TIME ZONE;
ALTER TABLE terminals ALTER COLUMN created_at SET DATA TYPE TIMESTAMP WITHOUT TIME ZONE;
ALTER TABLE terminals ALTER COLUMN updated_at SET DATA TYPE TIMESTAMP WITHOUT TIME ZONE;
RESET search_path;

SET search_path TO swgameserver;
ALTER TABLE archived_game_contexts ADD COLUMN IF NOT EXISTS is_broken boolean NULL DEFAULT false;
ALTER TABLE archived_game_contexts ALTER is_broken DROP DEFAULT;
ALTER TABLE archived_game_contexts ALTER is_broken DROP NOT NULL;
COMMENT ON COLUMN archived_game_contexts.is_broken IS 'Indicates if game is broken';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE available_sites ALTER COLUMN inserted_at SET DATA TYPE TIMESTAMP WITH TIME ZONE;
--rollback ALTER TABLE labels ALTER COLUMN created_at SET DATA TYPE TIMESTAMP WITH TIME ZONE;
--rollback ALTER TABLE labels ALTER COLUMN updated_at SET DATA TYPE TIMESTAMP WITH TIME ZONE;
--rollback ALTER TABLE notifications ALTER COLUMN ts SET DATA TYPE TIMESTAMP WITH TIME ZONE;
--rollback ALTER TABLE notifications ALTER COLUMN lifetime SET DATA TYPE TIMESTAMP WITH TIME ZONE;
--rollback ALTER TABLE site_tokens ALTER COLUMN ts SET DATA TYPE TIMESTAMP WITH TIME ZONE;
--rollback ALTER TABLE terminals ALTER COLUMN created_at SET DATA TYPE TIMESTAMP WITH TIME ZONE;
--rollback ALTER TABLE terminals ALTER COLUMN updated_at SET DATA TYPE TIMESTAMP WITH TIME ZONE;
--rollback RESET search_path;
--rollback SET search_path TO swgameserver;
--rollback ALTER TABLE archived_game_contexts DROP COLUMN IF EXISTS is_broken;
--rollback RESET search_path;

--changeset aleksey.ignatenko:2020-05-21-SWS-15770_correct_ts_col_type_in_game_contexts
--comment Correct temporal data type from timetampz to timestamp of the proper cols in the table swgameserver.game_contexts
--comment !!! Against Production DBs instead of it run specific PL/pgSQL blocks and function to modify data type concurrently
SET search_path TO swgameserver;
ALTER TABLE game_contexts
    ALTER created_at SET DATA TYPE timestamp USING (created_at AT TIME ZONE 'UTC'), 
    ALTER updated_at SET DATA TYPE timestamp USING (updated_at AT TIME ZONE 'UTC');
RESET search_path;

--rollback SET search_path TO swgameserver;
--rollback ALTER TABLE game_contexts ALTER created_at SET DATA TYPE timestamptz;
--rollback ALTER TABLE game_contexts ALTER updated_at SET DATA TYPE timestamptz;
--rollback RESET search_path;

--changeset aleksey.ignatenko:2020-05-22-SWS-13818_add_audit_types_dict_table
--comment Create dictionary table and store audit event types in it
SET search_path = swmanagement;

CREATE TABLE audit_types
(
    event_id    smallint        NOT NULL,
    event_code  varchar(50)     NOT NULL    CHECK (event_code = UPPER (event_code)),
    description varchar(250)    NULL, 
    created_at  timestamp       NOT NULL    DEFAULT (now() AT TIME ZONE 'UTC'),
    updated_at  timestamp       NULL,
    vers_from   numeric(7, 2)   NULL,
    CONSTRAINT audit_types_pk PRIMARY KEY (event_id),
    CONSTRAINT audit_types_uk UNIQUE (event_code)
);

COMMENT ON TABLE audit_types IS 'Event types that should be logged in "audits" table. Each event logged as id';
COMMENT ON COLUMN audit_types.event_id IS 'Identifier';
COMMENT ON COLUMN audit_types.event_code IS 'Event Code';
COMMENT ON COLUMN audit_types.description IS 'Comment on event';
COMMENT ON COLUMN audit_types.created_at IS 'Addition time of event';
COMMENT ON COLUMN audit_types.updated_at IS 'Modification time of event';
COMMENT ON COLUMN audit_types.vers_from IS 'App version when the event appeared';

ALTER TABLE audit_types OWNER TO swmanagement;

INSERT INTO audit_types (event_id, event_code, vers_from)
VALUES  (1, 'LOGIN', 3.10)
    ,   (2, 'UNUSED', NULL)
    ,   (3, 'PLAYER_CHANGE', 3.10)
    ,   (4, 'PLAYER_CHANGE_STATUS', 3.10)
    ,   (5, 'ENTITY_SETTINGS_CHANGE', 3.10)
    ,   (6, 'ENTITY_SETTINGS_RESET', 3.10)
    ,   (7, 'ENTITY_LANGUAGE_ADD', 3.10)
    ,   (8, 'ENTITY_LANGUAGE_REMOVE', 3.10)
    ,   (9, 'ENTITY_CURRENCY_ADD', 3.10)
    ,   (10,'ENTITY_CURRENCY_REMOVE', 3.10)
    ,   (11,'ENTITY_COUNTRY_ADD', 3.10)
    ,   (12,'ENTITY_COUNTRY_REMOVE', 3.10)
    ,   (13,'ENTITY_CREDIT', 3.10)
    ,   (14,'ENTITY_DEBIT', 3.10)
    ,   (15,'ENTITY_CREATE', 3.10)
    ,   (16,'ENTITY_BRAND_CREATE', 3.10)
    ,   (17,'ENTITY_MERCHANT_CREATE', 3.10)
    ,   (18,'MERCHANT_TEST_PLAYER_REMOVE', NULL)
    ,   (19,'USER_CREATE', 3.10)
    ,   (20,'USER_REMOVE', 3.10)
    ,   (21,'FORCE_FINISH_ROUND', 3.10)
    ,   (22,'REVERT_ROUND', 3.10)
    ,   (23,'SESSION_KILL', 3.10)
    ,   (24,'ENTITY_GAME_SUSPEND', 3.10)
    ,   (25,'ENTITY_GAME_RESTORE', 3.10)
    ,   (26,'ENTITY_GAME_BULK_UPDATE_STATUS', 3.10)
    ,   (27,'ENTITY_GAME_UPDATE', 3.10)
    ,   (28,'PLAYER_INIT_DEPOSIT', 3.10)
    ,   (29,'PLAYER_INIT_WITHDRAW', 3.10)
    ,   (30,'ENTITY_BULK_OPERATION', 3.10)
    ,   (31,'STATIC_DOMAIN_CREATE', 4.0)
    ,   (32,'STATIC_DOMAIN_UPDATE', 4.0)
    ,   (33,'STATIC_DOMAIN_REMOVE', 4.0)
    ,   (34,'DYNAMIC_DOMAIN_CREATE', 4.0)
    ,   (35,'DYNAMIC_DOMAIN_UPDATE', 4.0)
    ,   (36,'DYNAMIC_DOMAIN_REMOVE', 4.0)
    ,   (37,'STATIC_ENTITY_DOMAIN_SET', 4.0)
    ,   (38,'STATIC_ENTITY_DOMAIN_RESET', 4.0)
    ,   (39,'DYNAMIC_ENTITY_DOMAIN_SET', 4.0)
    ,   (40,'DYNAMIC_ENTITY_DOMAIN_RESET', 4.0)
    ,   (41,'SET_SECOND_STEP_AUTH_TYPE', NULL)
    ,   (42,'SECOND_STEP_LOGIN', NULL)
    ,   (43,'PLAYER_BULK_OPERATION', NULL)
    ,   (44,'ENTITY_GAME_BULK_UPDATE_LIMITS', NULL)
    ,   (45,'START_FINALIZE_ROUND', NULL)
    ,   (46,'FINISH_FINALIZE_ROUND', NULL)
    ,   (47,'ENTITY_STATIC_DOMAIN_TAGS_SET', NULL)
    ,   (48,'ENTITY_STATIC_DOMAIN_TAGS_RESET', NULL)
    ,   (49,'GAME_LIMITS_CONFIG_CREATE', NULL)
    ,   (50,'GAME_LIMITS_CONFIG_UPDATE', NULL)
    ,   (51,'GAME_LIMITS_CONFIG_REMOVE', NULL)
    ,   (52,'CURRENCY_MULTIPLIER_SET', NULL)
;
    
RESET search_path;

--rollback SET search_path TO swmanagement;
--rollback DROP TABLE audit_types;
--rollback RESET search_path;

--changeset aleksey.ignatenko:2020-06-09-SWS-19093_fix_limit_templates
--comment Changed owner for table and sequence of limit templates (fix changeset: 2020-05-05-SWS-17711-limit-template)
SET search_path = swmanagement;

ALTER TABLE limit_templates OWNER TO swmanagement;
ALTER SEQUENCE limit_templates_id_seq OWNER TO swmanagement;

RESET search_path;

--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE limit_templates OWNER TO swsystem;
--rollback ALTER SEQUENCE limit_templates_id_seq OWNER TO swsystem;
--rollback RESET search_path;

--changeset evgeniy.gandzyuck:2020-06-09-SWS-18755
--comment Add grouping key for BI reports
SET search_path = swmanagement;
ALTER TABLE bi_reports ADD IF NOT EXISTS report_group varchar(255) NULL;
UPDATE bi_reports SET report_group = 'Performance' WHERE name IN ('Games', 'Players', 'Currency', 'Jackpots', 'Summary', 'Summary Real Time', 'Summary Real Time Advanced', 'Live Game Providers');
UPDATE bi_reports SET report_group = 'Engagement Tools' WHERE name IN ('Bonus Coins', 'Bonus Coins', 'GRC');
UPDATE bi_reports SET report_group = 'Insights' WHERE name IN ('Bets Distribution', 'Leaving Players');
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE bi_reports DROP COLUMN report_group;
--rollback RESET search_path;

--changeset evgeniy.gandzyuck:2020-06-11-SWS-19033
--comment Create new permission for the 'Must Win Jackpot Report' BI report
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'bi:report:jackpot:mustwin' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:jackpot:mustwin' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["bi:report:jackpot:mustwin", "keyentity:bi:report:jackpot:mustwin"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:jackpot:mustwin' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:jackpot:mustwin' WHERE id = 1;
--rollback RESET search_path;