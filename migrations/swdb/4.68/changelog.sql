--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset emanuel-alin.railean:2021-09-10-BANGINT-124-bangbet_gameIds
--comment create bangbet_games table, insert data for mapping game ids to game codes
--validCheckSum: 7:62139a98884ec366663173fc069b2152
SET search_path = swmanagement;
CREATE TABLE IF NOT EXISTS bangbet_games (
    id BIGINT,
    game_code VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now(),
    PRIMARY KEY(id)
);
COMMENT ON TABLE bangbet_games IS 'Game ids mapped to sw game codes';
COMMENT ON COLUMN bangbet_games.id IS 'BangBet game id unique for each country';
COMMENT ON COLUMN bangbet_games.game_code IS 'SW game code - can have duplicates';

DELETE FROM bangbet_games;

INSERT INTO bangbet_games (id, game_code)
VALUES (5193, 'sw_ro_a01bac'),
       (5201, 'sw_ro_a04bac'),
       (5205, 'sw_ro_a04bac_nc'),
       (5209, 'sw_ro_a07bac'),
       (5213, 'sw_ro_a07bac_nc'),
       (5217, 'sw_ro_a06bac'),
       (5221, 'sw_ro_a06bac_nc'),
       (5225, 'sw_ro_a05bac'),
       (5229, 'sw_ro_a05bac_nc'),
       (5237, 'sw_ro_a02bac_nc'),
       (5241, 'sw_ro_a01ro'),
       (5245, 'sw_ro_a03ro'),
       (5197, 'sw_ro_a01bac_nc'),
       (5233, 'sw_ro_a02bac'),
       (5194, 'sw_ro_a01bac'),
       (5202, 'sw_ro_a04bac'),
       (5206, 'sw_ro_a04bac_nc'),
       (5210, 'sw_ro_a07bac'),
       (5214, 'sw_ro_a07bac_nc'),
       (5218, 'sw_ro_a06bac'),
       (5222, 'sw_ro_a06bac_nc'),
       (5226, 'sw_ro_a05bac'),
       (5230, 'sw_ro_a05bac_nc'),
       (5238, 'sw_ro_a02bac_nc'),
       (5242, 'sw_ro_a01ro'),
       (5246, 'sw_ro_a03ro'),
       (5198, 'sw_ro_a01bac_nc'),
       (5234, 'sw_ro_a02bac'),
       (5195, 'sw_ro_a01bac'),
       (5199, 'sw_ro_a01bac_nc'),
       (5203, 'sw_ro_a04bac'),
       (5207, 'sw_ro_a04bac_nc'),
       (5211, 'sw_ro_a07bac'),
       (5215, 'sw_ro_a07bac_nc'),
       (5219, 'sw_ro_a06bac'),
       (5223, 'sw_ro_a06bac_nc'),
       (5227, 'sw_ro_a05bac'),
       (5231, 'sw_ro_a05bac_nc'),
       (5239, 'sw_ro_a02bac_nc'),
       (5243, 'sw_ro_a01ro'),
       (5247, 'sw_ro_a03ro'),
       (5235, 'sw_ro_a02bac'),
       (5196, 'sw_ro_a01bac'),
       (5200, 'sw_ro_a01bac_nc'),
       (5204, 'sw_ro_a04bac'),
       (5208, 'sw_ro_a04bac_nc'),
       (5212, 'sw_ro_a07bac'),
       (5216, 'sw_ro_a07bac_nc'),
       (5220, 'sw_ro_a06bac'),
       (5224, 'sw_ro_a06bac_nc'),
       (5228, 'sw_ro_a05bac'),
       (5232, 'sw_ro_a05bac_nc'),
       (5236, 'sw_ro_a02bac'),
       (5240, 'sw_ro_a02bac_nc'),
       (5244, 'sw_ro_a01ro'),
       (5248, 'sw_ro_a03ro'),
       (4201, 'sw_tcb'),
       (4205, 'sw_88sf'),
       (4209, 'sw_888t'),
       (4213, 'sw_9s1k'),
       (4217, 'sw_al'),
       (4221, 'sw_af'),
       (4225, 'sw_bzxt'),
       (4229, 'sw_bl'),
       (4233, 'sw_bjc'),
       (4237, 'sw_bd'),
       (4241, 'sw_bosl'),
       (4245, 'sw_bul'),
       (4249, 'sw_bm'),
       (4253, 'sw_btrb'),
       (4257, 'sw_cscf'),
       (4261, 'sw_csy'),
       (4265, 'sw_ch8'),
       (4269, 'sw_cf'),
       (4273, 'sw_cmw'),
       (4277, 'sw_cts'),
       (4281, 'sw_dhcf'),
       (4285, 'sw_dld'),
       (4289, 'sw_dmzc'),
       (4293, 'sw_dtc'),
       (4297, 'sw_dd'),
       (4301, 'sw_db'),
       (4305, 'sw_dj'),
       (4309, 'sw_ewb'),
       (4313, 'sw_ec'),
       (4317, 'sw_er'),
       (4321, 'sw_fkmj'),
       (4325, 'sw_fbbls'),
       (4329, 'sw_fish_prawn_crab'),
       (4333, 'sw_fp'),
       (4337, 'sw_fcase'),
       (4341, 'sw_fc'),
       (4345, 'sw_fl'),
       (4349, 'sw_fg'),
       (4353, 'sw_fbb'),
       (4357, 'sw_fufarm'),
       (4361, 'sw_fufish_intw'),
       (4365, 'sw_fuqsg'),
       (4369, 'sw_fsqt'),
       (4373, 'sw_fzyq'),
       (4377, 'sw_gk'),
       (4381, 'sw_gq'),
       (4389, 'sw_gtg'),
       (4393, 'sw_gg'),
       (4397, 'sw_gol'),
       (4401, 'sw_go8d'),
       (4405, 'sw_ggdn'),
       (4409, 'sw_gm'),
       (4413, 'sw_gr'),
       (4417, 'sw_hcs'),
       (4421, 'sw_h2h'),
       (4425, 'sw_hd'),
       (4429, 'sw_hp'),
       (4433, 'sw_hr'),
       (4437, 'sw_jxl'),
       (4441, 'sw_jjbx'),
       (4445, 'sw_jqw'),
       (4449, 'sw_kog'),
       (4453, 'sw_ksm'),
       (4385, 'sw_gatc'),
       (4457, 'sw_kiwi'),
       (4461, 'sw_ks'),
       (4465, 'sw_kxcs'),
       (4469, 'sw_lodk'),
       (4473, 'sw_lohy'),
       (4477, 'sw_ld'),
       (4481, 'sw_lll'),
       (4485, 'sw_lcc'),
       (4489, 'sw_moo'),
       (4493, 'sw_mf'),
       (4497, 'sw_ms'),
       (4501, 'sw_mm'),
       (4505, 'sw_mwol'),
       (4509, 'sw_mer'),
       (4513, 'sw_mj'),
       (4517, 'sw_mt'),
       (4521, 'sw_mc'),
       (4525, 'sw_mpp'),
       (4529, 'sw_mp'),
       (4533, 'sw_mrmnky'),
       (4537, 'sw_nyf'),
       (4541, 'sw_nyg'),
       (4545, 'sw_pc'),
       (4549, 'sw_pg'),
       (4553, 'sw_pp'),
       (4557, 'sw_pvg'),
       (4561, 'sw_pe'),
       (4565, 'sw_pt'),
       (4569, 'sw_qotp'),
       (4573, 'sw_qv'),
       (4577, 'sw_qow'),
       (4581, 'sw_qoiaf'),
       (4585, 'sw_rf'),
       (4589, 'sw_rm'),
       (4593, 'sw_rsyg'),
       (4597, 'sw_rs'),
       (4601, 'sw_rc'),
       (4605, 'sw_rcr'),
       (4609, 'sw_sf'),
       (4613, 'sw_sc'),
       (4617, 'sw_sog'),
       (4621, 'sw_slws'),
       (4625, 'sw_slbs'),
       (4629, 'sw_fd'),
       (4633, 'sw_scyd'),
       (4637, 'sw_sx'),
       (4641, 'sw_sgcf'),
       (4645, 'sw_sl'),
       (4649, 'sw_fb'),
       (4653, 'sw_sixng'),
       (4657, 'sw_sq'),
       (4661, 'sw_sctz'),
       (4665, 'sw_sld'),
       (4669, 'sw_sod'),
       (4673, 'sw_tr'),
       (4677, 'sw_t2d'),
       (4681, 'sw_tlotws'),
       (4685, 'sw_totiatp'),
       (4689, 'sw_rmac'),
       (4693, 'sw_sfy'),
       (4697, 'sw_ts'),
       (4701, 'sw_tc'),
       (4705, 'sw_tm'),
       (4709, 'sw_vos'),
       (4713, 'sw_vi'),
       (4717, 'sw_wf'),
       (4721, 'sw_wfot'),
       (4725, 'sw_wq'),
       (4729, 'sw_wws'),
       (4733, 'sw_xwk'),
       (4737, 'sw_xyjc'),
       (4741, 'sw_xw'),
       (4745, 'sw_ycs'),
       (4749, 'sw_shctz'),
       (4753, 'sw_zcxm'),
       (4757, 'sw_8tr1qu'),
       (4761, 'sw_loofthsp'),
       (4765, 'sw_sdjg'),
       (4769, 'sw_ylxn'),
       (4773, 'sw_wg'),
       (4777, 'sw_hlcs'),
       (4781, 'sw_xybl'),
       (4785, 'sw_or'),
       (4789, 'sw_lomutabangno'),
       (4793, 'sw_ex'),
       (4797, 'sw_twfr'),
       (4801, 'sw_pote'),
       (4805, 'sw_jogowi'),
       (4809, 'sw_bac'),
       (4813, 'sw_es'),
       (4817, 'sw_ylns'),
       (4821, 'sw_bb'),
       (4825, 'sw_hg'),
       (4829, 'sw_luckyfim'),
       (4833, 'sw_chwi'),
       (4837, 'sw_ggrizzly'),
       (4841, 'sw_yyy'),
       (4845, 'sw_dc'),
       (4849, 'sw_fofefa'),
       (4853, 'sw_gt'),
       (4857, 'sw_wfl'),
       (4861, 'sw_remamere'),
       (4865, 'sw_zhhu'),
       (4869, 'sw_filifo'),
       (4873, 'sw_tiki_luck'),
       (4877, 'sw_nilazhnu'),
       (4881, 'sw_fj'),
       (4885, 'sw_azre'),
       (4889, 'sw_olcaymsh'),
       (4893, 'sw_sland'),
       (4897, 'sw_cashdaha'),
       (4901, 'sw_yxlb'),
       (4905, 'sw_dr'),
       (4909, 'sw_scca2d'),
       (4913, 'sw_wcup'),
       (4917, 'sw_chfi'),
       (4921, 'sw_le'),
       (4925, 'sw_ge1xas-te'),
       (4929, 'sw_jodobi'),
       (4933, 'sw_wi0'),
       (4937, 'sw_wifl'),
       (4941, 'sw_mopa'),
       (4945, 'sw_cashlalala'),
       (4949, 'sw_ra'),
       (4953, 'sw_coma'),
       (4957, 'sw_prli'),
       (4961, 'sw_cada'),
       (4965, 'sw_drgo'),
       (4969, 'sw_papo'),
       (4973, 'ig_giantmusselfishing'),
       (4977, 'ig_kingfishing'),
       (4981, 'ig_caishenfafafa'),
       (4985, 'sw_fobo'),
       (4989, 'sw_wifr'),
       (4993, 'sw_mybe'),
       (5101, 'sw_repo'),
       (5105, 'sw_lubiha'),
       (5109, 'sw_yolidoli'),
       (5113, 'sw_boofge'),
       (5117, 'sw_gejo'),
       (5121, 'sw_rupe'),
       (5125, 'sw_luch'),
       (5129, 'sw_bibume'),
       (5133, 'sw_bago'),
       (5137, 'sw_ssm'),
       (5145, 'sw_seofpe'),
       (5149, 'sw_alme'),
       (5153, 'sw_tpgsop'),
       (5157, 'sw_jolude'),
       (5161, 'sw_kk_original'),
       (5165, 'sw_csi'),
       (5169, 'sw_thmase'),
       (5173, 'sw_st_original'),
       (5177, 'sw_bloo'),
       (5181, 'sw_rambo'),
       (5185, 'sw_reev'),
       (5189, 'sw_lomathtt'),
       (5141, 'sw_jolu'),
       (4202, 'sw_tcb'),
       (4206, 'sw_88sf'),
       (4210, 'sw_888t'),
       (4214, 'sw_9s1k'),
       (4218, 'sw_al'),
       (4222, 'sw_af'),
       (4226, 'sw_bzxt'),
       (4230, 'sw_bl'),
       (4234, 'sw_bjc'),
       (4238, 'sw_bd'),
       (4242, 'sw_bosl'),
       (4246, 'sw_bul'),
       (4250, 'sw_bm'),
       (4254, 'sw_btrb'),
       (4258, 'sw_cscf'),
       (4262, 'sw_csy'),
       (4266, 'sw_ch8'),
       (4270, 'sw_cf'),
       (4274, 'sw_cmw'),
       (4278, 'sw_cts'),
       (4282, 'sw_dhcf'),
       (4286, 'sw_dld'),
       (4290, 'sw_dmzc'),
       (4294, 'sw_dtc'),
       (4298, 'sw_dd'),
       (4302, 'sw_db'),
       (4306, 'sw_dj'),
       (4310, 'sw_ewb'),
       (4314, 'sw_ec'),
       (4318, 'sw_er'),
       (4322, 'sw_fkmj'),
       (4326, 'sw_fbbls'),
       (4330, 'sw_fish_prawn_crab'),
       (4334, 'sw_fp'),
       (4338, 'sw_fcase'),
       (4342, 'sw_fc'),
       (4346, 'sw_fl'),
       (4350, 'sw_fg'),
       (4354, 'sw_fbb'),
       (4358, 'sw_fufarm'),
       (4362, 'sw_fufish_intw'),
       (4366, 'sw_fuqsg'),
       (4370, 'sw_fsqt'),
       (4374, 'sw_fzyq'),
       (4378, 'sw_gk'),
       (4382, 'sw_gq'),
       (4390, 'sw_gtg'),
       (4394, 'sw_gg'),
       (4398, 'sw_gol'),
       (4402, 'sw_go8d'),
       (4406, 'sw_ggdn'),
       (4410, 'sw_gm'),
       (4414, 'sw_gr'),
       (4418, 'sw_hcs'),
       (4422, 'sw_h2h'),
       (4426, 'sw_hd'),
       (4430, 'sw_hp'),
       (4434, 'sw_hr'),
       (4438, 'sw_jxl'),
       (4442, 'sw_jjbx'),
       (4446, 'sw_jqw'),
       (4450, 'sw_kog'),
       (4454, 'sw_ksm'),
       (4458, 'sw_kiwi'),
       (4462, 'sw_ks'),
       (4466, 'sw_kxcs'),
       (4470, 'sw_lodk'),
       (4474, 'sw_lohy'),
       (4478, 'sw_ld'),
       (4482, 'sw_lll'),
       (4486, 'sw_lcc'),
       (4490, 'sw_moo'),
       (4494, 'sw_mf'),
       (4498, 'sw_ms'),
       (4502, 'sw_mm'),
       (4506, 'sw_mwol'),
       (4510, 'sw_mer'),
       (4514, 'sw_mj'),
       (4518, 'sw_mt'),
       (4522, 'sw_mc'),
       (4526, 'sw_mpp'),
       (4530, 'sw_mp'),
       (4534, 'sw_mrmnky'),
       (4538, 'sw_nyf'),
       (4542, 'sw_nyg'),
       (4546, 'sw_pc'),
       (4550, 'sw_pg'),
       (4554, 'sw_pp'),
       (4558, 'sw_pvg'),
       (4562, 'sw_pe'),
       (4566, 'sw_pt'),
       (4570, 'sw_qotp'),
       (4574, 'sw_qv'),
       (4578, 'sw_qow'),
       (4582, 'sw_qoiaf'),
       (4586, 'sw_rf'),
       (4590, 'sw_rm'),
       (4594, 'sw_rsyg'),
       (4598, 'sw_rs'),
       (4602, 'sw_rc'),
       (4606, 'sw_rcr'),
       (4610, 'sw_sf'),
       (4614, 'sw_sc'),
       (4618, 'sw_sog'),
       (4622, 'sw_slws'),
       (4386, 'sw_gatc'),
       (4626, 'sw_slbs'),
       (4630, 'sw_fd'),
       (4634, 'sw_scyd'),
       (4638, 'sw_sx'),
       (4642, 'sw_sgcf'),
       (4646, 'sw_sl'),
       (4650, 'sw_fb'),
       (4654, 'sw_sixng'),
       (4658, 'sw_sq'),
       (4662, 'sw_sctz'),
       (4666, 'sw_sld'),
       (4670, 'sw_sod'),
       (4674, 'sw_tr'),
       (4678, 'sw_t2d'),
       (4682, 'sw_tlotws'),
       (4686, 'sw_totiatp'),
       (4690, 'sw_rmac'),
       (4694, 'sw_sfy'),
       (4698, 'sw_ts'),
       (4702, 'sw_tc'),
       (4706, 'sw_tm'),
       (4710, 'sw_vos'),
       (4714, 'sw_vi'),
       (4718, 'sw_wf'),
       (4722, 'sw_wfot'),
       (4726, 'sw_wq'),
       (4730, 'sw_wws'),
       (4734, 'sw_xwk'),
       (4738, 'sw_xyjc'),
       (4742, 'sw_xw'),
       (4746, 'sw_ycs'),
       (4750, 'sw_shctz'),
       (4754, 'sw_zcxm'),
       (4758, 'sw_8tr1qu'),
       (4762, 'sw_loofthsp'),
       (4766, 'sw_sdjg'),
       (4770, 'sw_ylxn'),
       (4774, 'sw_wg'),
       (4778, 'sw_hlcs'),
       (4782, 'sw_xybl'),
       (4786, 'sw_or'),
       (4790, 'sw_lomutabangno'),
       (4794, 'sw_ex'),
       (4798, 'sw_twfr'),
       (4802, 'sw_pote'),
       (4806, 'sw_jogowi'),
       (4810, 'sw_bac'),
       (4814, 'sw_es'),
       (4818, 'sw_ylns'),
       (4822, 'sw_bb'),
       (4826, 'sw_hg'),
       (4830, 'sw_luckyfim'),
       (4834, 'sw_chwi'),
       (4838, 'sw_ggrizzly'),
       (4842, 'sw_yyy'),
       (4846, 'sw_dc'),
       (4850, 'sw_fofefa'),
       (4854, 'sw_gt'),
       (4858, 'sw_wfl'),
       (4862, 'sw_remamere'),
       (4866, 'sw_zhhu'),
       (4870, 'sw_filifo'),
       (4874, 'sw_tiki_luck'),
       (4878, 'sw_nilazhnu'),
       (4882, 'sw_fj'),
       (4886, 'sw_azre'),
       (4890, 'sw_olcaymsh'),
       (4894, 'sw_sland'),
       (4898, 'sw_cashdaha'),
       (4902, 'sw_yxlb'),
       (4906, 'sw_dr'),
       (4910, 'sw_scca2d'),
       (4914, 'sw_wcup'),
       (4918, 'sw_chfi'),
       (4922, 'sw_le'),
       (4926, 'sw_ge1xas-te'),
       (4930, 'sw_jodobi'),
       (4934, 'sw_wi0'),
       (4938, 'sw_wifl'),
       (4942, 'sw_mopa'),
       (4946, 'sw_cashlalala'),
       (4950, 'sw_ra'),
       (4954, 'sw_coma'),
       (4958, 'sw_prli'),
       (4962, 'sw_cada'),
       (4966, 'sw_drgo'),
       (4970, 'sw_papo'),
       (4974, 'ig_giantmusselfishing'),
       (4978, 'ig_kingfishing'),
       (4982, 'ig_caishenfafafa'),
       (4986, 'sw_fobo'),
       (4990, 'sw_wifr'),
       (4994, 'sw_mybe'),
       (5102, 'sw_repo'),
       (5106, 'sw_lubiha'),
       (5110, 'sw_yolidoli'),
       (5114, 'sw_boofge'),
       (5118, 'sw_gejo'),
       (5122, 'sw_rupe'),
       (5126, 'sw_luch'),
       (5130, 'sw_bibume'),
       (5134, 'sw_bago'),
       (5138, 'sw_ssm'),
       (5142, 'sw_jolu'),
       (5146, 'sw_seofpe'),
       (5150, 'sw_alme'),
       (5154, 'sw_tpgsop'),
       (5158, 'sw_jolude'),
       (5162, 'sw_kk_original'),
       (5166, 'sw_csi'),
       (5170, 'sw_thmase'),
       (5174, 'sw_st_original'),
       (5178, 'sw_bloo'),
       (5182, 'sw_rambo'),
       (5186, 'sw_reev'),
       (5190, 'sw_lomathtt'),
       (4203, 'sw_tcb'),
       (4207, 'sw_88sf'),
       (4211, 'sw_888t'),
       (4215, 'sw_9s1k'),
       (4219, 'sw_al'),
       (4223, 'sw_af'),
       (4227, 'sw_bzxt'),
       (4231, 'sw_bl'),
       (4235, 'sw_bjc'),
       (4239, 'sw_bd'),
       (4243, 'sw_bosl'),
       (4247, 'sw_bul'),
       (4251, 'sw_bm'),
       (4255, 'sw_btrb'),
       (4259, 'sw_cscf'),
       (4263, 'sw_csy'),
       (4267, 'sw_ch8'),
       (4271, 'sw_cf'),
       (4275, 'sw_cmw'),
       (4279, 'sw_cts'),
       (4283, 'sw_dhcf'),
       (4287, 'sw_dld'),
       (4291, 'sw_dmzc'),
       (4295, 'sw_dtc'),
       (4299, 'sw_dd'),
       (4303, 'sw_db'),
       (4307, 'sw_dj'),
       (4311, 'sw_ewb'),
       (4315, 'sw_ec'),
       (4319, 'sw_er'),
       (4323, 'sw_fkmj'),
       (4327, 'sw_fbbls'),
       (4331, 'sw_fish_prawn_crab'),
       (4335, 'sw_fp'),
       (4339, 'sw_fcase'),
       (4343, 'sw_fc'),
       (4347, 'sw_fl'),
       (4351, 'sw_fg'),
       (4355, 'sw_fbb'),
       (4359, 'sw_fufarm'),
       (4363, 'sw_fufish_intw'),
       (4367, 'sw_fuqsg'),
       (4371, 'sw_fsqt'),
       (4375, 'sw_fzyq'),
       (4379, 'sw_gk'),
       (4383, 'sw_gq'),
       (4387, 'sw_gatc'),
       (4391, 'sw_gtg'),
       (4395, 'sw_gg'),
       (4399, 'sw_gol'),
       (4403, 'sw_go8d'),
       (4407, 'sw_ggdn'),
       (4411, 'sw_gm'),
       (4415, 'sw_gr'),
       (4419, 'sw_hcs'),
       (4423, 'sw_h2h'),
       (4427, 'sw_hd'),
       (4431, 'sw_hp'),
       (4435, 'sw_hr'),
       (4439, 'sw_jxl'),
       (4443, 'sw_jjbx'),
       (4447, 'sw_jqw'),
       (4451, 'sw_kog'),
       (4455, 'sw_ksm'),
       (4459, 'sw_kiwi'),
       (4463, 'sw_ks'),
       (4467, 'sw_kxcs'),
       (4471, 'sw_lodk'),
       (4475, 'sw_lohy'),
       (4479, 'sw_ld'),
       (4483, 'sw_lll'),
       (4487, 'sw_lcc'),
       (4491, 'sw_moo'),
       (4495, 'sw_mf'),
       (4499, 'sw_ms'),
       (4503, 'sw_mm'),
       (4507, 'sw_mwol'),
       (4511, 'sw_mer'),
       (4515, 'sw_mj'),
       (4519, 'sw_mt'),
       (4523, 'sw_mc'),
       (4527, 'sw_mpp'),
       (4531, 'sw_mp'),
       (4535, 'sw_mrmnky'),
       (4539, 'sw_nyf'),
       (4543, 'sw_nyg'),
       (4547, 'sw_pc'),
       (4551, 'sw_pg'),
       (4555, 'sw_pp'),
       (4559, 'sw_pvg'),
       (4563, 'sw_pe'),
       (4567, 'sw_pt'),
       (4571, 'sw_qotp'),
       (4575, 'sw_qv'),
       (4579, 'sw_qow'),
       (4583, 'sw_qoiaf'),
       (4587, 'sw_rf'),
       (4591, 'sw_rm'),
       (4595, 'sw_rsyg'),
       (4599, 'sw_rs'),
       (4603, 'sw_rc'),
       (4607, 'sw_rcr'),
       (4611, 'sw_sf'),
       (4615, 'sw_sc'),
       (4619, 'sw_sog'),
       (4623, 'sw_slws'),
       (4627, 'sw_slbs'),
       (4631, 'sw_fd'),
       (4635, 'sw_scyd'),
       (4639, 'sw_sx'),
       (4643, 'sw_sgcf'),
       (4647, 'sw_sl'),
       (4651, 'sw_fb'),
       (4655, 'sw_sixng'),
       (4659, 'sw_sq'),
       (4663, 'sw_sctz'),
       (4667, 'sw_sld'),
       (4671, 'sw_sod'),
       (4675, 'sw_tr'),
       (4679, 'sw_t2d'),
       (4683, 'sw_tlotws'),
       (4687, 'sw_totiatp'),
       (4691, 'sw_rmac'),
       (4695, 'sw_sfy'),
       (4699, 'sw_ts'),
       (4703, 'sw_tc'),
       (4707, 'sw_tm'),
       (4711, 'sw_vos'),
       (4715, 'sw_vi'),
       (4719, 'sw_wf'),
       (4723, 'sw_wfot'),
       (4727, 'sw_wq'),
       (4731, 'sw_wws'),
       (4735, 'sw_xwk'),
       (4739, 'sw_xyjc'),
       (4743, 'sw_xw'),
       (4747, 'sw_ycs'),
       (4751, 'sw_shctz'),
       (4755, 'sw_zcxm'),
       (4759, 'sw_8tr1qu'),
       (4763, 'sw_loofthsp'),
       (4767, 'sw_sdjg'),
       (4771, 'sw_ylxn'),
       (4775, 'sw_wg'),
       (4779, 'sw_hlcs'),
       (4783, 'sw_xybl'),
       (4787, 'sw_or'),
       (4791, 'sw_lomutabangno'),
       (4795, 'sw_ex'),
       (4799, 'sw_twfr'),
       (4803, 'sw_pote'),
       (4807, 'sw_jogowi'),
       (4811, 'sw_bac'),
       (4815, 'sw_es'),
       (4819, 'sw_ylns'),
       (4823, 'sw_bb'),
       (4827, 'sw_hg'),
       (4831, 'sw_luckyfim'),
       (4835, 'sw_chwi'),
       (4839, 'sw_ggrizzly'),
       (4843, 'sw_yyy'),
       (4847, 'sw_dc'),
       (4851, 'sw_fofefa'),
       (4855, 'sw_gt'),
       (4859, 'sw_wfl'),
       (4863, 'sw_remamere'),
       (4867, 'sw_zhhu'),
       (4871, 'sw_filifo'),
       (4875, 'sw_tiki_luck'),
       (4879, 'sw_nilazhnu'),
       (4883, 'sw_fj'),
       (4887, 'sw_azre'),
       (4891, 'sw_olcaymsh'),
       (4895, 'sw_sland'),
       (4899, 'sw_cashdaha'),
       (4903, 'sw_yxlb'),
       (4907, 'sw_dr'),
       (4911, 'sw_scca2d'),
       (4915, 'sw_wcup'),
       (4919, 'sw_chfi'),
       (4923, 'sw_le'),
       (4927, 'sw_ge1xas-te'),
       (4931, 'sw_jodobi'),
       (4935, 'sw_wi0'),
       (4939, 'sw_wifl'),
       (4943, 'sw_mopa'),
       (4947, 'sw_cashlalala'),
       (4951, 'sw_ra'),
       (4955, 'sw_coma'),
       (4959, 'sw_prli'),
       (4963, 'sw_cada'),
       (4967, 'sw_drgo'),
       (4971, 'sw_papo'),
       (4975, 'ig_giantmusselfishing'),
       (4979, 'ig_kingfishing'),
       (4983, 'ig_caishenfafafa'),
       (4987, 'sw_fobo'),
       (4991, 'sw_wifr'),
       (4995, 'sw_mybe'),
       (5103, 'sw_repo'),
       (5107, 'sw_lubiha'),
       (5111, 'sw_yolidoli'),
       (5115, 'sw_boofge'),
       (5119, 'sw_gejo'),
       (5123, 'sw_rupe'),
       (5127, 'sw_luch'),
       (5131, 'sw_bibume'),
       (5135, 'sw_bago'),
       (5139, 'sw_ssm'),
       (5143, 'sw_jolu'),
       (5147, 'sw_seofpe'),
       (5151, 'sw_alme'),
       (5155, 'sw_tpgsop'),
       (5159, 'sw_jolude'),
       (5163, 'sw_kk_original'),
       (5167, 'sw_csi'),
       (5171, 'sw_thmase'),
       (5175, 'sw_st_original'),
       (5179, 'sw_bloo'),
       (5183, 'sw_rambo'),
       (5187, 'sw_reev'),
       (5191, 'sw_lomathtt'),
       (4204, 'sw_tcb'),
       (4208, 'sw_88sf'),
       (4212, 'sw_888t'),
       (4216, 'sw_9s1k'),
       (4220, 'sw_al'),
       (4224, 'sw_af'),
       (4228, 'sw_bzxt'),
       (4232, 'sw_bl'),
       (4236, 'sw_bjc'),
       (4240, 'sw_bd'),
       (4244, 'sw_bosl'),
       (4248, 'sw_bul'),
       (4252, 'sw_bm'),
       (4256, 'sw_btrb'),
       (4260, 'sw_cscf'),
       (4264, 'sw_csy'),
       (4268, 'sw_ch8'),
       (4272, 'sw_cf'),
       (4276, 'sw_cmw'),
       (4280, 'sw_cts'),
       (4284, 'sw_dhcf'),
       (4288, 'sw_dld'),
       (4292, 'sw_dmzc'),
       (4296, 'sw_dtc'),
       (4300, 'sw_dd'),
       (4304, 'sw_db'),
       (4308, 'sw_dj'),
       (4312, 'sw_ewb'),
       (4316, 'sw_ec'),
       (4320, 'sw_er'),
       (4324, 'sw_fkmj'),
       (4328, 'sw_fbbls'),
       (4332, 'sw_fish_prawn_crab'),
       (4336, 'sw_fp'),
       (4340, 'sw_fcase'),
       (4344, 'sw_fc'),
       (4348, 'sw_fl'),
       (4352, 'sw_fg'),
       (4356, 'sw_fbb'),
       (4360, 'sw_fufarm'),
       (4364, 'sw_fufish_intw'),
       (4368, 'sw_fuqsg'),
       (4372, 'sw_fsqt'),
       (4376, 'sw_fzyq'),
       (4380, 'sw_gk'),
       (4384, 'sw_gq'),
       (4388, 'sw_gatc'),
       (4392, 'sw_gtg'),
       (4396, 'sw_gg'),
       (4400, 'sw_gol'),
       (4404, 'sw_go8d'),
       (4408, 'sw_ggdn'),
       (4412, 'sw_gm'),
       (4416, 'sw_gr'),
       (4420, 'sw_hcs'),
       (4424, 'sw_h2h'),
       (4428, 'sw_hd'),
       (4432, 'sw_hp'),
       (4436, 'sw_hr'),
       (4440, 'sw_jxl'),
       (4444, 'sw_jjbx'),
       (4448, 'sw_jqw'),
       (4452, 'sw_kog'),
       (4456, 'sw_ksm'),
       (4460, 'sw_kiwi'),
       (4464, 'sw_ks'),
       (4468, 'sw_kxcs'),
       (4472, 'sw_lodk'),
       (4476, 'sw_lohy'),
       (4480, 'sw_ld'),
       (4484, 'sw_lll'),
       (4488, 'sw_lcc'),
       (4492, 'sw_moo'),
       (4496, 'sw_mf'),
       (4500, 'sw_ms'),
       (4504, 'sw_mm'),
       (4508, 'sw_mwol'),
       (4512, 'sw_mer'),
       (4516, 'sw_mj'),
       (4520, 'sw_mt'),
       (4524, 'sw_mc'),
       (4528, 'sw_mpp'),
       (4532, 'sw_mp'),
       (4536, 'sw_mrmnky'),
       (4540, 'sw_nyf'),
       (4544, 'sw_nyg'),
       (4548, 'sw_pc'),
       (4552, 'sw_pg'),
       (4556, 'sw_pp'),
       (4560, 'sw_pvg'),
       (4564, 'sw_pe'),
       (4568, 'sw_pt'),
       (4572, 'sw_qotp'),
       (4576, 'sw_qv'),
       (4580, 'sw_qow'),
       (4584, 'sw_qoiaf'),
       (4588, 'sw_rf'),
       (4592, 'sw_rm'),
       (4596, 'sw_rsyg'),
       (4600, 'sw_rs'),
       (4604, 'sw_rc'),
       (4608, 'sw_rcr'),
       (4612, 'sw_sf'),
       (4616, 'sw_sc'),
       (4620, 'sw_sog'),
       (4624, 'sw_slws'),
       (4628, 'sw_slbs'),
       (4632, 'sw_fd'),
       (4636, 'sw_scyd'),
       (4640, 'sw_sx'),
       (4644, 'sw_sgcf'),
       (4648, 'sw_sl'),
       (4652, 'sw_fb'),
       (4656, 'sw_sixng'),
       (4660, 'sw_sq'),
       (4664, 'sw_sctz'),
       (4668, 'sw_sld'),
       (4672, 'sw_sod'),
       (4676, 'sw_tr'),
       (4680, 'sw_t2d'),
       (4684, 'sw_tlotws'),
       (4688, 'sw_totiatp'),
       (4692, 'sw_rmac'),
       (4696, 'sw_sfy'),
       (4700, 'sw_ts'),
       (4704, 'sw_tc'),
       (4708, 'sw_tm'),
       (4712, 'sw_vos'),
       (4716, 'sw_vi'),
       (4720, 'sw_wf'),
       (4724, 'sw_wfot'),
       (4728, 'sw_wq'),
       (4732, 'sw_wws'),
       (4736, 'sw_xwk'),
       (4740, 'sw_xyjc'),
       (4744, 'sw_xw'),
       (4748, 'sw_ycs'),
       (4752, 'sw_shctz'),
       (4756, 'sw_zcxm'),
       (4760, 'sw_8tr1qu'),
       (4764, 'sw_loofthsp'),
       (4768, 'sw_sdjg'),
       (4772, 'sw_ylxn'),
       (4776, 'sw_wg'),
       (4780, 'sw_hlcs'),
       (4784, 'sw_xybl'),
       (4788, 'sw_or'),
       (4792, 'sw_lomutabangno'),
       (4796, 'sw_ex'),
       (4800, 'sw_twfr'),
       (4804, 'sw_pote'),
       (4808, 'sw_jogowi'),
       (4812, 'sw_bac'),
       (4816, 'sw_es'),
       (4820, 'sw_ylns'),
       (4824, 'sw_bb'),
       (4828, 'sw_hg'),
       (4832, 'sw_luckyfim'),
       (4836, 'sw_chwi'),
       (4840, 'sw_ggrizzly'),
       (4844, 'sw_yyy'),
       (4848, 'sw_dc'),
       (4852, 'sw_fofefa'),
       (4856, 'sw_gt'),
       (4860, 'sw_wfl'),
       (4864, 'sw_remamere'),
       (4868, 'sw_zhhu'),
       (4872, 'sw_filifo'),
       (4876, 'sw_tiki_luck'),
       (4880, 'sw_nilazhnu'),
       (4884, 'sw_fj'),
       (4888, 'sw_azre'),
       (4892, 'sw_olcaymsh'),
       (4896, 'sw_sland'),
       (4900, 'sw_cashdaha'),
       (4904, 'sw_yxlb'),
       (4908, 'sw_dr'),
       (4912, 'sw_scca2d'),
       (4916, 'sw_wcup'),
       (4920, 'sw_chfi'),
       (4924, 'sw_le'),
       (4928, 'sw_ge1xas-te'),
       (4932, 'sw_jodobi'),
       (4936, 'sw_wi0'),
       (4940, 'sw_wifl'),
       (4944, 'sw_mopa'),
       (4948, 'sw_cashlalala'),
       (4952, 'sw_ra'),
       (4956, 'sw_coma'),
       (4960, 'sw_prli'),
       (4964, 'sw_cada'),
       (4968, 'sw_drgo'),
       (4972, 'sw_papo'),
       (4976, 'ig_giantmusselfishing'),
       (4980, 'ig_kingfishing'),
       (4984, 'ig_caishenfafafa'),
       (4988, 'sw_fobo'),
       (4992, 'sw_wifr'),
       (4996, 'sw_mybe'),
       (5104, 'sw_repo'),
       (5108, 'sw_lubiha'),
       (5112, 'sw_yolidoli'),
       (5116, 'sw_boofge'),
       (5120, 'sw_gejo'),
       (5124, 'sw_rupe'),
       (5128, 'sw_luch'),
       (5132, 'sw_bibume'),
       (5136, 'sw_bago'),
       (5140, 'sw_ssm'),
       (5144, 'sw_jolu'),
       (5148, 'sw_seofpe'),
       (5152, 'sw_alme'),
       (5156, 'sw_tpgsop'),
       (5160, 'sw_jolude'),
       (5164, 'sw_kk_original'),
       (5168, 'sw_csi'),
       (5172, 'sw_thmase'),
       (5176, 'sw_st_original'),
       (5180, 'sw_bloo'),
       (5184, 'sw_rambo'),
       (5188, 'sw_reev'),
       (5192, 'sw_lomathtt');
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP TABLE IF EXISTS bangbet_games;
--rollback RESET search_path;

--changeset aleh.rudzko:2021-09-15-SWS-30250-create-drop-stake-ranges-actions
--comment New actions for stake range api
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'stake-range-delete' where id = 1;
UPDATE roles SET permissions = permissions - 'stake-range-create' where id = 1;
UPDATE roles SET permissions = permissions || '["stake-range-delete", "stake-range-create"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'stake-range-delete' where id = 1;
--rollback UPDATE roles SET permissions = permissions - 'stake-range-create' where id = 1;
--rollback RESET search_path;