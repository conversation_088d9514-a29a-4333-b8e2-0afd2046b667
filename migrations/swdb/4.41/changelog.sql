--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset pavel.shamshurov:2020-07-07-SWS-XXXX-start-release-4.41.0
--comment label for 4.41.0
select now();
--rollback select now();

--changeset pavel.shamshurov:2020-07-07-SWS-19394-add-game-category-permissions
--comment Add game category permissions to superadmin role
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'gamecategory:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamecategory' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamecategory:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamecategory:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamecategory:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamecategory:change-ordering' WHERE id = 1;

UPDATE roles SET permissions = permissions || '["gamecategory:view", "gamecategorys", "gamecategory:create", "gamecategory:edit", "gamecategory:delete", "gamecategory:change-ordering"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'gamecategory:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'gamecategory' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'gamecategory:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'gamecategory:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'gamecategory:delete' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'gamecategory:change-ordering' WHERE id = 1;
--rollback RESET search_path;


--changeset valdis.akmens:2020-07-14-SWS-19571-optimize-access-to-the-historical-data endDelimiter:# stripComments:false
--comment Optimize access to the historical data for queries with exact round_ID, but without finished_at filters
SET search_path TO swmanagement;

ALTER FUNCTION fnc_bo_rounds_history (VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN) RENAME TO fnc_bo_rounds_history_before_4_41_0;

CREATE OR REPLACE FUNCTION fnc_bo_rounds_history(p_where_filters character varying[], p_sort_by character varying[] DEFAULT NULL::character varying[], p_limit integer DEFAULT 20, p_offset integer DEFAULT 0, p_incl_sub_brands boolean DEFAULT false, p_incl_test boolean DEFAULT true)
 RETURNS SETOF rounds_finished
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************

    Object Name:   fnc_bo_rounds_history 
    Purpose    : Provide read access to rounds_history table and rounds_unfinished and rounds_finished
                If not partition key in WHERE, than only to new partitions. If is partition key, than check pathman_config, and pathman_partition_list to see if we need:
                new table
                old table
                or both tables union
    History    :
        1.0.0(?)
            Date    : Oct 16, 2018
            Authors : Valdis Akmens
            Notes   : Release (SWS-6904)
        1.0.1
            Date    : Jan 30, 2019
            Authors : Valdis Akmens
            Notes   : Change SELECT list for tables to dynamic generated to automatically include new columns to function result (SWDB-82)
        1.0.2
            Date    : May 02, 2019
            Authors : Valdis Akmens
            Notes   : Add parameters for including sub-entities in result (SWS-9902)
                    Work with sub-entities moved to special function - fnc_bo_rounds_history_inner - to optimize performance.
        1.0.3
            Date    : May 30, 2019
            Authors : Valdis Akmens
            Notes   : Add support for the historical data in Rounds history related calls.
                    Added call to "swmanagement_archive.rounds_history" if is definied "started_at" in p_where_filters and it points to "swmanagement_archive.rounds_history"
                    time interval (SWS-10600)
        1.0.4
            Date    : Oct 14, 2019
            Authors : Valdis Akmens
            Notes   : Add support for "swmanagement_archive.rounds_finished" and remove unfinished rounds view "swmanagement.v_rounds_unfinished"
                    (SWDB-117)
        1.0.5
            Date    : Mar 25, 2020
            Authors : Valdis Akmens
            Notes   : Remove rounds_history table from "fnc_bo_rounds_history" and check on started_at parition key 
                    (SWS-16933)         
        1.0.6
            Date    : May 15, 2020
            Authors : Valdis Akmens
            Notes   : Based on date interval choose different archive schema
                    (SWDB-138)  
        1.0.7
            Date    : Jul 01, 2020
            Authors : Valdis Akmens
            Notes   : Add special functionality when "round_id" is definied in filters:
                        Optimize access to the historical data for queries with exact "round ID", but without "finished at" filters.
                        Logic should be next:
                        If "round ID" is provided then search in Live Data first,
                        If found nothing, -  then check Historical Data too.
                    (SWS-19571)                  
    Sample run:
    SELECT brand_id,started_at,finished_at FROM fnc_bo_rounds_history(
                                        p_where_filters => '{"brand_id = 51", "started_at >=''2017-12-08 00:00:00''","started_at < ''2017-12-09 00:00:00''" }',
                                        p_sort_by => '{"finished_at DESC"}',
                                        p_limit => 20,
                                        p_offset => 0
                                        );
    SELECT brand_id,started_at,finished_at FROM fnc_bo_rounds_history(
                                        p_where_filters => '{"brand_id = 50", "started_at >=''2017-12-08 00:00:00''","started_at < ''2017-12-09 00:00:00''" }',
                                        p_sort_by => '{"finished_at DESC"}',
                                        p_limit => 20,
                                        p_offset => 0,
                                        p_incl_sub_brands => TRUE,
                                        p_incl_test => FALSE
                                        );
    SELECT * FROM fnc_bo_rounds_history(
                                            p_where_filters => '{"brand_id = 51", "started_at >=''2017-12-08 00:00:00''","started_at < ''2017-12-09 00:00:00''", "finished_at is not null" }'
                                            );
********************************************************************************************************/
DECLARE
    v_filter                VARCHAR;
    v_select                VARCHAR;
    v_select_unf            VARCHAR;
    v_where                 VARCHAR:='WHERE ';
    v_sort_by               VARCHAR:='ORDER BY ';
    v_limit                 VARCHAR:=COALESCE('LIMIT '||p_limit::TEXT,'');
    v_offset                VARCHAR:=COALESCE('OFFSET '||p_offset::TEXT,'');
    v_exec_sql              VARCHAR:='';
    v_line                  VARCHAR;
    v_arch_schema           VARCHAR:='swmanagement_archive';
    v_arch_schema_1         VARCHAR:='swmanagement_archive';
    v_arch_schema_2         VARCHAR:='swmanagement_archive_ro';
    v_arch_schema_int       INTERVAL:= '7 DAYS';
    v_part_key_int          INTERVAL;
    v_rounds_table          VARCHAR:='swmanagement.rounds_finished';
    v_rounds_archive_table  VARCHAR:='rounds_finished';
    v_unfinished_table      VARCHAR:='swmanagement.rounds_unfinished';
    v_old_archive_table     VARCHAR:='rounds_history';
    v_partiton_key          VARCHAR:='finished_at';
    v_partiton_key_filters  VARCHAR[];
    v_is_in_archive         BOOLEAN:=FALSE;
    v_sub_brands            VARCHAR[];
    v_brand_id              VARCHAR:='';
    v_range_min             TIMESTAMP;
    v_range_max             TIMESTAMP;
    v_rounds_archive_table_full VARCHAR:=v_arch_schema||'.'||v_rounds_archive_table;
    -- For round_id
    v_id                    VARCHAR:='id';
    v_round_id_filters      VARCHAR[];
    v_rounds_rec            swmanagement.rounds_finished%ROWTYPE;
    v_round_id              VARCHAR;
    v_brand_filter          VARCHAR;
    v_new_line              VARCHAR:=chr(10);
BEGIN

     /* Check mandatory params */
    IF p_where_filters IS NULL THEN
        RAISE EXCEPTION 'Filter for WHERE must be defined!';
    END IF;

    -- Get column list
    SELECT 'SELECT '||string_agg(attname, ', ' ORDER BY attnum)||' FROM '
    INTO v_select
    FROM pg_catalog.pg_attribute
    WHERE  attrelid = v_rounds_table::regclass
    AND    attnum > 0
    AND    NOT attisdropped;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No column list for %.', v_rounds_table;
    END IF;

    -- Make select list for swmanagement.rounds_unfinished
    v_select_unf:=REPLACE(v_select,'finished_at','NULL::TIMESTAMP AS finished_at');

    --Build ORDER BY
    IF p_sort_by IS NULL OR p_sort_by = '{}'::VARCHAR[] THEN
        v_sort_by:= v_sort_by||v_partiton_key||' DESC';
    ELSE
        FOREACH v_filter IN ARRAY p_sort_by LOOP
            v_sort_by:=v_sort_by||CASE WHEN array_position(p_sort_by, v_filter) <> 1 THEN ' ,' ELSE '' END ||v_new_line||v_filter;
        END LOOP;
    END IF;

    --Build WHERE filter
    FOREACH v_filter IN ARRAY p_where_filters LOOP
        v_where:=v_where||CASE WHEN array_position(p_where_filters, v_filter) <> 1 THEN ' AND' ELSE '' END ||v_new_line||v_filter;
        -- Get datetime values from partition key filters, to match them with pathman configuration tables
        IF  (v_filter ILIKE v_partiton_key||'%' AND v_filter NOT ILIKE '%true%' AND v_filter NOT ILIKE '%false%') THEN
            v_partiton_key_filters:= array_append(v_partiton_key_filters, (SELECT substring(v_filter from '\''.*?\''' ))::VARCHAR);
        END IF;

        -- Get round ids
        IF  (v_filter ILIKE v_id||'%' ) THEN
            IF (v_filter ILIKE '%IN%') THEN 
            v_round_id_filters:= array_append(v_round_id_filters, (SELECT substring(v_filter from '\((.+)\)' ))::VARCHAR);
            END IF; 
            IF (v_filter ILIKE '%=%') THEN 
            v_round_id_filters:= array_append(v_round_id_filters, (SELECT split_part(v_filter,'=',2))::VARCHAR);
            END IF; 
        END IF;
        -- Get brand_id filter
        IF (v_filter ILIKE 'brand_id%') THEN 
            v_brand_filter:= v_filter;
        END IF;
    END LOOP;

    -- WITH round_id in filter and without finished_at in filter
    IF v_round_id_filters IS NOT NULL AND v_partiton_key_filters IS NULL THEN 
    --==================================================================================================================================================================
        v_arch_schema := v_arch_schema_2;
        v_rounds_archive_table:= v_arch_schema||'.'||v_rounds_archive_table;
        v_old_archive_table   := v_arch_schema||'.'||v_old_archive_table;
        IF p_incl_sub_brands = FALSE THEN 
            FOREACH v_filter IN ARRAY v_round_id_filters LOOP
                -- Loop trough round_ids
                FOR v_round_id IN EXECUTE 'SELECT UNNEST(string_to_array('||quote_literal(v_filter)||', '','') );' LOOP
                    v_where:='WHERE id = '||v_round_id||' AND '||v_brand_filter;
                    v_exec_sql:=v_select||' (('||v_new_line||
                            v_select||v_rounds_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                            ||') 
                                UNION ALL ( SELECT * FROM ( '||
                            v_select_unf||v_unfinished_table||') AS q '||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                            ||')
                            ) AS q '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
                    -- Check Live cluster 
                    EXECUTE v_exec_sql INTO v_rounds_rec; 
                    IF v_rounds_rec.id IS NOT NULL THEN 
                        RETURN NEXT v_rounds_rec;
                    ELSE 
                        -- If not found on live cluster check historical 
                        v_exec_sql:=v_select||' (('||
                            v_select||v_old_archive_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                            ||')
                                UNION ALL ('||
                            v_select||v_rounds_archive_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                            ||')
                            ) AS q '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
                        EXECUTE v_exec_sql INTO v_rounds_rec; 
                        IF v_rounds_rec.id IS NOT NULL THEN 
                            RETURN NEXT v_rounds_rec;
                        END IF;
                    END IF;

                END LOOP;

            END LOOP;
        ELSE 
            -- If needed sub-brands get all brand_id in ARRAY
            FOREACH v_filter IN ARRAY p_where_filters LOOP
                IF v_filter ILIKE '%brand_id%' THEN
                        FOR v_brand_id IN  EXECUTE 'WITH RECURSIVE hierarchy AS
                                                    (
                                                        SELECT brand_id, parent, name, type, 1 AS deep_level, title, key, path, is_test
                                                        FROM   (SELECT id as brand_id, parent, name, type, 1 AS deep_level, title, key, path, is_test FROM entities) AS x
                                                        WHERE  '||v_filter||'
                                                        UNION ALL
                                                        SELECT en.id AS brand_id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.key, en.path, en.is_test
                                                        FROM   entities en
                                                        INNER JOIN hierarchy h ON en.parent = h.brand_id
                                                    )
                                                    SELECT brand_id::VARCHAR AS sub_brand_id
                                                    FROM   hierarchy
                                                    WHERE  is_test = COALESCE(CASE WHEN '||p_incl_test||' <> FALSE THEN NULL ELSE '||p_incl_test||' END, is_test);' LOOP
                            v_sub_brands:= array_append(v_sub_brands, v_brand_id);
                        END LOOP;
                END IF;
            END LOOP;
                --------------------------------------------------
                FOREACH v_filter IN ARRAY v_round_id_filters LOOP
                    -- Loop trough round_ids
                    FOR v_round_id IN EXECUTE 'SELECT UNNEST(string_to_array('||quote_literal(v_filter)||', '','') );' LOOP
                        v_where:='WHERE id = '||v_round_id;
                        v_exec_sql:=v_select||' (('||v_new_line||
                                v_select||v_rounds_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                                ||') 
                                    UNION ALL ( SELECT * FROM ( '||
                                v_select_unf||v_unfinished_table||') AS q '||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                                ||')
                                ) AS q '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
                        -- Check Live cluster
                        EXECUTE v_exec_sql INTO v_rounds_rec; 
                        IF (v_rounds_rec.id IS NOT NULL AND v_rounds_rec.brand_id::VARCHAR = ANY(v_sub_brands)) THEN 
                            RETURN NEXT v_rounds_rec;
                        ELSE 
                            -- If not found on live cluster check historical
                            v_exec_sql:=v_select||' (('||
                                v_select||v_old_archive_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                                ||')
                                    UNION ALL ('||
                                v_select||v_rounds_archive_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                                ||')
                                ) AS q '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
                            EXECUTE v_exec_sql INTO v_rounds_rec; 
                            IF (v_rounds_rec.id IS NOT NULL AND v_rounds_rec.brand_id::VARCHAR = ANY(v_sub_brands)) THEN 
                                RETURN NEXT v_rounds_rec;
                            END IF;
                        END IF;
                    END LOOP;
                END LOOP;
        END IF;
        RETURN;
    --==================================================================================================================================================================
    ELSE 
    --====================================================================== WITHOUT round_id in filter ================================================================
        -- Check if exists partition key filters
        IF v_partiton_key_filters IS NOT NULL THEN

            v_range_max:= (SELECT                     
                                GREATEST(
                                    (SELECT MAX(range_max::timestamp)
                                    FROM public.pathman_partition_list
                                    WHERE parent IN (v_old_archive_table::regclass)
                                    ), 
                                    (SELECT MAX(range_max::timestamp)
                                    FROM public.pathman_partition_list
                                    WHERE parent IN (v_rounds_archive_table_full::regclass)
                                    )                         
                            ));
            -- Check if partition filters points to archive table
            IF ( EXISTS(SELECT * FROM unnest( v_partiton_key_filters ) AS part_filter WHERE part_filter.part_filter::timestamp < v_range_max) ) THEN 
                v_is_in_archive:= TRUE;
                v_part_key_int := (SELECT (SELECT MAX(part_filter.part_filter::TIMESTAMP) FROM unnest( v_partiton_key_filters ) AS part_filter) - (SELECT MIN(part_filter.part_filter::TIMESTAMP) FROM unnest( v_partiton_key_filters ) AS part_filter));
                IF v_part_key_int > '00:00:00'::INTERVAL AND  v_part_key_int < v_arch_schema_int THEN 
                    v_arch_schema := v_arch_schema_1;
                ELSE 
                    v_arch_schema := v_arch_schema_2;
                END IF;
            END IF;
            v_is_in_archive:=COALESCE(v_is_in_archive,FALSE);
        END IF;

        v_rounds_archive_table:= v_arch_schema||'.'||v_rounds_archive_table;
        v_old_archive_table   := v_arch_schema||'.'||v_old_archive_table;

        IF p_incl_sub_brands = FALSE THEN
                -- Build EXEC string based on which tables need to use
                CASE
                    -- Add swmanagement_archive.rounds_history table to union
                    WHEN v_is_in_archive THEN
                        v_exec_sql:=v_select||' (('||v_new_line||
                        v_select||v_rounds_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                        ||') 
                            UNION ALL ( SELECT * FROM ( '||
                        v_select_unf||v_unfinished_table||') AS q '||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                        ||')
                            UNION ALL ('||
                        v_select||v_old_archive_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                        ||')
                            UNION ALL ('||
                        v_select||v_rounds_archive_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                        ||')
                        ) AS q '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
                    ------------------------------------------------------------------------------------------------------------------------
                    ELSE
                        v_exec_sql:=v_select||' (('||v_new_line||
                        v_select||v_rounds_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                        ||') 
                            UNION ALL ( SELECT * FROM ( '||
                        v_select_unf||v_unfinished_table||') AS q '||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
                        ||')
                        ) AS q '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
                END CASE;
        ELSE
            -- If needed sub-brands get all brand_id in ARRAY
            FOREACH v_filter IN ARRAY p_where_filters LOOP
                IF v_filter ILIKE '%brand_id%' THEN
                        FOR v_brand_id IN  EXECUTE 'WITH RECURSIVE hierarchy AS
                                                    (
                                                        SELECT brand_id, parent, name, type, 1 AS deep_level, title, key, path, is_test
                                                        FROM   (SELECT id as brand_id, parent, name, type, 1 AS deep_level, title, key, path, is_test FROM entities) AS x
                                                        WHERE  '||v_filter||'
                                                        UNION ALL
                                                        SELECT en.id AS brand_id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.key, en.path, en.is_test
                                                        FROM   entities en
                                                        INNER JOIN hierarchy h ON en.parent = h.brand_id
                                                    )
                                                    SELECT ''brand_id = ''||brand_id::VARCHAR AS sub_brand_id
                                                    FROM   hierarchy
                                                    WHERE  is_test = COALESCE(CASE WHEN '||p_incl_test||' <> FALSE THEN NULL ELSE '||p_incl_test||' END, is_test);' LOOP
                            v_sub_brands:= array_append(v_sub_brands, v_brand_id);
                        END LOOP;
                END IF;
            END LOOP;
            -- Call sub-function for brand_id array
            v_exec_sql:=v_select||'( '||v_new_line||
                    'SELECT * FROM fnc_bo_rounds_history_inner( '||quote_literal(p_where_filters::TEXT)||', '''||v_sort_by::TEXT||''', '||p_limit::TEXT||', '||p_offset::TEXT||', '''||v_select||''' ,'||v_is_in_archive::TEXT||', '''||v_sub_brands::TEXT||''', TRUE, NULL, '''
                    ||v_arch_schema||''')'||v_new_line||
                    ') AS x '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
        END IF;

        ---RAISE INFO '[%]: v_exec_sql: %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_new_line||v_exec_sql;
        -- Check execution plan
        /*FOR v_line IN EXECUTE 'EXPLAIN ANALYZE '||v_exec_sql LOOP
            RAISE INFO '% ' , v_line;
        END LOOP;*/
        RETURN QUERY
        EXECUTE v_exec_sql;
    END IF;

END;
$function$
;

RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_rounds_history(VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN);
--rollback ALTER FUNCTION fnc_bo_rounds_history_before_4_41_0 (VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN) RENAME TO fnc_bo_rounds_history;
--rollback RESET search_path;


--changeset anastasia.kostyukova:2020-07-16-SWS-20250-rtp-fixes
--comment Fix rtp nested json
SET search_path = swmanagement;

WITH cte_rtp AS (SELECT jsonb_each_text(rtp_info) AS rtp_pair, id FROM game_rtp_history)
UPDATE game_rtp_history a
SET rtp_info = rtp_info || jsonb_build_object((rtp_pair)."key", (rtp_pair).value::jsonb)
FROM cte_rtp b
WHERE b.id = a.id AND (b.rtp_pair)."key" = 'baseRTPRange';

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback SELECT now;
--rollback RESET search_path;

--changeset anastasia.kostyukova:2020-07-16-SWS-18438-rtp-report-function endDelimiter:# stripComments:false
--comment Get rtp history report: merge parent changes to child
SET search_path = swmanagement;


CREATE OR REPLACE FUNCTION fnc_list_game_rtp_history
(
    p_entity_id     integer,
    p_game_codes    varchar[]   DEFAULT NULL,
    p_ts_from       timestamp   DEFAULT NULL,
    p_ts_till       timestamp   DEFAULT NULL
)
 RETURNS SETOF game_rtp_history
 LANGUAGE plpgsql
AS  $function$
/********************************************************************************************************
    Object Name: fnc_list_game_rtp_history
    Purpose    : List from the table game_rtp_history for given entity_id, list of games and period,
                with merged rtp_deduction from the entity ancestors
    History    :
        1.0.0
            Date    : Jul 09, 2020
            Authors : Ales
            Notes   : Release (DEVOPS-9481)
    Sample run:
        SELECT * FROM fnc_list_game_rtp_history (
                                        p_entity_id => 40493
                                       --, p_game_codes => '{sw_prli, sw_qow, sw_omq}'::VARCHAR[]
                                       --, p_ts_from => '200709 15:42'
                                       --, p_ts_till => '200709 15:42'
                                    );

********************************************************************************************************/
DECLARE
    v_code  VARCHAR;
BEGIN

    /* Check mandatory params */
    IF (p_entity_id IS NULL) THEN
        RAISE EXCEPTION 'Parameter Entity must be DEFINED!';
    END IF;

    RETURN QUERY
        WITH RECURSIVE cte_entity_hier_up AS
    ( -- full hierarchy tree to root direction for given entity_id
        SELECT id, parent, name, type, 0 AS root_level, title, path, is_test
        FROM entities
        WHERE id = p_entity_id
        UNION ALL
        SELECT e.id, e.parent, e.name, e.type, h.root_level + 1 AS root_level, e.title, e.path, e.is_test
        FROM cte_entity_hier_up h
        INNER JOIN entities e ON e.id = h.parent
    )
    , cte_game_rtp_hist AS
    ( -- history of the given entity, its ancients
        SELECT h.id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code,
            e.root_level, e.path
        FROM game_rtp_history h
        INNER JOIN cte_entity_hier_up e ON e.id = h.entity_id
        WHERE (p_game_codes IS NULL OR game_code = ANY(p_game_codes))
            AND (p_ts_till IS NULL OR ts <= p_ts_till)
    )
    , cte_game_rtp_hist_full AS
    ( -- add history of games which have links to the main entity
        SELECT id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code,
            root_level
        FROM cte_game_rtp_hist
        UNION ALL
        SELECT id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code,
            999 AS root_level
        FROM game_rtp_history h
        WHERE entity_id IS NULL
            AND game_id IN (SELECT game_id FROM cte_game_rtp_hist WHERE entity_id = p_entity_id)
            AND (p_ts_till IS NULL OR ts <= p_ts_till)
    )
    , cte_rtp_deduction_values AS
    ( -- all rtp_deduction key-value pairs */
        SELECT id, entity_id, game_id, ts,
            root_level,
            (jsonb_each(rtp_deduction))."key" AS rtp_key,
            (jsonb_each(rtp_deduction)).value AS rtp_val
        FROM cte_game_rtp_hist
    )
    , cte_game_rtp_hist_ext AS
    (
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
                v.rtp_key,
                FIRST_VALUE(v.rtp_val) OVER (
                        PARTITION BY h.entity_id, h.game_id, h.ts, v.rtp_key
                        ORDER BY v.root_level, v.ts DESC
                ) AS rtp_val
        FROM cte_game_rtp_hist_full h
        LEFT JOIN cte_rtp_deduction_values v
            ON v.id = h.id OR (v.game_id = h.game_id AND v.ts <= h.ts AND v.root_level < h.root_level)
    )
    SELECT id, entity_id, game_id, rtp_info,
            CASE WHEN max(rtp_key) IS NULL
                THEN '{}'::jsonb
                ELSE jsonb_object_agg(COALESCE (rtp_key, '-'), rtp_val )
            END AS rtp_deduction,
            ts, game_code
    FROM cte_game_rtp_hist_ext
    WHERE (p_ts_from IS NULL OR ts >= p_ts_from)
    GROUP BY id, entity_id, game_id, rtp_info, ts, game_code
    ORDER BY game_id, ts, entity_id;

END $function$
;

ALTER FUNCTION fnc_add_entity_games OWNER TO swmanagement;
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_list_game_rtp_history(INTEGER, VARCHAR[], TIMESTAMP, TIMESTAMP);
--rollback RESET search_path;


--changeset  anastasia.kostyukova:2020-07-23-SWS-18438-rtp-permissions
--comment Add rtp permissions to superadmin role
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:gamertp:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:gamertp' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamertp:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamertp' WHERE id = 1;

UPDATE roles SET permissions = permissions || '["keyentity:gamertp:view", "keyentity:gamertp", "gamertp:view", "gamertp"]'::jsonb WHERE id = 1;

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:gamertp:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:gamertp' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'gamertp:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'gamertp' WHERE id = 1;

--rollback RESET search_path;


--changeset pavel.shamshurov:2020-07-07-SWS-19394-fix-permission
--comment Fix permission
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'gamecategory' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamecategorys' WHERE id = 1;

UPDATE roles SET permissions = permissions || '["gamecategory"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'gamecategory' WHERE id = 1;
--rollback RESET search_path;
