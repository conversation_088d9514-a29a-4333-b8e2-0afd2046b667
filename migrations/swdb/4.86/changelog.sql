--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset emanuel-alin.railean:2022-05-23_SWS-34121-add-ext_session_id-column
--comment Add the ext_session_id column to the sessions_history table
SET search_path = swmanagement;
ALTER TABLE sessions_history ADD COLUMN ext_session_id VARCHAR(255);
COMMENT ON COLUMN sessions_history.ext_session_id IS 'Operator''s session id';
ALTER TABLE sessions_history_duplicates ADD COLUMN ext_session_id VARCHAR(255);
COMMENT ON COLUMN sessions_history_duplicates.ext_session_id IS 'Operator''s session id';
RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE sessions_history ADD COLUMN ext_session_id VARCHAR(255);
COMMENT ON COLUMN sessions_history.ext_session_id IS 'Operator''s session id';
RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE sessions_history ADD COLUMN ext_session_id VARCHAR(255);
COMMENT ON COLUMN sessions_history.ext_session_id IS 'Operator''s session id';
RESET search_path;
--rollback SET search_path = swmanagement_archive_ro;
--rollback ALTER TABLE sessions_history DROP COLUMN ext_session_id;
--rollback RESET search_path;
--rollback SET search_path = swmanagement_archive;
--rollback ALTER TABLE sessions_history DROP COLUMN ext_session_id;
--rollback RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE sessions_history DROP COLUMN ext_session_id;
--rollback ALTER TABLE sessions_history_duplicates DROP COLUMN ext_session_id;
--rollback RESET search_path;

--changeset mikhail.ivanov:2022-05-23_SWS-34258_return-free-bet-coin-value-within-get-entities-history
--comment Add free_bet_coin column in spins_history table
SET search_path = swmanagement;
ALTER TABLE spins_history ADD IF NOT EXISTS free_bet_coin numeric;
ALTER TABLE spins_history_duplicates ADD IF NOT EXISTS free_bet_coin numeric;
COMMENT ON COLUMN spins_history.free_bet_coin IS 'Information about real coin bet value';
COMMENT ON COLUMN spins_history_duplicates.free_bet_coin IS 'Information about real coin bet value';
RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE spins_history ADD IF NOT EXISTS free_bet_coin numeric;
COMMENT ON COLUMN spins_history.free_bet_coin IS 'Information about real coin bet value';
RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE spins_history ADD IF NOT EXISTS free_bet_coin numeric;
COMMENT ON COLUMN spins_history.free_bet_coin IS 'Information about real coin bet value';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE spins_history DROP IF EXISTS free_bet_coin;
--rollback ALTER TABLE spins_history_duplicates DROP IF EXISTS free_bet_coin;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive;
--rollback ALTER TABLE spins_history DROP IF EXISTS free_bet_coin;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive_ro;
--rollback ALTER TABLE spins_history DROP IF EXISTS free_bet_coin;
--rollback RESET search_path;

--changeset vladimir.minakov:2022-06-03_SWS-33829-bo-api-ip-whitelist-add-link-to-jira-ticket
--comment Add initiator_issue_id column in audits table
SET search_path = swmanagement;
ALTER TABLE audits ADD IF NOT EXISTS initiator_issue_id varchar(255);
COMMENT ON COLUMN audits.initiator_issue_id IS 'Initiator Jira issue';
RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE audits ADD IF NOT EXISTS initiator_issue_id VARCHAR(255);
COMMENT ON COLUMN audits.initiator_issue_id IS 'Initiator Jira issue';
RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE audits ADD IF NOT EXISTS initiator_issue_id VARCHAR(255);
COMMENT ON COLUMN audits.initiator_issue_id IS 'Initiator Jira issue';
RESET search_path;
--rollback SET search_path = swmanagement_archive_ro;
--rollback ALTER TABLE audits DROP IF EXISTS initiator_issue_id;
--rollback RESET search_path;
--rollback SET search_path = swmanagement_archive;
--rollback ALTER TABLE audits DROP IF EXISTS initiator_issue_id;
--rollback RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE audits DROP IF EXISTS initiator_issue_id;
--rollback RESET search_path;

--changeset ivan.puzyrny:2022-06-06-SWS-31916-ability-to-change-is-test-param-in-jp-settings
--comment add new permission to edit "isTest" jackpot instance parameter
SET search_path = swmanagement;
INSERT INTO permissions(code, description, created_at, updated_at)
VALUES('jackpot:instance:edit:is-test', 'Edit isTest jackpot instance parameter', NOW(), NOW());
UPDATE roles SET permissions = permissions || '["jackpot:instance:edit:is-test"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM permissions WHERE code = 'jackpot:instance:edit:is-test';
--rollback UPDATE roles SET permissions = permissions - 'jackpot:instance:edit:is-test' WHERE id = 1;
--rollback RESET search_path;



--changeset valdis.akmens:2022-06-13-SWS-35057-add_column endDelimiter:# stripComments:false
--comment Add column to output
SET search_path TO swmanagement;
ALTER FUNCTION fnc_bo_audits (character varying[],character varying[], integer, integer, boolean , boolean) RENAME TO fnc_bo_audits_before_4_86_0;
ALTER FUNCTION fnc_bo_audits_before_4_86_0 SET SCHEMA swbackup;

CREATE OR REPLACE FUNCTION fnc_bo_audits(p_where_filters character varying[], p_sort_by character varying[] DEFAULT NULL::character varying[], p_limit integer DEFAULT 20, p_offset integer DEFAULT 0, p_incl_sub_brands boolean DEFAULT false, p_incl_test boolean DEFAULT true)
 RETURNS TABLE(
        --audits
        audit_id                INTEGER,
        entity_id               INTEGER,
        ts                      TIMESTAMP,
        audits_summary_id       SMALLINT,
        history                 JSONB,
        initiator_type          swmanagement.enum_audits_initiator_type,
        initiator_name          VARCHAR(255),
        ip                      INET,
        user_agent              VARCHAR(2048),
        initiator_service_name  VARCHAR(255),
        audits_session_id       UUID,
        initiator_issue_id      VARCHAR(255),
        -- audits_summary
        id                      INTEGER,
        event_name              VARCHAR(255),
        summary                 VARCHAR(255),
        path                    VARCHAR(255),
        method                  swmanagement.enum_action_methods,
        created_at              TIMESTAMP,
        updated_at              TIMESTAMP
    )
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************

    Object Name:   fnc_bo_audits
    Purpose    :   Provide read access to audits table and archived audits_archive
                If not partition key in WHERE, than only to new partitions. If is partition key, than check pathman_config, and pathman_partition_list to see if we need:
                new table
                old table
                or both tables union
    History    :
        1.0.0
            Date    : Oct 16, 2018
            Authors : Valdis Akmens
            Notes   : Release (SWS-6904)
        1.0.1
            Date    : Jan 30, 2019
            Authors : Valdis Akmens
            Notes   : Change SELECT list for tables to dynamic generated to automatically include new columns to function result (SWDB-82)
        1.0.2
            Date    : May 02, 2019
            Authors : Valdis Akmens
            Notes   : Add parameters for including sub-entities in result (SWS-9902)
        1.0.3
            Date    : Aug 02, 2019
            Authors : Valdis Akmens
            Notes   : Postgres optimizator chooses wrong index when WHERE filter is definied with entity_id IN (..,..,)
                        it should choose index (entity_id, ts), not (ts) to force use right index string:", entity_id" is added to ORDER BY (SWDB-104)
        1.0.4
            Date    : Aug 11, 2020
            Authors : Valdis Akmens
            Notes   : Correct criteria to search in historical and live clusters (SWS-18819)
        1.0.5
            Date    : Sep 24, 2020
            Authors : Valdis Akmens
            Notes   : Extend procedure for get audit with audits_summary nested data (SWS-21782)
        1.0.6
            Date    : Nov 16, 2020
            Authors : Valdis Akmens
            Notes   : Correct error when not found filters in audits_summary (SWS-22966)
        1.0.7
            Date    : Jun 13, 2022
            Authors : Valdis Akmens
            Notes   : Added new column to audits (SWS-35057)
    Sample run:
    SELECT audit_id, entity_id, ts,initiator_type FROM fnc_bo_audits(
                                        p_where_filters => '{"entity_id = 51", "ts >=''2017-07-11 00:00:00''","ts < ''2017-07-19 00:00:00''" }',
                                        p_sort_by => '{"entity_id DESC"}',
                                        p_limit => 5,
                                        p_offset => 0
                                        );
    SELECT audit_id, entity_id, ts,initiator_type FROM fnc_bo_audits(
                                        p_where_filters => '{"entity_id = 50", "ts >=''2017-07-11 00:00:00''","ts < ''2017-07-19 00:00:00''" }',
                                        p_sort_by => '{"entity_id DESC"}',
                                        p_limit => 5,
                                        p_offset => 0,
                                        p_incl_sub_brands => TRUE,
                                        p_incl_test => FALSE
                                        );
    SELECT audit_id, entity_id, ts,initiator_type,path,method,event_name FROM swmanagement.fnc_bo_audits (p_where_filters => '{"entity_id = 275", "ts >=''2020-08-16 23:59:00''", "ts <''2020-08-18 23:59:00''", "event_name = ''LOGIN''" }', p_sort_by => '{"ts ASC"}', p_limit => 5, p_offset => 0 );
********************************************************************************************************/
DECLARE
    v_filter                VARCHAR;
    v_select                VARCHAR;
    v_select_act            VARCHAR;
    v_select_sum            VARCHAR;
    v_where                 VARCHAR:='WHERE ';
    v_where_sum             VARCHAR:='WHERE 1=1';
    v_sum_ids               VARCHAR:='';
    v_sort_by               VARCHAR:='ORDER BY ';
    v_limit                 VARCHAR:=COALESCE('LIMIT '||p_limit::TEXT,'');
    v_offset                VARCHAR:=COALESCE('OFFSET '||p_offset::TEXT,'');
    v_exec_sql              VARCHAR:='';
    v_line                  VARCHAR;
    v_actual_table          VARCHAR:='swmanagement.audits';
    v_summary_table         VARCHAR:='swmanagement.audits_summary';
    v_archive_table         VARCHAR:='swmanagement_archive.audits';
    v_partiton_key          VARCHAR;
    v_partiton_key_filters  VARCHAR[];
    v_is_in_archive         BOOLEAN:=FALSE;
    v_sub_brands            VARCHAR:='';
    v_new_line              VARCHAR:=chr(10);
    v_range_max             TIMESTAMP;
BEGIN

     /* Check mandatory params */
    IF p_where_filters IS NULL THEN
        RAISE EXCEPTION 'Filter for WHERE must be defined!';
    END IF;

    -- Get partition key from pathman configuration table
    SELECT expr
    INTO v_partiton_key
    FROM public.pathman_config
    WHERE
        partrel  = v_actual_table::regclass
    AND parttype = 2;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Table % has no valid partitions.', v_actual_table;
    END IF;

    -- Get column list for audits
    SELECT ' '||string_agg(attname, ', ' ORDER BY attnum)||' '
    INTO v_select_act
    FROM pg_catalog.pg_attribute
    WHERE  attrelid = v_actual_table::regclass
    AND    attnum > 0
    AND    NOT attisdropped;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No column list for %.', v_actual_table;
    END IF;

    -- Get column list for audits_summary
    SELECT ' '||string_agg(attname, ', ' ORDER BY attnum)||' '
    INTO v_select_sum
    FROM pg_catalog.pg_attribute
    WHERE  attrelid = v_summary_table::regclass
    AND    attnum > 0
    AND    NOT attisdropped;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No column list for %.', v_summary_table;
    END IF;

    v_select:= 'SELECT '||v_select_act||','||v_select_sum||' FROM ';

    --Build WHERE filter
    FOREACH v_filter IN ARRAY p_where_filters LOOP
        -- If includes sub-brands then need to find all brands for brand_id from p_where_filters
        IF p_incl_sub_brands = TRUE AND v_filter ILIKE '%entity_id%' THEN
                    --RAISE INFO '[%]: v_filter : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_filter;
                    EXECUTE 'WITH RECURSIVE hierarchy AS
                                            (
                                                SELECT entity_id, parent, name, type, 1 AS deep_level, title, key, path, is_test
                                                FROM   (SELECT id as entity_id, parent, name, type, 1 AS deep_level, title, key, path, is_test FROM entities) AS x
                                                WHERE  '||v_filter||'
                                                UNION ALL
                                                SELECT en.id AS entity_id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.key, en.path, en.is_test
                                                FROM   entities en
                                                INNER JOIN hierarchy h ON en.parent = h.entity_id
                                            )
                                            SELECT ''entity_id IN (''|| string_agg(entity_id::varchar, '', '' ORDER BY entity_id) ||'')''
                                            FROM   hierarchy
                                            WHERE  is_test = COALESCE(CASE WHEN '||p_incl_test||' <> FALSE THEN NULL ELSE '||p_incl_test||' END, is_test);' INTO v_sub_brands;
                    --RAISE INFO '[%]: v_sub_brands : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_sub_brands;
                    v_filter:= v_sub_brands;
        END IF;
        v_where:=v_where||CASE WHEN array_position(p_where_filters, v_filter) <> 1 THEN ' AND' ELSE '' END ||v_new_line||v_filter;

        -- Get datetime values from partition key filters, to match them with pathman configuration tables
        IF v_filter ILIKE v_partiton_key||'%' THEN
            v_partiton_key_filters:= array_append(v_partiton_key_filters, (SELECT substring(v_filter from '\''.*?\''' ))::VARCHAR);
        END IF;
        -- Get filters for audits_summary table
        IF v_filter ILIKE 'event_name%' OR v_filter ILIKE 'summary%' OR v_filter ILIKE 'path%' OR v_filter ILIKE 'method%' THEN
            v_where_sum:= v_where_sum||' AND '||v_new_line||v_filter;
        END IF;

    END LOOP;

    -- Add ids to WHERE clause from audits_summary to optimize query to archive cluster
    IF v_where_sum <> 'WHERE 1=1' THEN
        EXECUTE '
                SELECT ''(audits_summary_id IN (''|| string_agg(id::varchar, '', '' ORDER BY id) ||''))''
                        FROM  '||v_summary_table||v_new_line||v_where_sum||';' INTO v_sum_ids;
        v_sum_ids:= COALESCE(v_sum_ids, '1 = 0');
        v_where:= v_where||' AND '||v_new_line||v_sum_ids;
    END IF;

    --Build ORDER BY
    IF p_sort_by IS NULL OR p_sort_by = '{}'::VARCHAR[] THEN
        v_sort_by:= v_sort_by||v_partiton_key||' DESC';
    ELSE
        FOREACH v_filter IN ARRAY p_sort_by LOOP
            v_sort_by:=v_sort_by||CASE WHEN array_position(p_sort_by, v_filter) <> 1 THEN ' ,' ELSE '' END ||v_new_line||v_filter;
        END LOOP;
    END IF;

    --2019-08-02 Special hack to make Postgres use index (entity_id, ts) when conditions is with IN like:  "WHERE entity_id IN ('2219', '2221', '2270', '2533')"
    IF v_where ILIKE '%entity_id in%' THEN
        v_sort_by:=v_sort_by||' ,'||v_new_line||' entity_id';
    END IF;

    -- Check if exists partition key filters
    IF v_partiton_key_filters IS NOT NULL THEN
        --RAISE INFO '[%]: Partition key exists in WHERE ', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS');

        -- Check if partition filters points to archive table
        v_range_max:= (SELECT MAX(range_max::timestamp)
                                FROM public.pathman_partition_list
                                WHERE parent IN (v_archive_table::regclass)
                        );
        -- Check if partition filters points to archive table
        IF ( EXISTS(SELECT * FROM unnest( v_partiton_key_filters ) AS part_filter WHERE part_filter.part_filter::timestamp < v_range_max) ) THEN
            v_is_in_archive:= TRUE;
        END IF;
        v_is_in_archive:=COALESCE(v_is_in_archive,FALSE);

        -- Build EXEC string based on which tables need to use
        IF v_is_in_archive = TRUE THEN
            v_exec_sql:=v_select||' (('||v_new_line||
            v_select||v_actual_table||v_new_line||' JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
            ||') UNION ALL ('||
            v_select||v_archive_table||v_new_line||'JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
            ||')) AS q '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
        ELSE
            v_exec_sql:=v_select||v_actual_table||v_new_line||' JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
        END IF;
    ELSE
        -- If partition key doesnt exist in WHERE use only actual table
        v_exec_sql:=v_select||v_actual_table||v_new_line||' JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
    END IF;

    --RAISE INFO '[%]: v_exec_sql: %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_new_line||v_exec_sql;

    RETURN QUERY
        EXECUTE v_exec_sql
        ;
END;
$function$
;

--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_audits(character varying[],character varying[], integer , integer , boolean , boolean);
--rollback ALTER FUNCTION swbackup.fnc_bo_audits_before_4_86_0 SET SCHEMA swmanagement;
--rollback ALTER FUNCTION fnc_bo_audits_before_4_86_0 (character varying[],character varying[],  integer , integer , boolean , boolean) RENAME TO fnc_bo_audits;
--rollback RESET search_path;
