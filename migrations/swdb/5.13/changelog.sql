--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset aleksey.stepanov:2023-08-24-SWS-41477
--comment [Live Games] Update configuration of the structural place of "gameFinalizationType" parameter
SET search_path TO swmanagement;

WITH live_feat AS (
    SELECT id, code
         ,features - 'live' AS feat_no_live
         ,CASE WHEN (features -> 'live' -> 'gameFinalizationType') IS NOT NULL
                   THEN jsonb_build_object('gameFinalizationType', (features -> 'live' -> 'gameFinalizationType')) ELSE '{}'::JSONB END AS feat_gft
         ,CASE WHEN (features -> 'live') IS NOT NULL
                   THEN jsonb_build_object('live', ((features -> 'live') - 'gameFinalizationType')) ELSE '{}'::JSONB END AS feat_new_live
         ,features AS feat_original
    FROM games WHERE type = 'live'
)
UPDATE games g SET updated_at = now(), features = l.feat_no_live || l.feat_gft || l.feat_new_live
FROM  live_feat l
WHERE l.id = g.id;

RESET search_path;

--rollback select now();
