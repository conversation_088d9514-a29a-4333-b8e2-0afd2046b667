--liquibase formatted sql

--changeset nikita.senko:2020-12-29-SWS-23912-remove-nickname-uniquness-validation runInTransaction:false
--comment change index to not unique
SET search_path = swmanagement;
DROP INDEX IF EXISTS idx_merchant_player_nick_name_brand_id_nickname;
CREATE INDEX IF NOT EXISTS idx_merchant_player_info_brand_id_nickname ON merchant_player_info (brand_id, nickname);
DROP INDEX IF EXISTS idx_players_brand_id_nickname;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_players_brand_id_nickname ON players (brand_id, nickname);
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP INDEX IF EXISTS idx_merchant_player_info_brand_id_nickname;
--roolback CREATE UNIQUE INDEX IF NOT EXISTS idx_merchant_player_nick_name_brand_id_nickname ON merchant_player_info (brand_id, nickname);
--roolback DROP INDEX IF EXISTS idx_players_brand_id_nickname;
--roolback CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_players_brand_id_nickname ON players (brand_id, nickname);
--rollback RESET search_path;
