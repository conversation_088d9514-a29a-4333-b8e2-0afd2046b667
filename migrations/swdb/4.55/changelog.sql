--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset nikita.senko:2021-02-25-SWS-25587
--comment add settings columns in integration_test_reports
SET search_path = swmanagement;
ALTER TABLE integration_test_reports ADD COLUMN settings jsonb;
COMMENT ON COLUMN integration_test_reports.settings IS 'tests params';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE integration_test_reports DROP COLUMN settings;
--rollback RESET search_path;

--changeset nikita.senko:2021-02-26-SWS-25235
--comment add title to master entity
SET search_path = swmanagement;
UPDATE entities SET title = COALESCE(title, 'Master Entity') WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE entities SET title = NULL WHERE id = 1;
--rollback RESET search_path;

--changeset sergey.malkov:2021-03-01-SWS-25211-Add-API-to-trigger-round-finalization
--comment Add new permissions for new api
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'entity:gameclose:finalize' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:gameclose:finalize' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["entity:gameclose:finalize","keyentity:gameclose:finalize"]' WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'entity:gameclose:finalize' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:gameclose:finalize' WHERE id = 1;
--rollback RESET search_path;

--changeset aleh.rudzko:2021-03-20-SWS-26118-operator-domain-management
--comment Add new fields to available_sites table
SET search_path = swmanagement;
ALTER TABLE available_sites ADD COLUMN is_default BOOLEAN NOT NULL DEFAULT FALSE;
COMMENT ON COLUMN available_sites.is_default IS 'Indicate if this is the default site for this operator';
ALTER TABLE available_sites ADD COLUMN operator_site_group_name VARCHAR(255);
COMMENT ON COLUMN available_sites.operator_site_group_name IS 'This column is textual and will be used to group several sites for reporting. for example, if I have the following sites: "casino.bwin.com" and "sports.bwin.com" and I want to group them in the reporting under "bwin.com" in the site group name I will put for both of them "bwin.com"';
ALTER TABLE available_sites ADD COLUMN external_code VARCHAR(255);
COMMENT ON COLUMN available_sites.external_code IS 'This will be used for external code identification of a site (for example GVC will send us a field in the game launch which called "gameContext" which contains their site code and we will store it in this column and search by it)';

--comment write infomation about operator site id to session history
ALTER TABLE sessions_history ADD COLUMN operator_site_id INTEGER;
COMMENT ON COLUMN sessions_history.operator_site_id IS 'Operator''s site id';
ALTER TABLE sessions_history_duplicates ADD COLUMN operator_site_id INTEGER;
COMMENT ON COLUMN sessions_history_duplicates.operator_site_id IS 'Operator''s site id';
RESET search_path;

--comment Write information about operator's site id to archive session history
SET search_path = swmanagement_archive;
ALTER TABLE sessions_history ADD COLUMN operator_site_id INTEGER;
COMMENT ON COLUMN sessions_history.operator_site_id IS 'Operator''s site url that player comes from';
RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE sessions_history ADD COLUMN operator_site_id INTEGER;
COMMENT ON COLUMN sessions_history.operator_site_id IS 'Operator''s site url that player comes from';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE available_sites DROP COLUMN is_default;
--rollback ALTER TABLE available_sites DROP COLUMN operator_site_group_name;
--rollback ALTER TABLE available_sites DROP COLUMN external_code;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_site_id;
--rollback ALTER TABLE sessions_history_duplicates DROP COLUMN operator_site_id;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_site_id;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive_ro;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_site_id;
--rollback RESET search_path;


--changeset aleh.rudzko:2021-03-22-SWS-26134-add-operator-site-id-to-history
--comment write infomation about operator site id to round history
SET search_path = swmanagement;
ALTER TABLE rounds_finished ADD COLUMN operator_site_id INTEGER;
ALTER TABLE rounds_unfinished ADD COLUMN operator_site_id INTEGER;
ALTER TABLE rounds_history ADD COLUMN operator_site_id INTEGER;
ALTER TABLE rounds_history_duplicates ADD COLUMN operator_site_id INTEGER;

COMMENT ON COLUMN rounds_unfinished.operator_site_id IS 'Operator''s site id';
COMMENT ON COLUMN rounds_finished.operator_site_id IS 'Operator''s site id';
COMMENT ON COLUMN rounds_history.operator_site_id IS 'Operator''s site id';
COMMENT ON COLUMN rounds_history_duplicates.operator_site_id IS 'Operator''s site id';
RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE rounds_finished ADD COLUMN operator_site_id INTEGER;
ALTER TABLE rounds_history ADD COLUMN operator_site_id INTEGER;
COMMENT ON COLUMN rounds_finished.operator_site_id IS 'Operator''s site id';
COMMENT ON COLUMN rounds_history.operator_site_id IS 'Operator''s site id';
RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE rounds_finished ADD COLUMN operator_site_id INTEGER;
ALTER TABLE rounds_history ADD COLUMN operator_site_id INTEGER;
COMMENT ON COLUMN rounds_finished.operator_site_id IS 'Operator''s site id';
COMMENT ON COLUMN rounds_history.operator_site_id IS 'Operator''s site id';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE rounds_finished DROP COLUMN operator_site_id;
--rollback ALTER TABLE rounds_unfinished DROP COLUMN operator_site_id;
--rollback ALTER TABLE rounds_history_duplicates DROP COLUMN operator_site_id;
--rollback ALTER TABLE rounds_history DROP COLUMN operator_site_id;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive;
--rollback ALTER TABLE rounds_finished DROP COLUMN operator_site_id;
--rollback ALTER TABLE rounds_history DROP COLUMN operator_site_id;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive_ro;
--rollback ALTER TABLE rounds_finished DROP COLUMN operator_site_id;
--rollback ALTER TABLE rounds_history DROP COLUMN operator_site_id;
--rollback RESET search_path;