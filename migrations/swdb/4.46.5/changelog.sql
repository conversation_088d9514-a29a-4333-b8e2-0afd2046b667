--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset anastasia.kostyukova:2020-11-04-SWS-XXXX-start-release-4.46.5
--comment label for 4.46.5
select now();
--rollback select now();

--changeset anastasia.kostyukova:2020-11-04-SWS-22539-update-default-limits-config-for-slots
--comment Update default configurations, each game code represents it's schema configuration
SET search_path = swmanagement;
update schema_configurations set configuration='{"ARS": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "BNS": {"aligned": false, "stakeAll": [10, 20, 30, 50, 80, 100, 200, 300, 500, 800, 1000, 2000, 3000, 5000, 8000, 10000, 20000, 30000, 50000, 60000, 80000, 90000, 100000, 150000, 200000, 250000, 300000]}, "CNY": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "DKK": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "EUR": {"winMax": 500000, "stakeAll": [0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.08, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.8, 1, 2, 3, 5, 8, 10, 20, 30, 50, 60, 80, 90, 100, 150, 200, 250, 300], "defaultTotalStake": 1.5}, "HKD": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "HKR": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "MAD": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "MOP": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "MXN": {"aligned": true, "stakeAll": [0.01, 0.03, 0.05, 0.1, 0.2]}, "NOK": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "SEK": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "VEF": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "ZAR": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "ZMW": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03,0.04,0.05, 0.06]}, "RON": {"aligned": true, "stakeAll": [0.01, 0.02, 0.03, 0.04]}}'::jsonb
where schema_definition_id in (select distinct schema_definition_id from games where code in('sw_dr', 'sw_bb', 'sw_dosc7s'));
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback SELECT now();
--rollback RESET search_path;

