--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset emanuel-alin.raileanu:2024-11-06-SWS-46317
--comment Add new columns to the oauth tables
SET search_path = swmanagement;

ALTER TABLE oauth_clients ADD COLUMN redirect_uri varchar(255);
COMMENT ON COLUMN oauth_clients.redirect_uri IS 'The redirect URI of the client application';

ALTER TABLE oauth_authorization_codes ADD COLUMN session_id VARCHAR(255) UNIQUE;
COMMENT ON COLUMN oauth_authorization_codes.session_id IS 'The user session id';

ALTER TABLE oauth_refresh_tokens ADD COLUMN session_id VARCHAR(255) UNIQUE;
COMMENT ON COLUMN oauth_refresh_tokens.session_id IS 'The user session id';

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE oauth_refresh_tokens DROP COLUMN session_id;
--rollback ALTER TABLE oauth_authorization_codes DROP COLUMN session_id;
--rollback ALTER TABLE oauth_clients DROP COLUMN redirect_uri;
--rollback RESET search_path;

--changeset emanuel-alin.raileanu:2024-11-18-SWS-46683
--comment Add the main_domain_url colum to merchant_types
SET search_path = swmanagement;

ALTER TABLE merchant_types ADD COLUMN main_domain_url VARCHAR(255);
COMMENT ON TABLE merchant_types IS 'Main domain wallet adapter url';

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE merchant_types DROP COLUMN main_domain_url;
--rollback RESET search_path;

--changeset emanuel-alin.raileanu:2024-11-26-SWS-46567
--comment Set null on static domain pool deletion instead of cascading
SET search_path = swmanagement;

ALTER TABLE entities DROP CONSTRAINT entities_static_domain_pool_id_fkey;
ALTER TABLE entities ADD FOREIGN KEY (static_domain_pool_id) REFERENCES static_domain_pools ON DELETE SET NULL;

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entities DROP CONSTRAINT entities_static_domain_pool_id_fkey;
--rollback ALTER TABLE entities ADD FOREIGN KEY (static_domain_pool_id) REFERENCES static_domain_pools ON DELETE CASCADE;
--rollback RESET search_path;

--changeset vladimir.minakov:2024-11-22-SWS-46757
--comment Add lobby domain tables

SET search_path = swmanagement;

CREATE TABLE lobby_domains (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at timestamp without time zone DEFAULT NOW(),
    updated_at timestamp without time zone DEFAULT NOW()
);
COMMENT ON TABLE lobby_domains IS 'Lobby domain entities';
COMMENT ON COLUMN lobby_domains.name IS 'Domain name';
COMMENT ON COLUMN lobby_domains.is_active IS 'Flag that indicates whether the lobby domain is active and can be used';

CREATE TABLE static_domain_pools_lobby_domains (
    static_domain_pool_id INT NOT NULL REFERENCES static_domain_pools(id) ON DELETE CASCADE,
    lobby_domain_id INT NOT NULL REFERENCES lobby_domains(id) ON DELETE CASCADE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    PRIMARY KEY (static_domain_pool_id, lobby_domain_id)
);

CREATE INDEX idx_static_domain_pools_lobby_domains_lobby_domain_id ON static_domain_pools_lobby_domains USING btree (lobby_domain_id);
CREATE INDEX idx_static_domain_pools_lobby_domains_is_active ON static_domain_pools_lobby_domains USING btree (is_active);

COMMENT ON TABLE static_domain_pools_lobby_domains IS 'Connection table between static domain pools and lobby domains';
COMMENT ON COLUMN static_domain_pools_lobby_domains.is_active IS 'Flag that indicates whether the lobby domain is active and can be used';

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP TABLE static_domain_pools_lobby_domains;
--rollback DROP TABLE lobby_domains;
--rollback RESET search_path;


--changeset dmitriy.palaznik:2024-11-29-SWS-46883-add-bo-permissions-for-new-billing-bi-report
--comment Create new permission for the Billing BI report
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'bi:report:billing' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:billing' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["bi:report:billing", "keyentity:bi:report:billing"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:billing' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:billing' WHERE id = 1;
--rollback RESET search_path;
