--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2022-09-06_SWS-32820-include-lobbysessionid-to-spin_history
--comment Add lobby_session_id column in spins_history table
SET search_path = swmanagement;
ALTER TABLE spins_history ADD IF NOT EXISTS lobby_session_id VARCHAR(36);
ALTER TABLE spins_history_duplicates ADD IF NOT EXISTS lobby_session_id VARCHAR(36);
COMMENT ON COLUMN spins_history.lobby_session_id IS 'Optional player lobby session Id';
COMMENT ON COLUMN spins_history_duplicates.lobby_session_id IS 'Optional player lobby session Id';
RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE spins_history ADD IF NOT EXISTS lobby_session_id VARCHAR(36);
COMMENT ON COLUMN spins_history.lobby_session_id IS 'Optional player lobby session Id';
RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE spins_history ADD IF NOT EXISTS lobby_session_id VARCHAR(36);
COMMENT ON COLUMN spins_history.lobby_session_id IS 'Optional player lobby session Id';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE spins_history DROP IF EXISTS lobby_session_id;
--rollback ALTER TABLE spins_history_duplicates DROP IF EXISTS lobby_session_id;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive;
--rollback ALTER TABLE spins_history DROP IF EXISTS lobby_session_id;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive_ro;
--rollback ALTER TABLE spins_history DROP IF EXISTS lobby_session_id;
--rollback RESET search_path;

--changeset emanuel-alin.railean:2022-10-05_SWS-37286-support-currencies-per-jackpot
--comment Add support for baseCurrency and poolCurrencies for individual jackpots
SET search_path = swadapterstars;
ALTER TABLE jp_feeder ADD COLUMN base_currency CHAR(3);
COMMENT ON COLUMN jp_feeder.base_currency IS 'JP base currency';
ALTER TABLE jp_feeder ADD COLUMN pool_currencies JSONB;
COMMENT ON COLUMN jp_feeder.pool_currencies IS 'List of allowed pool currencies';
RESET search_path;

--rollback SET search_path = swadapterstars;
--rollback ALTER TABLE jp_feeder DROP COLUMN base_currency;
--rollback ALTER TABLE jp_feeder DROP COLUMN pool_currencies;
--rollback RESET search_path;
