--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset andrei.stefan:2023-01-17-SWS-38714-add-column-version-to-integration-test-reports
--comment Add 'version' field to 'integration_test_reports'
SET search_path TO swmanagement;
ALTER TABLE integration_test_reports ADD COLUMN version INTEGER;
COMMENT ON COLUMN integration_test_reports.version IS 'Report version';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE integration_test_reports DROP COLUMN version;
--rollback RESET search_path;
