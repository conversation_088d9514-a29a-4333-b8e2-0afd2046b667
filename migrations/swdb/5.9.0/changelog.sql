--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset andrei.stefan:2023-05-22-SWS-40208-login-audit
--comment Add mirror tables for login auditing

SET search_path = swmanagement;

CREATE TABLE IF NOT EXISTS audits_login (
    audit_id integer NOT NULL,
    entity_id integer NOT NULL,
    ts timestamp without time zone NOT NULL,
    audits_summary_id smallint,
    history jsonb,
    initiator_type enum_audits_initiator_type NOT NULL,
    initiator_name character varying(255) NOT NULL,
    ip inet NOT NULL,
    user_agent character varying(2048) NOT NULL,
    initiator_service_name character varying(255),
    audits_session_id uuid,
    initiator_issue_id character varying(255)
);

CREATE SEQUENCE audits_login_audit_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE audits_login_audit_id_seq OWNED BY audits_login.audit_id;

ALTER TABLE ONLY audits_login ALTER COLUMN audit_id SET DEFAULT nextval('audits_login_audit_id_seq'::regclass);
ALTER TABLE ONLY audits_login ADD CONSTRAINT audits_login_pkey PRIMARY KEY (audit_id) WITH (fillfactor='100');

CREATE INDEX idx_audits_login_entity_id_ts ON audits_login USING btree (entity_id, ts DESC) WITH (fillfactor='100');
CREATE INDEX IF NOT EXISTS idx_audits_login_ts ON audits_login (ts);

SELECT public.set_init_callback (
                        relation        => 'swmanagement.audits_login'::regclass,
                        callback        => 'public.pathman_callback(jsonb)' );

SELECT public.create_range_partitions (
                        parent_relid    => 'swmanagement.audits_login'::regclass,
                        expression      => 'ts',
                        start_value     => (SELECT date_trunc('week', COALESCE (min(ts), now()))::date FROM swmanagement.audits_login),
                        p_interval      => '7 days'::interval,
                        p_count         => (CASE WHEN EXISTS (SELECT 1 FROM swmanagement.audits_login) THEN NULL ELSE 1 END),
                        partition_data  => FALSE );

CREATE TABLE IF NOT EXISTS audits_login_session (
    id UUID PRIMARY KEY,
    entity_id INT NOT NULL,
    initiator_name VARCHAR(255),
    started_at timestamp without time zone NOT NULL DEFAULT NOW(),
    finished_at timestamp without time zone NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE audits_login_session IS 'The table contains info about audited session';
COMMENT ON COLUMN audits_login_session.entity_id IS 'Entity id';
COMMENT ON COLUMN audits_login_session.initiator_name IS 'User who id using the session';
COMMENT ON COLUMN audits_login_session.started_at IS 'Session start';
COMMENT ON COLUMN audits_login_session.finished_at IS 'Session finish';

CREATE INDEX IF NOT EXISTS idx_audits_login_session_started_at ON audits_login_session (started_at);

SELECT public.set_init_callback (
                        relation        => 'swmanagement.audits_login_session'::regclass,
                        callback        => 'public.pathman_callback(jsonb)' );

SELECT public.create_range_partitions (
                        parent_relid    => 'swmanagement.audits_login_session'::regclass,
                        expression      => 'started_at',
                        start_value     => (SELECT date_trunc('week', COALESCE (min(started_at), now()))::date FROM swmanagement.audits_login_session),
                        p_interval      => '7 days'::interval,
                        p_count         => (CASE WHEN EXISTS (SELECT 1 FROM swmanagement.audits_login_session) THEN NULL ELSE 1 END),
                        partition_data  => FALSE );

RESET search_path;
SET search_path = swmanagement_archive;

CREATE TABLE IF NOT EXISTS audits_login (
    audit_id integer NOT NULL,
    entity_id integer NOT NULL,
    ts timestamp without time zone NOT NULL,
    audits_summary_id smallint,
    history jsonb,
    initiator_type swmanagement.enum_audits_initiator_type NOT NULL,
    initiator_name character varying(255) NOT NULL,
    ip inet NOT NULL,
    user_agent character varying(2048) NOT NULL,
    initiator_service_name character varying(255),
    audits_session_id uuid,
    initiator_issue_id character varying(255)
);

ALTER TABLE ONLY audits_login ADD CONSTRAINT audits_login_pkey PRIMARY KEY (audit_id) WITH (fillfactor='100');

CREATE INDEX idx_audits_login_entity_id_ts ON audits_login USING btree (entity_id, ts DESC) WITH (fillfactor='100');
CREATE INDEX idx_audits_login_ts ON audits_login USING btree (ts) WITH (fillfactor='100');

CREATE TABLE IF NOT EXISTS audits_login_session (
    id UUID PRIMARY KEY,
    entity_id INT NOT NULL,
    initiator_name VARCHAR(255),
    started_at timestamp without time zone NOT NULL DEFAULT NOW(),
    finished_at timestamp without time zone NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE audits_login_session IS 'The table contains info about audited session';
COMMENT ON COLUMN audits_login_session.entity_id IS 'Entity id';
COMMENT ON COLUMN audits_login_session.initiator_name IS 'User who id using the session';
COMMENT ON COLUMN audits_login_session.started_at IS 'Session start';
COMMENT ON COLUMN audits_login_session.finished_at IS 'Session finish';

CREATE INDEX IF NOT EXISTS idx_audits_login_session_started_at ON audits_login_session (started_at);

RESET search_path;
SET search_path = swmanagement_archive_ro;

CREATE TABLE IF NOT EXISTS audits_login (
    audit_id integer NOT NULL,
    entity_id integer NOT NULL,
    ts timestamp without time zone NOT NULL,
    audits_summary_id smallint,
    history jsonb,
    initiator_type swmanagement.enum_audits_initiator_type NOT NULL,
    initiator_name character varying(255) NOT NULL,
    ip inet NOT NULL,
    user_agent character varying(2048) NOT NULL,
    initiator_service_name character varying(255),
    audits_session_id uuid,
    initiator_issue_id character varying(255)
);

ALTER TABLE ONLY audits_login ADD CONSTRAINT audits_login_pkey PRIMARY KEY (audit_id) WITH (fillfactor='100');

CREATE INDEX idx_audits_login_entity_id_ts ON audits_login USING btree (entity_id, ts DESC) WITH (fillfactor='100');
CREATE INDEX idx_audits_login_ts ON audits_login USING btree (ts) WITH (fillfactor='100');

CREATE TABLE IF NOT EXISTS audits_login_session (
    id UUID PRIMARY KEY,
    entity_id INT NOT NULL,
    initiator_name VARCHAR(255),
    started_at timestamp without time zone NOT NULL DEFAULT NOW(),
    finished_at timestamp without time zone NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE audits_login_session IS 'The table contains info about audited session';
COMMENT ON COLUMN audits_login_session.entity_id IS 'Entity id';
COMMENT ON COLUMN audits_login_session.initiator_name IS 'User who id using the session';
COMMENT ON COLUMN audits_login_session.started_at IS 'Session start';
COMMENT ON COLUMN audits_login_session.finished_at IS 'Session finish';

CREATE INDEX IF NOT EXISTS idx_audits_login_session_started_at ON audits_login_session (started_at);

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback SELECT public.drop_partitions(parent_relid => 'swmanagement.audits_login'::regclass);
--rollback DROP TABLE audits_login;
--rollback SELECT public.drop_partitions(parent_relid => 'swmanagement.audits_login_session'::regclass);
--rollback DROP TABLE audits_login_session;
--rollback RESET search_path;
--rollback SET search_path = swmanagement_archive;
--rollback DROP TABLE audits_login;
--rollback DROP TABLE audits_login_session;
--rollback RESET search_path;
--rollback SET search_path = swmanagement_archive_ro;
--rollback DROP TABLE audits_login;
--rollback DROP TABLE audits_login_session;
--rollback RESET search_path;

--changeset emanuel-alin.raileanu:2023-06-26-SWS-41019-new-jackpot-management-system
--comment Add new columns to support the new jackpot management system

SET search_path = swmanagement;

ALTER TABLE jurisdictions
    ADD COLUMN allowed_jackpot_configuration_level INTEGER DEFAULT 99
        CHECK (allowed_jackpot_configuration_level >= 0 AND allowed_jackpot_configuration_level <= 6 OR
               allowed_jackpot_configuration_level = 99);

COMMENT ON COLUMN jurisdictions.allowed_jackpot_configuration_level IS 'Allowed jackpot configuration level according to jurisdiction requirements';

RESET search_path;
SET search_path = swjackpot;

ALTER TABLE jp_instance
    ADD COLUMN jackpot_configuration_level INTEGER CHECK (jackpot_configuration_level > 0 AND jackpot_configuration_level <= 6);
COMMENT ON COLUMN jp_instance.jackpot_configuration_level IS 'Jackpot configuration level';

ALTER TABLE jp_instance ADD COLUMN entity_id INTEGER;
COMMENT ON COLUMN jp_instance.entity_id IS 'The entity id for which the jackpot configuration level applies';

ALTER TABLE jp_instance ADD COLUMN jurisdiction_code VARCHAR(6);
COMMENT ON COLUMN jp_instance.entity_id IS 'The jurisdiction code for which the jackpot configuration level applies';

RESET search_path;

--rollback SET search_path = swjackpot;
--rollback ALTER TABLE jp_instance DROP COLUMN jurisdiction_code;
--rollback ALTER TABLE jp_instance DROP COLUMN entity_id;
--rollback ALTER TABLE jp_instance DROP COLUMN jackpot_configuration_level;
--rollback RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE jurisdictions DROP COLUMN allowed_jackpot_configuration_level;
--rollback RESET search_path;

