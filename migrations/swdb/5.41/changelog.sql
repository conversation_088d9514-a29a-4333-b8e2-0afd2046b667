--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset emanuel-alin.raileanu:2024-10-10-SWS-46172
--comment Add falcon-oauth service tables: keys, clients, authorization_codes
SET search_path = swmanagement;

CREATE TABLE oauth_keys (
    id SERIAL PRIMARY KEY,
    private_key VARCHAR(4096) NOT NULL,
    public_key VARCHAR(2048) NOT NULL,
    key_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

COMMENT ON TABLE oauth_keys IS 'Table that stores RSA keys pairs used for OAuth purposes';
COMMENT ON COLUMN oauth_keys.private_key IS 'RSA Private Key';
COMMENT ON COLUMN oauth_keys.public_key IS 'RSA Public Key';
COMMENT ON COLUMN oauth_keys.key_id IS 'JSON Web Key ID used to sign tokens';
COMMENT ON COLUMN oauth_keys.created_at IS 'Record creation timestamp';

CREATE TABLE oauth_clients (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    client_id VARCHAR(255) NOT NULL UNIQUE,
    client_secret VARCHAR(255) NOT NULL UNIQUE,
    allowed_scopes JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE oauth_clients IS 'Table that stores Falcon OAuth Application Clients';
COMMENT ON COLUMN oauth_clients.name IS 'The name of the client';
COMMENT ON COLUMN oauth_clients.client_id IS 'The issued Client ID';
COMMENT ON COLUMN oauth_clients.client_secret IS 'The issued Client Secret';
COMMENT ON COLUMN oauth_clients.allowed_scopes IS 'The allowed token issuing scope of the client';
COMMENT ON COLUMN oauth_clients.created_at IS 'Record creation timestamp';
COMMENT ON COLUMN oauth_clients.updated_at IS 'Record update timestamp';

CREATE TABLE oauth_authorization_codes (
    id SERIAL PRIMARY KEY,
    code VARCHAR(255) UNIQUE NOT NULL,
    client_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    scopes JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    CONSTRAINT fk_client FOREIGN KEY (client_id) REFERENCES oauth_clients(id) ON DELETE CASCADE,
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

COMMENT ON TABLE oauth_authorization_codes IS 'Table that stores the temporary Falcon OAuth authorization codes';
COMMENT ON COLUMN oauth_authorization_codes.code IS 'The issued authorization code';
COMMENT ON COLUMN oauth_authorization_codes.client_id IS 'The id of the client pointing to clients.id';
COMMENT ON COLUMN oauth_authorization_codes.user_id IS 'The id of the user pointing to users.id';
COMMENT ON COLUMN oauth_authorization_codes.scopes IS 'The scopes of the authorization code';
COMMENT ON COLUMN oauth_authorization_codes.created_at IS 'Record creation timestamp';
COMMENT ON COLUMN oauth_authorization_codes.expires_at IS 'The expiration timestamp of the authorization code';

CREATE INDEX idx_oauth_authorization_codes_client_id ON oauth_authorization_codes USING btree (client_id);
CREATE INDEX idx_oauth_authorization_codes_user_id ON oauth_authorization_codes USING btree (user_id);
CREATE INDEX idx_oauth_authorization_codes_expires_at ON oauth_authorization_codes USING btree (expires_at);

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP TABLE oauth_authorization_codes;
--rollback DROP TABLE oauth_clients;
--rollback DROP TABLE oauth_keys;
--rollback RESET search_path;

--changeset emanuel-alin.raileanu:2024-10-11-SWS-46172
--comment Add the oauth_refresh_tokens table
SET search_path = swmanagement;

CREATE TABLE oauth_refresh_tokens (
    id SERIAL PRIMARY KEY,
    token VARCHAR(1024) NOT NULL UNIQUE,
    user_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    scopes JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_client FOREIGN KEY (client_id) REFERENCES oauth_clients(id) ON DELETE CASCADE
);

COMMENT ON TABLE oauth_refresh_tokens IS 'Table that stores issued refresh tokens';
COMMENT ON COLUMN oauth_refresh_tokens.token IS 'The stored refresh token';
COMMENT ON COLUMN oauth_refresh_tokens.user_id IS 'The id of the user pointing to users.id';
COMMENT ON COLUMN oauth_refresh_tokens.client_id IS 'The id of the client pointing to oauth_clients.id';
COMMENT ON COLUMN oauth_refresh_tokens.scopes IS 'The scopes of the refresh token';
COMMENT ON COLUMN oauth_refresh_tokens.created_at IS 'Record creation timestamp';
COMMENT ON COLUMN oauth_refresh_tokens.expires_at IS 'The expiration timestamp of the authorization code';


CREATE INDEX idx_oauth_refresh_tokens_client_id ON oauth_refresh_tokens USING btree (client_id);
CREATE INDEX idx_oauth_refresh_tokens_user_id ON oauth_refresh_tokens USING btree (user_id);
CREATE INDEX idx_oauth_refresh_tokens_expires_at ON oauth_refresh_tokens USING btree (expires_at);

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP TABLE oauth_refresh_tokens;
--rollback RESET search_path;
