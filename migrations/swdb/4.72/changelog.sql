--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset georgiy.kovrey:2021-11-01_MICGMG-172-Playcheck
--comment Update table to add column to support quick merchant search
SET search_path = swadaptermgm;
ALTER TABLE micgaming_rounds ADD COLUMN merchant_code character varying(255) NULL;

COMMENT ON COLUMN micgaming_rounds.merchant_code IS 'Skywinds merchant code.';

RESET search_path;
--rollback SET search_path = swadaptermgm;
--rollback ALTER TABLE micgaming_rounds DROP COLUMN merchant_code;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2021-11-03_DEVOPS-16279_new_tables_to_move_old_data_to_arch_tables endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Archive DB emulation on CDs(Dev/QA) environments. Joined tables payments, audits_session, remote_jp_contribution_log to clean up
DO $$
DECLARE   
    v_fs_name   TEXT;    
BEGIN
    SET client_min_messages TO INFO;
    -- Check for the foreign server
    BEGIN
        SELECT srvname FROM pg_foreign_server WHERE srvname ~* '^sw_hcl.+ro$' INTO STRICT v_fs_name;
    EXCEPTION
        WHEN no_data_found THEN
            RAISE INFO '[%] Foreign server not found. Will do emulation', clock_timestamp();
        WHEN too_many_rows THEN
            RAISE EXCEPTION '[%] Too many foreign servers. Can''t choose one!', clock_timestamp();
    END;
   
    IF (v_fs_name IS NULL) THEN -- Emulation (CD envs)
    
        DELETE FROM swsystem.partition_cleanup_config 
        WHERE tabrel::text IN ('swgameserver.archived_game_contexts', 'swadaptergos.ext_bet_win_history');
        
        /* sync the payments table structure in arch schema */
        DROP TABLE swmanagement_archive.payments CASCADE;
        CREATE TABLE swmanagement_archive.payments (LIKE swmanagement.payments INCLUDING ALL EXCLUDING DEFAULTS);
        PERFORM public.set_init_callback('swmanagement_archive.payments', 'pathman_callback(jsonb)');
        PERFORM public.create_range_partitions('swmanagement_archive.payments', 'start_date', date_trunc('day', now())::date, '7 days'::interval, 1, false);
        CREATE TABLE swmanagement_archive_ro.payments (LIKE swmanagement.payments INCLUDING ALL EXCLUDING DEFAULTS);
    
        CREATE TABLE swmanagement_archive.audits_session (LIKE swmanagement.audits_session INCLUDING ALL EXCLUDING DEFAULTS);
        PERFORM public.set_init_callback('swmanagement_archive.audits_session', 'pathman_callback(jsonb)');
        PERFORM public.create_range_partitions('swmanagement_archive.audits_session', 'started_at', date_trunc('day', now())::date, '7 days'::interval, 1, false);
        CREATE TABLE swmanagement_archive_ro.audits_session (LIKE swmanagement.audits_session INCLUDING ALL EXCLUDING DEFAULTS);
        
        CREATE TABLE IF NOT EXISTS swjackpot_archive_ro.remote_jp_contribution_log (LIKE swjackpot.remote_jp_contribution_log INCLUDING ALL EXCLUDING DEFAULTS);

    END IF;

    RESET client_min_messages;
END $$;

--rollback DO $$
--rollback BEGIN
--rollback     IF NOT EXISTS (SELECT 1 FROM pg_foreign_server WHERE srvname ~* '^sw_hcl.+ro$')
--rollback     THEN -- Emulation (CD envs) 
--rollback            
--rollback         INSERT INTO swsystem.partition_cleanup_config (tabrel, dt_col, lifespan) VALUES
--rollback             ('swgameserver.archived_game_contexts'::regclass,'created_at','1 MONTH'),
--rollback             ('swadaptergos.ext_bet_win_history'::regclass,'inserted_at','1 MONTH');
--rollback         
--rollback         INSERT INTO swmanagement.payments SELECT * FROM swmanagement_archive.payments;
--rollback         TRUNCATE swmanagement_archive.payments;
--rollback         DROP TABLE swmanagement_archive_ro.payments CASCADE;
--rollback     
--rollback         INSERT INTO swmanagement.audits_session SELECT * FROM swmanagement_archive.audits_session;
--rollback         DROP TABLE swmanagement_archive_ro.audits_session CASCADE;
--rollback         DROP TABLE swmanagement_archive.audits_session CASCADE;
--rollback 
--rollback     END IF;
--rollback END $$;

--changeset oleg.rudko:2021-11-19-SWS-31432-create-new-permission-for-the-unfinished-rounds-bi-report
--comment Add new permissions
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:unfinished-rounds' where id = 1;
UPDATE roles SET permissions = permissions - 'bi:report:unfinished-rounds' where id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:bi:report:unfinished-rounds", "bi:report:unfinished-rounds"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:unfinished-rounds' where id = 1;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:unfinished-rounds' where id = 1;
--rollback RESET search_path;