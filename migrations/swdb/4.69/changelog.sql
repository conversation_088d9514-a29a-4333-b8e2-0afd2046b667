--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset emanuel-alin.railean:2021-09-16-SWS-30238-add-external-game-id-column
--comment add external_game_id column to the entity_games table
SET search_path = swmanagement;
ALTER TABLE entity_games ADD COLUMN external_game_id varchar(255);
COMMENT ON COLUMN entity_games.external_game_id IS 'The external game id provided by the operator';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entity_games DROP COLUMN external_game_id;
--rollback RESET search_path;
