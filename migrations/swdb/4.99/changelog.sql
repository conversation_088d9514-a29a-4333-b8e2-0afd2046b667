--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset pavel.shamshurov:2022-11-18-SWS-33360-drop-entity-settings-table
--comment drop entity settings table
--validCheckSum: 7:94eb5c713754019b6c691f59a3bbc7e6
SET search_path TO swmanagement;
DROP TABLE IF EXISTS entity_settings;
RESET search_path;
--rollback SELECT NOW();

--changeset emanuel-alin.raileanu:2022-12-01-SWS-37984-contribution-and-win-status
--comment add status column
SET search_path = swjackpot;
CREATE TYPE enum_jackpot_operation_status AS ENUM ('pending', 'resolved', 'rejected');
ALTER TABLE jp_contribution_log ADD COLUMN IF NOT EXISTS status enum_jackpot_operation_status;
COMMENT ON COLUMN jp_contribution_log.status IS 'JP contribution status';
ALTER TABLE jp_win_log ADD COLUMN IF NOT EXISTS status enum_jackpot_operation_status;
COMMENT ON COLUMN jp_win_log.status IS 'JP win status';
RESET search_path;

SET search_path = swjackpot_archive;
ALTER TABLE jp_contribution_log ADD COLUMN IF NOT EXISTS status swjackpot.enum_jackpot_operation_status;
COMMENT ON COLUMN jp_contribution_log.status IS 'JP contribution status';
RESET search_path;

SET search_path = swjackpot_archive_ro;
ALTER TABLE jp_contribution_log ADD COLUMN IF NOT EXISTS status swjackpot.enum_jackpot_operation_status;
COMMENT ON COLUMN jp_contribution_log.status IS 'JP contribution status';
RESET search_path;
--rollback SET search_path = swjackpot_archive_ro;
--rollback ALTER TABLE jp_contribution_log DROP COLUMN status;
--rollback RESET search_path;
--rollback SET search_path = swjackpot_archive;
--rollback ALTER TABLE jp_contribution_log DROP COLUMN status;
--rollback RESET search_path;
--rollback SET search_path = swjackpot;
--rollback ALTER TABLE jp_win_log DROP COLUMN status;
--rollback ALTER TABLE jp_contribution_log DROP COLUMN status;
--rollback DROP TYPE IF EXISTS enum_jackpot_operation_status;
--rollback RESET search_path;

--changeset valdis.akmens:2022-12-15-SWDB-291-improve-aggr_rounds_external endDelimiter:# stripComments:false
--comment Improve monitoring.aggr_rounds_external
SET search_path TO monitoring;

ALTER FUNCTION fnc_aggr_rounds() RENAME TO fnc_aggr_rounds_before_4_99_0;
ALTER FUNCTION fnc_aggr_rounds_before_4_99_0 SET SCHEMA swbackup;

ALTER TABLE aggr_rounds_external ADD IF NOT EXISTS brand_id integer;
ALTER TABLE aggr_rounds_external ADD IF NOT EXISTS game_code VARCHAR(255);

CREATE OR REPLACE FUNCTION fnc_aggr_rounds()
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/********************************************************************************
    Object Name:   fnc_aggr_rounds
    Purpose    :   Aggregate aggr_rounds table. Table is used for monitoring tools.
    History    :
        1.0.0
            Date    :  May 20, 2020
            Authors : Valdis Akmens
            Notes   : Release (SWDB-106)
        1.0.1
            Date    :  Dec 15, 2020
            Authors : Valdis Akmens
            Notes   : Update monitoring aggregation (DEVOPS-11755)
        1.0.2
            Date    : Oct 12, 2022
            Authors : Timur Luchkin
            Notes   : Add ITG game provider to the aggr_rounds_external (DEVOPS-20919)
        1.0.3
            Date    : Dec 15, 2022
            Authors : Valdis Akmens
            Notes   : Improve monitoring.aggr_rounds_external (SWDB-291)

    Sample run:
      SELECT * FROM monitoring.fnc_aggr_rounds();
********************************************************************************/
DECLARE
    v_lock_id               INTEGER := 'monitoring.aggr_rounds'::regclass::integer;
    v_max_finished_at       TIMESTAMP;
    v_min_finished_at       TIMESTAMP;
    v_counter               BIGINT;
    v_off_users             VARCHAR[]:='{"kafka_offloader", "redis_game_offloader"}';
    v_lower_limit           TIMESTAMP;
    v_upper_limit           TIMESTAMP;
    v_huge_interval         INTERVAL:= '10 minutes'::INTERVAL;
    v_partiton_prune        TIMESTAMP;
    v_lower_limit_prev      TIMESTAMP;
    v_upper_limit_prev      TIMESTAMP;
    v_exec_sql              VARCHAR;
BEGIN

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job started'; RETURN NEXT;

    /* Check if MDB */
    IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
    END IF;

    /* Check if another job is running*/
    IF NOT (SELECT pg_try_advisory_xact_lock(v_lock_id)) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Another function is running. Skip'; RETURN NEXT;
        RETURN;
    END IF;

    v_lower_limit:=(SELECT COALESCE(MAX(max_finished_at),date_trunc('MINUTE',now())) FROM monitoring.aggr_rounds);
    v_upper_limit:=(SELECT date_trunc('MINUTE', ts_upper_limit) + FLOOR(date_part('SECOND', ts_upper_limit))::INTEGER / 20 * interval '20 sec' AS ts_upper_20sec_limit
                    FROM  (
                                SELECT LEAST((SELECT MIN(xact_start)
                                                FROM pg_stat_activity sa
                                                WHERE state <> 'idle'::text
                                                AND usename = ANY(v_off_users)
                                            ), Now() )::TIMESTAMP AS ts_upper_limit
                            ) AS x);
    v_partiton_prune:=(SELECT date_trunc('MINUTE',now()) - v_huge_interval);

        /* Automatically prevent too huge intervals */
    IF (v_upper_limit - v_lower_limit) > v_huge_interval THEN
        v_lower_limit := v_upper_limit - v_huge_interval;
    END IF;

    v_lower_limit_prev:= v_lower_limit - '40 SECONDS'::INTERVAL;
    v_upper_limit_prev:= v_lower_limit;
    --RAISE INFO 'v_lower_limit = % ; v_upper_limit = % ; v_lower_limit_prev= % ; v_upper_limit_prev = %', v_lower_limit,v_upper_limit,v_lower_limit_prev,v_upper_limit_prev;
    v_exec_sql:= '
    WITH cte_xrates AS (
            SELECT r.currency_code, r.rate
            FROM swmanagement.currency_rates AS r
            JOIN (
                    SELECT currency_code, MAX(rate_date) AS rate_date
                    FROM swmanagement.currency_rates
                    WHERE rate_date <= CURRENT_DATE
                    GROUP BY currency_code
                )
                    AS m ON r.currency_code = m.currency_code AND r.rate_date = m.rate_date
            ORDER BY r.currency_code, r.rate_date DESC
        ), cte_prev AS (
            SELECT DISTINCT x.round_id
            FROM swmanagement.spins_history AS x
            WHERE
                x.ts > '''||v_lower_limit_prev||'''
            AND x.ts < '''||v_upper_limit_prev||'''
        ),
    cte_insert AS (
        INSERT INTO monitoring.aggr_rounds (ts_period, brand_id, game_code, player_code, currency, is_test_round, is_test_brand, device_id, rounds_cnt,rounds_fin_cnt, events_cnt, sessions_cnt, max_unload_lag, bet_sum_base, win_sum_base, jp_contrib_sum_base, jp_win_sum_base, bet_sum, win_sum, jp_contrib_sum, jp_win_sum, max_finished_at)
        SELECT date_trunc(''MINUTE'', h.ts) + FLOOR(date_part(''SECOND'', h.ts))::INTEGER / 20 * interval ''20 sec'' AS ts_20sec
            ,h.brand_id                                              AS brand_id
            ,h.game_code                                             AS game_code
            ,h.player_code                                           AS player_code
            ,h.currency                                              AS currency_code
            ,h.test                                                  AS is_test
            ,e.is_test                                               AS is_test_brand
            ,h.device_id                                             AS device
            --
            ,COALESCE(COUNT(DISTINCT
                    CASE WHEN
                            NOT EXISTS(SELECT pr.round_id
                                        FROM cte_prev AS pr
                                        WHERE pr.round_id = h.round_id
                                        )
                        THEN
                        h.round_id
                        END
                    ),0)                                              AS rounds_cnt
            ,COALESCE(COUNT(DISTINCT
                    CASE WHEN h.round_ended = TRUE THEN h.round_id END
                    ),0)                                              AS rounds_fin_cnt
            ,COALESCE(COUNT(*)::BIGINT,0)                             AS events_cnt
            ,COALESCE(COUNT(DISTINCT
                    CASE WHEN
                            NOT EXISTS(SELECT pr_2.round_id
                                        FROM cte_prev AS pr_2
                                        WHERE pr_2.round_id = h.round_id
                                        )
                        THEN
                        h.session_id
                        END
                    ),0)                                                    AS sessions_cnt
            ,MAX(GREATEST(h.inserted_at, h.ts) - h.ts)                      AS max_unload_lag
            --
            ,COALESCE(SUM(h.bet * xr.rate)::NUMERIC(20,2),0)                 AS bet_sum_base
            ,COALESCE(SUM(h.win * xr.rate)::NUMERIC(20,2),0)                 AS win_sum_base
            ,COALESCE(SUM(h.total_jp_contribution * xr.rate)::NUMERIC(20,2),0)     AS jp_contrib_sum_base
            ,COALESCE(SUM(h.total_jp_win * xr.rate)::NUMERIC(20,2),0)              AS jp_win_sum_base
            --
            ,COALESCE(SUM(h.bet),0)                                         AS bet_sum
            ,COALESCE(SUM(h.win),0)                                         AS win_sum
            ,COALESCE(SUM(h.total_jp_contribution),0)                       AS jp_contrib_sum
            ,COALESCE(SUM(h.total_jp_win),0)                                AS jp_win_sum
            --
            ,MAX(h.inserted_at)                                             AS max_finished_at
        FROM   swmanagement.spins_history AS h
        JOIN   swmanagement.entities        AS e ON e.id = h.brand_id
        JOIN   cte_xrates                   AS xr ON h.currency = xr.currency_code
        WHERE
            h.ts >= '''||v_partiton_prune||'''
        AND COALESCE(h.inserted_at, h.ts) > '''||v_lower_limit||'''
        AND COALESCE(h.inserted_at, h.ts) < '''||v_upper_limit||'''
        GROUP BY
            date_trunc(''MINUTE'', h.ts) + FLOOR(date_part(''SECOND'', h.ts))::INTEGER / 20 * interval ''20 sec''
            ,h.brand_id
            ,h.game_code
            ,h.player_code
            ,h.currency
            ,h.test
            ,e.is_test
            ,h.device_id
        RETURNING *
        )
    SELECT MAX(max_finished_at), MIN(max_finished_at),COUNT(*)
    FROM cte_insert ';

    --RAISE INFO 'v_exec_sql: %', v_exec_sql;

    EXECUTE v_exec_sql
    INTO v_max_finished_at, v_min_finished_at,v_counter;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "aggr_rounds" finished. Rows processed: '||quote_nullable(COALESCE(v_counter, 0))||' Timeframe processed: ('||quote_nullable(COALESCE(v_min_finished_at,v_lower_limit))||'; '||quote_nullable(COALESCE(v_max_finished_at,v_upper_limit))||')'; RETURN NEXT;

    v_exec_sql:='
    WITH cte_insert AS (
        INSERT INTO monitoring.aggr_rounds_external (ts_period, brand_id, game_code, player_code,is_test_round, game_provider, rounds_cnt)
        SELECT date_trunc(''MINUTE'', inserted_at) + FLOOR(date_part(''SECOND'', inserted_at))::INTEGER / 20 * interval ''20 sec'' AS ts_20sec
            ,brand_id
            ,game_code
            ,player_code
            ,is_test
            ,CASE game_provider_code
                WHEN ''IG'' THEN ''ig''
                WHEN ''ITG'' THEN ''itg''
                ELSE ''ext_other''
            END::VARCHAR(20)             AS group_type
            ,Count(Distinct(round_id))    AS rounds_cnt
        FROM   swadaptergos.ext_bet_win_history
        WHERE
            inserted_at >  '''||v_lower_limit||'''::TIMESTAMP
        AND  inserted_at <  '''||v_upper_limit||'''::TIMESTAMP
        AND is_rollbacked = FALSE
        GROUP BY date_trunc(''MINUTE'', inserted_at) + FLOOR(date_part(''SECOND'', inserted_at))::INTEGER / 20 * interval ''20sec''
            ,brand_id
            ,game_code
            ,player_code
            ,is_test
            ,CASE game_provider_code
                WHEN ''IG'' THEN ''ig''
                WHEN ''ITG'' THEN ''itg''
                ELSE ''ext_other''
            END
        RETURNING *
        )
    SELECT COUNT(*)
    FROM cte_insert ';
    EXECUTE v_exec_sql
    INTO v_counter;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "aggr_rounds_external" finished. Rows processed: '||quote_nullable(COALESCE(v_counter, 0)); RETURN NEXT;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job finished'; RETURN NEXT;
    RETURN;
END;
$function$;

--rollback SET search_path TO monitoring;
--rollback DROP FUNCTION IF EXISTS fnc_aggr_rounds();
--rollback ALTER FUNCTION swbackup.fnc_aggr_rounds_before_4_99_0 SET SCHEMA swmanagement;
--rollback ALTER FUNCTION fnc_aggr_rounds_before_4_99_0 () RENAME TO fnc_aggr_rounds;
--rollback ALTER TABLE aggr_rounds_external DROP IF EXISTS brand_id;
--rollback ALTER TABLE aggr_rounds_external DROP IF EXISTS game_code;
--rollback RESET search_path;
