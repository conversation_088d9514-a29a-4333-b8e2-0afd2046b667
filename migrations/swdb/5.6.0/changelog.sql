--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset emanuel-alin.raileanu:2023-05-19-SWS-40596-increase-lobby_session_id-length
--comment Increase the length of lobby_session_id
SET search_path = swmanagement;
ALTER TABLE spins_history ALTER COLUMN lobby_session_id TYPE VARCHAR(500);
ALTER TABLE spins_history_duplicates ALTER COLUMN lobby_session_id TYPE VARCHAR(500);
RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE spins_history ALTER COLUMN lobby_session_id TYPE VARCHAR(500);
RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE spins_history ALTER COLUMN lobby_session_id TYPE VARCHAR(500);
RESET search_path;
--rollback SET search_path = swmanagement_archive_ro;
--rollback ALTER TABLE spins_history ALTER COLUMN lobby_session_id TYPE VARCHAR(255);
--rollback RESET search_path;
--rollback SET search_path = swmanagement_archive;
--rollback ALTER TABLE spins_history ALTER COLUMN lobby_session_id TYPE VARCHAR(255);
--rollback RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE spins_history_duplicates ALTER COLUMN lobby_session_id TYPE VARCHAR(255);
--rollback ALTER TABLE spins_history ALTER COLUMN lobby_session_id TYPE VARCHAR(255);
--rollback RESET search_path;
