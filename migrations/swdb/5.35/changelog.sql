--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset dmitriy.palaznik:2024-07-15-SWS-45090-pariplay-incorrect-values-for-EndRound-request
--comment Add a new column 'end_of_round' for bet_win_history table
SET search_path TO swmanagement;

ALTER TABLE bet_win_history ADD COLUMN end_of_round BOOLEAN;
COMMENT ON COLUMN bet_win_history.end_of_round IS 'End of round of bet win history';

RESET search_path;

--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE bet_win_history DROP COLUMN end_of_round;
--rollback RESET search_path;

--changeset dmitriy.palaznik:2024-07-19-SWS-44822-pariplay-delete-bet_win_histories-table
--comment Drop obsolete bet_win_histories table
SET search_path TO swmanagement;

DROP TABLE IF EXISTS bet_win_histories;

RESET search_path;

--rollback SET search_path TO swmanagement;
--rollback RESET search_path;