--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset dmitriy.palaznik:2021-08-11_SWS-27084-flat-reports-permissions
--comment Add permissions for flat reports API to master
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'flat-reports' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["flat-reports"]'::jsonb WHERE id = 1;
UPDATE roles SET permissions = permissions - 'flat-reports:view' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["flat-reports:view"]'::jsonb WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:flat-reports' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:flat-reports"]'::jsonb WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:flat-reports:view' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:flat-reports:view"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'flat-reports' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'flat-reports:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:flat-reports' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:flat-reports:view' WHERE id = 1;
--rollback RESET search_path;

--changeset georgiy.kovrey:2021-08-20_MICGMG-12-Basic-Bet
--comment create role, schema
SET search_path = swsystem;
   CREATE USER swadaptermgm WITH PASSWORD 'changeme';
   CREATE SCHEMA swadaptermgm;
   ALTER SCHEMA swadaptermgm OWNER TO swsystem;
   GRANT USAGE ON SCHEMA swadaptermgm TO swadaptermgm;
   ALTER DEFAULT PRIVILEGES IN SCHEMA swadaptermgm GRANT SELECT,INSERT,UPDATE,DELETE ON TABLES TO swadaptermgm;
   ALTER DEFAULT PRIVILEGES IN SCHEMA swadaptermgm GRANT ALL ON SEQUENCES TO swadaptermgm;
   ALTER DEFAULT PRIVILEGES IN SCHEMA swadaptermgm GRANT EXECUTE ON FUNCTIONS TO swadaptermgm;
   ALTER DEFAULT PRIVILEGES IN SCHEMA swadaptermgm GRANT USAGE ON TYPES TO swadaptermgm;
RESET search_path;

--rollback SET search_path = swsystem;
--rollback    DROP OWNED BY swadaptermgm;
--rollback    DROP SCHEMA swadaptermgm;
--rollback    DROP USER swadaptermgm;
--rollback RESET search_path;

--changeset georgiy.kovrey:2021-08-11_MICGMG-12-Basic-Bet
--comment Create table to store rounds
SET search_path = swadaptermgm;
CREATE TABLE micgaming_rounds (
    operator_round_id character varying(64) NOT NULL,
    sw_round_id character varying(16) NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    PRIMARY KEY (sw_round_id, operator_round_id)
);

COMMENT ON TABLE micgaming_rounds IS 'Microgaming external round Ids';
COMMENT ON COLUMN micgaming_rounds.operator_round_id IS 'Microgamings external  round id.';
COMMENT ON COLUMN micgaming_rounds.sw_round_id IS 'Skywind internal round id';
COMMENT ON COLUMN micgaming_rounds.created_at IS 'Created at date';
COMMENT ON COLUMN micgaming_rounds.updated_at IS 'Updated at date';

RESET search_path;
--rollback SET search_path = swadaptermgm;
--rollback DROP TABLE micgaming_rounds;
--rollback RESET search_path;


--changeset aleh.rudzko:2021-09-03_ols-limit-filters-split-filters
--comment OLS Limit Filters - Split filters supported flag into two flags
SET search_path = swmanagement;
UPDATE games SET features = features || '{"decreaseMaxBetSupported": true, "increaseMinBetSupported": true}' WHERE features -> 'isLimitFiltersSupported' = 'true';
UPDATE games SET features = features - 'isLimitFiltersSupported' WHERE features -> 'isLimitFiltersSupported' = 'true';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE games SET features = features || '{"isLimitFiltersSupported": true}' WHERE features -> 'decreaseMaxBetSupported' = 'true' or features -> 'increaseMinBetSupported' = 'true';
--rollback UPDATE games SET features = features - 'decreaseMaxBetSupported' WHERE features -> 'decreaseMaxBetSupported' = 'true';
--rollback UPDATE games SET features = features - 'increaseMinBetSupported' WHERE features -> 'increaseMinBetSupported' = 'true';
--rollback RESET search_path;

--changeset aleh.rudzko:2021-09-08_ols-limit-filters-split-filters-backward-compatibility
--comment Returns isLimitFiltersSupported
SET search_path = swmanagement;
UPDATE games SET features = features || '{"isLimitFiltersSupported": true}' WHERE features -> 'decreaseMaxBetSupported' = 'true' or features -> 'increaseMinBetSupported' = 'true';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE games SET features = features - 'isLimitFiltersSupported' WHERE features -> 'isLimitFiltersSupported' = 'true';
--rollback RESET search_path;