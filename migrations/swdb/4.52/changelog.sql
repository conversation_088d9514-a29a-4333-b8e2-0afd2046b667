--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset maksim.puzikov:2021-01-12-SWS-XXXX-start-release-4.52.0
--comment label for 4.52.0
select now();
--rollback select now();


--changeset aleh.rudzko:2021-01-14-SWS-23092-ols-support-max-exposure
--comment add new field to game group filter - max_exposure
SET search_path = swmanagement;
ALTER TABLE game_group_filters ADD COLUMN IF NOT EXISTS max_exposure NUMERIC;
COMMENT ON COLUMN game_group_filters.max_exposure IS 'Max exposure is maxTotalBet multiply highestWin';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE game_group_filters DROP COLUMN IF EXISTS max_exposure;
--rollback RESET search_path;