--liquibase formatted sql


--changeset nikita.senko:2021-01-29-SWS-23908-create-table-players-info
--comment create table 'players_info'
SET search_path = swmanagement;
CREATE SEQUENCE IF NOT EXISTS players_info_id_seq;
CREATE TABLE IF NOT EXISTS players_info (
  id bigint NOT NULL DEFAULT nextval('players_info_id_seq'),
  brand_id INTEGER NOT NULL,
  player_code VARCHAR(255) NOT NULL,
  nickname VARCHAR(15),
  is_vip BOOLEAN  NOT NULL DEFAULT false,
  is_tracked BOOLEAN  NOT NULL DEFAULT false,
  is_chat_block BOOLEAN  NOT NULL DEFAULT false,
  is_merchant_player BOOLEAN  NOT NULL,
  created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now(),
  PRIMAR<PERSON>EY (id),
  FOR<PERSON><PERSON><PERSON> KEY (brand_id) REFERENCES entities (id) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_player_info_player_code ON players_info (player_code);
CREATE UNIQUE INDEX IF NOT EXISTS idx_player_info_nickname_brand_id_player_code ON players_info (brand_id, player_code);
CREATE INDEX IF NOT EXISTS idx_player_info_brand_id_nickname ON players_info (brand_id, nickname);
COMMENT ON TABLE players_info IS 'Table for adding player info';
COMMENT ON COLUMN players_info.brand_id IS 'Reference to entity id';
COMMENT ON COLUMN players_info.player_code IS 'Player code';
COMMENT ON COLUMN players_info.nickname IS 'Player nickname';
COMMENT ON COLUMN players_info.is_vip IS 'Player vip flag';
COMMENT ON COLUMN players_info.is_tracked IS 'Player tracked';
COMMENT ON COLUMN players_info.is_chat_block IS 'Player bloc chat flag';
COMMENT ON COLUMN players_info.is_merchant_player IS 'Merchant or not merchanr player';
COMMENT ON COLUMN players_info.created_at IS 'Addition time of event';
COMMENT ON COLUMN players_info.updated_at IS 'Modification time of event';

INSERT INTO players_info (brand_id, player_code, nickname, is_vip, created_at, updated_at, is_tracked, is_chat_block, is_merchant_player)
SELECT brand_id, player_code, nickname, COALESCE(is_vip, false), now(), now(), false, false, true FROM merchant_player_info
WHERE merchant_player_info.nickname IS NOT NULL OR merchant_player_info.is_vip IS NOT NULL;

INSERT INTO players_info (brand_id, player_code, nickname, is_vip, created_at, updated_at, is_tracked, is_chat_block, is_merchant_player)
SELECT brand_id, code, nickname, COALESCE(is_vip, false), now(), now(), false, false, false FROM players
WHERE players.nickname IS NOT NULL OR players.is_vip = true
ON CONFLICT (brand_id, player_code) DO NOTHING;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP TABLE IF EXISTS players_info;
--rollback DROP SEQUENCE IF EXISTS players_info_id_seq;
--rollback RESET search_path;


--changeset valdis.akmens:2021-02-01-SWS-24755-triggers-for-update-nickname-is_vip endDelimiter:# stripComments:false
--comment Updates players_info table by triggers

SET search_path = swmanagement;

DROP TRIGGER IF EXISTS trg_players_update_players_info ON players;
CREATE OR REPLACE FUNCTION fnc_update_players_info()
    RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO players_info(brand_id, player_code, nickname, is_vip, is_merchant_player)
    VALUES(NEW.brand_id, NEW.code,NEW.nickname,COALESCE(NEW.is_vip, FALSE), FALSE)
    ON CONFLICT(brand_id, player_code) DO
    UPDATE SET
        nickname    = EXCLUDED.nickname,
        is_vip      = EXCLUDED.is_vip,
        updated_at  = NOW()
    ;
    RETURN NEW;
END;
$$;
CREATE TRIGGER trg_players_update_players_info AFTER INSERT OR UPDATE ON players FOR EACH ROW EXECUTE PROCEDURE fnc_update_players_info();


DROP TRIGGER IF EXISTS trg_merchant_player_info_update_players_info ON merchant_player_info;
CREATE OR REPLACE FUNCTION fnc_update_merchant_player_info()
    RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO players_info(brand_id, player_code, nickname, is_vip, is_merchant_player)
    VALUES(NEW.brand_id, NEW.player_code,NEW.nickname,COALESCE(NEW.is_vip,FALSE), TRUE)
    ON CONFLICT(brand_id, player_code) DO
    UPDATE SET
        nickname    = EXCLUDED.nickname,
        is_vip      = EXCLUDED.is_vip,
        updated_at  = NOW()
    ;
    RETURN NEW;
END;
$$;
CREATE TRIGGER trg_merchant_player_info_update_players_info AFTER INSERT OR UPDATE ON merchant_player_info FOR EACH ROW EXECUTE PROCEDURE fnc_update_merchant_player_info();

RESET search_path;

--rollback SET search_path TO swmanagement;
--rollback DROP TRIGGER IF EXISTS trg_players_update_players_info ON players;
--rollback DROP FUNCTION IF EXISTS fnc_update_players_info;
--rollback DROP TRIGGER IF EXISTS trg_merchant_player_info_update_players_info ON merchant_player_info;
--rollback DROP FUNCTION IF EXISTS fnc_update_merchant_player_info;
--rollback RESET search_path;


--changeset valdis.akmens:2021-02-09-SWS-24995-triggers-for-update-nickname-is_vip endDelimiter:# stripComments:false
--comment Updates players_info table by triggers
SET search_path = swmanagement;

DROP TRIGGER IF EXISTS trg_players_update_players_info ON players;
CREATE OR REPLACE FUNCTION fnc_update_players_info()
    RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN

        IF (TG_OP = 'INSERT') THEN
            INSERT INTO players_info(brand_id, player_code, nickname, is_vip, is_merchant_player)
            VALUES(NEW.brand_id, NEW.code,NEW.nickname,COALESCE(NEW.is_vip, FALSE), FALSE)
            ON CONFLICT(brand_id, player_code) DO
            UPDATE SET
                nickname    = EXCLUDED.nickname,
                is_vip      = EXCLUDED.is_vip,
                updated_at  = NOW();
            RETURN NEW;
        ELSIF (TG_OP = 'UPDATE') THEN
            IF ( NEW.nickname IS DISTINCT FROM OLD.nickname ) THEN
                INSERT INTO players_info(brand_id, player_code, nickname, is_vip, is_merchant_player)
                VALUES(NEW.brand_id, NEW.code,NEW.nickname,COALESCE(NEW.is_vip, FALSE), FALSE)
                ON CONFLICT(brand_id, player_code) DO
                UPDATE SET
                    nickname    = EXCLUDED.nickname,
                    updated_at  = NOW();
            END IF;
            IF ( NEW.is_vip IS DISTINCT FROM OLD.is_vip ) THEN
                INSERT INTO players_info(brand_id, player_code, nickname, is_vip, is_merchant_player)
                VALUES(NEW.brand_id, NEW.code,NEW.nickname,COALESCE(NEW.is_vip, FALSE), FALSE)
                ON CONFLICT(brand_id, player_code) DO
                UPDATE SET
                    is_vip    = EXCLUDED.is_vip,
                    updated_at  = NOW();
            END IF;
            RETURN NEW;
        END IF;


END;
$$;
CREATE TRIGGER trg_players_update_players_info AFTER INSERT OR UPDATE OF nickname, is_vip ON players FOR EACH ROW  EXECUTE PROCEDURE fnc_update_players_info();


DROP TRIGGER IF EXISTS trg_merchant_player_info_update_players_info ON merchant_player_info;
CREATE OR REPLACE FUNCTION fnc_update_merchant_player_info()
    RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
        IF (TG_OP = 'INSERT') THEN
            INSERT INTO players_info(brand_id, player_code, nickname, is_vip, is_merchant_player)
            VALUES(NEW.brand_id, NEW.player_code,NEW.nickname,COALESCE(NEW.is_vip, FALSE), TRUE)
            ON CONFLICT(brand_id, player_code) DO
            UPDATE SET
                nickname    = EXCLUDED.nickname,
                is_vip      = EXCLUDED.is_vip,
                updated_at  = NOW()    ;
            RETURN NEW;
        ELSIF (TG_OP = 'UPDATE') THEN
            IF ( NEW.nickname IS DISTINCT FROM OLD.nickname ) THEN
                INSERT INTO players_info(brand_id, player_code, nickname, is_vip, is_merchant_player)
                VALUES(NEW.brand_id, NEW.player_code,NEW.nickname,COALESCE(NEW.is_vip, FALSE), TRUE)
                ON CONFLICT(brand_id, player_code) DO
                UPDATE SET
                    nickname    = EXCLUDED.nickname,
                    updated_at  = NOW();
            END IF;
            IF ( NEW.is_vip IS DISTINCT FROM OLD.is_vip ) THEN
                INSERT INTO players_info(brand_id, player_code, nickname, is_vip, is_merchant_player)
                VALUES(NEW.brand_id, NEW.player_code,NEW.nickname,COALESCE(NEW.is_vip, FALSE), TRUE)
                ON CONFLICT(brand_id, player_code) DO
                UPDATE SET
                    is_vip    = EXCLUDED.is_vip,
                    updated_at  = NOW();
            END IF;
            RETURN NEW;
        END IF;

END;
$$;
CREATE TRIGGER trg_merchant_player_info_update_players_info AFTER INSERT OR UPDATE OF nickname, is_vip ON merchant_player_info FOR EACH ROW EXECUTE PROCEDURE fnc_update_merchant_player_info();

RESET search_path;

--rollback SET search_path TO swmanagement;
--rollback DROP TRIGGER IF EXISTS trg_players_update_players_info ON players;
--rollback DROP FUNCTION IF EXISTS fnc_update_players_info;
--rollback DROP TRIGGER IF EXISTS trg_merchant_player_info_update_players_info ON merchant_player_info;
--rollback DROP FUNCTION IF EXISTS fnc_update_merchant_player_info;
--rollback RESET search_path;
