--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset oleg.rudko:2021-10-15_SWS_30748_fix_some_permissions
--comment Fix some permission description
SET search_path = swmanagement;
UPDATE permissions SET description = 'Entity labels management for keyentity' WHERE code = 'keyentity:entitylabels';
UPDATE permissions SET description = 'Entity labels view for keyentity' WHERE code = 'keyentity:entitylabels:view';
UPDATE permissions SET description = 'Entity labels create for keyentity' WHERE code = 'keyentity:entitylabels:create';
UPDATE permissions SET description = 'Promo labels management for keyentity' WHERE code = 'keyentity:promolabels';
UPDATE permissions SET description = 'Promo labels view for keyentity' WHERE code = 'keyentity:promolabels:view';
UPDATE permissions SET description = 'Promo labels create for keyentity' WHERE code = 'keyentity:promolabels:create';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE permissions SET description = 'Entity labels management for keyentity' WHERE code = 'keyentity:entitylabels';
--rollback UPDATE permissions SET description = 'Entity labels view for keyentity' WHERE code = 'keyentity:entitylabels:view';
--rollback UPDATE permissions SET description = 'Entity labels create for keyentity' WHERE code = 'keyentity:entitylabels:create';
--rollback UPDATE permissions SET description = 'Promo labels management for keyentity' WHERE code = 'keyentity:promolabels';
--rollback UPDATE permissions SET description = 'Promo labels view for keyentity' WHERE code = 'keyentity:promolabels:view';
--rollback UPDATE permissions SET description = 'Promo labels create for keyentity' WHERE code = 'keyentity:promolabels:create';
--rollback RESET search_path;


--changeset sergey.malkov:2021-10-29_SWS-30903_Modify_util-trx-unloader_to_support_Manual_Payments
--comment Add table for manual finalization reports
SET search_path TO swmanagement;

CREATE TABLE IF NOT EXISTS wallet_finalization_log(
    round_id BIGINT PRIMARY KEY,
    brand_id INTEGER NOT NULL,
    game_code VARCHAR(255) NOT NULL,
    player_code VARCHAR(255) NOT NULL,
    trx_id CHAR(28) NOT NULL,
    started_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    bet NUMERIC NOT NULL,
    win numeric NOT NULL,
    currency CHAR(3) NOT NULL,
    jp_contribution NUMERIC,
    jp_win NUMERIC,
    credit NUMERIC,
    debit NUMERIC,
    is_test BOOLEAN NOT NULL DEFAULT false,
    inserted_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);
COMMENT ON TABLE wallet_finalization_log IS 'The table contains round statistics of finalized rounds using manual payments finalization type';
COMMENT ON COLUMN wallet_finalization_log.round_id IS 'Round serial identifier. Generated inside gameserver';
COMMENT ON COLUMN wallet_finalization_log.brand_id IS 'Brand identifier';
COMMENT ON COLUMN wallet_finalization_log.game_code IS 'Game code in management system';
COMMENT ON COLUMN wallet_finalization_log.player_code IS 'Player code';
COMMENT ON COLUMN wallet_finalization_log.trx_id IS 'Transaction id';
COMMENT ON COLUMN wallet_finalization_log.started_at IS 'Timestamp when round was started';
COMMENT ON COLUMN wallet_finalization_log.bet IS 'Total bet amount in round';
COMMENT ON COLUMN wallet_finalization_log.win IS 'Total win amount in round';
COMMENT ON COLUMN wallet_finalization_log.currency IS 'Currency code';
COMMENT ON COLUMN wallet_finalization_log.jp_contribution IS 'Total jackpot contribution per round in player currency';
COMMENT ON COLUMN wallet_finalization_log.jp_win IS 'Total jackpot win per round in player currency';
COMMENT ON COLUMN wallet_finalization_log.debit IS 'Debit amount (transfer-in amount, etc)';
COMMENT ON COLUMN wallet_finalization_log.credit IS 'Credit amount (redeem bns amount, transfer-out amount, etc)';
COMMENT ON COLUMN wallet_finalization_log.is_test IS 'Is it a test player';
COMMENT ON COLUMN wallet_finalization_log.inserted_at IS 'When transaction is inserted';

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP TABLE IF EXISTS wallet_finalization_log;
--rollback RESET search_path;
