--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset aleksey.ignatenko:2021-10-07_DEVOPS-16279_set_new_partitioned_tables_to_arch
--comment Added settings for moving to archive some new partitioned tables on CDs environments. 
DELETE FROM partition_cleanup_config WHERE tabrel::text IN 
    ('swmanagement.payments', 'swjackpot.remote_jp_contribution_log', 'swmanagement.audits_session', 'swgameserver.archived_game_contexts', 'swadaptergos.ext_bet_win_history');
    
INSERT INTO partition_cleanup_config (tabrel, dt_col, lifespan) VALUES
     ('swmanagement.payments'::regclass,'start_date','1 MONTH'),
     ('swjackpot.remote_jp_contribution_log'::regclass,'trx_date','1 MONTH'),
     ('swmanagement.audits_session'::regclass,'started_at','1 MONTH'),
     ('swgameserver.archived_game_contexts'::regclass,'created_at','1 MONTH'),
     ('swadaptergos.ext_bet_win_history'::regclass,'inserted_at','1 MONTH');

--rollback DELETE FROM partition_cleanup_config WHERE tabrel::text IN 
--rollback     ('swmanagement.payments', 'swjackpot.remote_jp_contribution_log', 'swmanagement.audits_session', 'swgameserver.archived_game_contexts', 'swadaptergos.ext_bet_win_history');


--changeset sergey.malkov:2021-10-25-SWS-30895-Add-new-field-to-EntityGame-hidden
--comment add hidden column to the entity_games table
SET search_path = swmanagement;
ALTER TABLE entity_games ADD COLUMN IF NOT EXISTS hidden BOOLEAN;
COMMENT ON COLUMN entity_games.hidden IS 'Marks game as hidden. Hidden games can be finished, but cannot be launched.';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entity_games DROP COLUMN IF EXISTS hidden;
--rollback RESET search_path;


--changeset sergey.malkov:2021-10-25-SWS-30895-Add-new-field-to-EntityGame-hidden-2 endDelimiter:# stripComments:false
--comment update fnc_add_entity_games value of column "hidden" should be taken from parent entity game
SET search_path TO swmanagement;

ALTER FUNCTION fnc_add_entity_games(int4, varchar[]) RENAME TO fnc_add_entity_games_before_4_70_0;

CREATE OR REPLACE FUNCTION fnc_add_entity_games(p_entity_id integer, p_game_codes character varying[], OUT po_games_added integer)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name: fnc_add_entity_games
    Purpose    : Add records in entity_games base for given entity_id and its childs. Records based on parents entity_games record.
    History    :
        1.0.0
            Date    : Dec 16, 2019
            Authors : Valdis Akmens
            Notes   : Release (DEVOPS-7322)
        1.0.2
            Date    : Jul 31, 2020
            Authors : Ales
            Notes   : Removed column "royalties" (SWS-20673)
        1.0.3
            Date    : Oct 25, 2021
            Authors : Sergey Malkov
            Notes   : Added column "hidden" (SWS-30895)
    Sample run:
    SELECT * FROM fnc_add_entity_games( p_entity_id => 256,
                                        p_game_codes => '{sw_ps, sw_mr, sw_gs}'::VARCHAR[]
                                        );
********************************************************************************************************/
DECLARE
    v_code  VARCHAR;
BEGIN

     /* Check mandatory params */
    IF p_entity_id IS NULL OR p_game_codes IS NULL OR p_game_codes ='{}' THEN
        RAISE EXCEPTION 'Parameters must be defined!';
    END IF;

    FOREACH v_code IN ARRAY p_game_codes LOOP
            IF fnc_validate_entity_game_jp_settings(p_entity_id, v_code) = FALSE THEN
                RAISE EXCEPTION 'Jackpot settings is not valid for game %', v_code;
            END IF;
    END LOOP;

    -- Get full hierarchy tree for entity UP and DOWN direction for given entity_id
    WITH RECURSIVE cte_hier_down
    AS
    (
        SELECT id, parent, name, type,  0 AS deep_level, title,  path, is_test
        FROM entities
        WHERE id = p_entity_id
        UNION ALL
        SELECT en.id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.path, en.is_test
        FROM entities AS en
        INNER JOIN cte_hier_down AS h ON en.parent = h.id
    ),
    -- Get parent of p_entity_id entity_game record which will be used for new records
    cte_entity_game AS (
        SELECT enga.*
        FROM entity_games      AS enga
        JOIN games             AS game ON enga.game_id = game.id
        JOIN game_providers    AS gapr ON game.provider_id = gapr.id
        JOIN cte_hier_down     AS cthi ON enga.entity_id = cthi.parent
        WHERE
            game.status = 'available'
        AND gapr.status = 'normal'
        AND game.code = ANY(p_game_codes)
        AND cthi.deep_level = 0
    ), cte_ins AS (
        INSERT INTO entity_games (entity_id, game_id, created_at, updated_at, parent_entity_game_id, settings, status, limit_filters, url_params, hidden)
        SELECT cthi.id AS entity_id, cega.game_id, NOW(), NOW(), cega.id AS parent_entity_game_id, NULL AS settings, cega.status, cega.limit_filters, NULL AS url_params, cega.hidden
        FROM cte_hier_down AS cthi
        CROSS JOIN cte_entity_game AS cega
        WHERE cthi.deep_level >=0
        AND NOT EXISTS(SELECT id FROM entity_games AS x WHERE cthi.id = x.entity_id AND cega.game_id = x.game_id)
        RETURNING *
    )
    SELECT COUNT(*)
    FROM cte_ins
    INTO po_games_added
    ;

    RETURN;
END;
$function$
;

RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_add_entity_games(int4, varchar[]);
--rollback ALTER FUNCTION fnc_add_entity_games_before_4_70_0 (int4, varchar[]) RENAME TO fnc_add_entity_games;
--rollback RESET search_path;
