--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset maksim.puzikov:2020-09-01-SWS-XXXX-start-release-4.44.0
--comment label for 4.44.0
select now();
--rollback select now();


--changeset aleksey.ignatenko:2020-09-03_SWS-21281_move_old_data_to_arch_no_more_views endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Returned back tables instead of views in archive_ro schemes on CD envs, filled from proper tables of archive schemes, added filling
SET search_path = swsystem;

DO $$
DECLARE   
    v_fs_name   TEXT;
    a_roles     TEXT[]      := '{usr_bi_team, usr_dev_team, usr_qa_team}';
    a_schemas   TEXT[]      := '{swmanagement_archive_ro, swjackpot_archive_ro}';
    v_sql       TEXT;
BEGIN
    SET client_min_messages TO INFO;

    -- Check for the foreign server
    BEGIN
        SELECT srvname FROM pg_foreign_server WHERE srvname ~* '^sw_hcl.+ro$' INTO STRICT v_fs_name;
    EXCEPTION
        WHEN no_data_found THEN
            RAISE INFO '[%] Foreign server not found. Will do emulation', clock_timestamp();
        WHEN too_many_rows THEN
            RAISE EXCEPTION '[%] Too many foreign servers. Can''t choose one!', clock_timestamp();
    END;
   
    IF (v_fs_name IS NULL) THEN -- Emulation (CD envs)
        -- Removing views
        SELECT string_agg(format(
                    'DROP VIEW %s.%s;', t.schemaname, t.viewname)
                , E'\n' ORDER BY t.schemaname, t.viewname)
            INTO v_sql
        FROM pg_catalog.pg_views t        
        WHERE t.schemaname = ANY(a_schemas)
        ;        
        RAISE INFO E'[%] Removing views: \n%', clock_timestamp(), v_sql;
        EXECUTE (v_sql);
        
        -- Renaming back tables
        SELECT string_agg(format(
                    'ALTER TABLE %s.%s RENAME TO %s;', t.schemaname, t.tablename, LEFT(t.tablename, -4))
                , E'\n' ORDER BY t.schemaname, t.tablename)
            INTO v_sql
        FROM pg_catalog.pg_tables t        
        WHERE t.schemaname = ANY(a_schemas) 
            AND t.tablename ILIKE '%_bak'
        ;        
        RAISE INFO E'[%] Renaming back tables: \n%', clock_timestamp(), v_sql;
        EXECUTE (v_sql);
        
        -- Permissions to tables
        SELECT string_agg(format(
                        'GRANT SELECT ON TABLE %s.%s TO %s;', v.schemaname, v.tablename, r.rolname)
                    , E'\n' ORDER BY v.schemaname, v.tablename, r.rolname)
            INTO v_sql
        FROM pg_catalog.pg_tables v
        CROSS JOIN pg_catalog.pg_roles r
        WHERE v.schemaname = ANY(a_schemas)
            AND r.rolname = ANY(a_roles)
        ;
        IF (v_sql IS NULL) THEN
            RAISE INFO E'[%] Permissions to views: skipped', clock_timestamp();
        ELSE
            RAISE INFO E'[%] Permissions to views: \n%', clock_timestamp(), v_sql;
            EXECUTE (v_sql);
        END IF;
            
        -- Copy data
        WITH cte_cols AS 
        (
            SELECT table_schema, table_name, 
                    string_agg(column_name::varchar, ', ' ORDER BY ordinal_position) AS list
            FROM information_schema.columns 
            WHERE table_schema = ANY(a_schemas)
            GROUP BY table_schema, table_name
        )
        SELECT string_agg(format(
                        'TRUNCATE TABLE %s.%s; INSERT INTO %s.%s SELECT %s FROM %s.%s;'
                        , v.schemaname, v.tablename, v.schemaname, v.tablename, c.list, t.schemaname, t.tablename)
                    , E'\n' ORDER BY v.schemaname, v.tablename)
            INTO v_sql
        FROM pg_catalog.pg_tables v
        INNER JOIN pg_catalog.pg_tables t ON t.schemaname = LEFT(v.schemaname, -3) AND t.tablename = v.tablename
        INNER JOIN cte_cols c ON c.table_schema = v.schemaname AND c.table_name = v.tablename 
        WHERE v.schemaname = ANY(a_schemas)
        ;        
        RAISE INFO E'[%] Copying data from archive to archive_ro schemes: \n%', clock_timestamp(), v_sql;
        EXECUTE (v_sql);
    
    END IF;
    
    RESET client_min_messages;
END $$;

ALTER FUNCTION fnc_move_data_to_arch RENAME TO fnc_move_data_to_arch_before_4_44_0;

--DROP FUNCTION fnc_move_data_to_arch (bool)
CREATE OR REPLACE FUNCTION swsystem.fnc_move_data_to_arch 
(
    p_lifespan      text    DEFAULT NULL, 
    p_check_only    bool    DEFAULT FALSE 
)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name:   fnc_move_data_to_arch
    Purpose    : Copy rows older than certain date (1 month ago usually, from config for each table, or from <p_lifespan> - will be used the greatest interval) 
                from a set of big tables listed and configured in the table "swsystem.cleanups"
                to proper archive tables usually placed in schemas "sw*_archive" 
                and clear the original tables from these old rows
    History    :
        1.0.0
            Date    : June 09, 2020
            Authors : Ales
            Notes   : Release (DEVOPS-9291)
        1.0.1
            Date    : Sep 03, 2020
            Authors : Ales
            Notes   : Added copying old data also to the archive_ro schema (SWS-21281)

    Sample run:
        SELECT swsystem.fnc_move_data_to_arch(null, true);
        SELECT jsonb_pretty( swsystem.fnc_move_data_to_arch(null) );
********************************************************************************************************/
DECLARE
    i_lifespan  interval    := p_lifespan::interval;
    v_res       jsonb       := '{}'::jsonb;
    v_today     timestamp   := date_trunc('DAY', now() AT TIME ZONE 'UTC');
    v_arch_suff varchar(30) := '_archive';
    v_refl_suff varchar(30) := '_ro';
    c           record;  
    v_dead_line timestamp;
    v_num       int;
    v_sql       text;
    v_count     int         := 0;
    v_total     int         := 0;
BEGIN
    
    FOR c IN (
                WITH arch_tables AS (
                    SELECT  split_part(tabrel::text, '.', 2) AS live_table,
                            split_part(tabrel::text, '.', 1) AS live_schema,
                            split_part(arch_tabrel::text, '.', 2) AS arch_table,
                            split_part(arch_tabrel::text, '.', 1) AS arch_schema,
                            tabrel, dt_col, lifespan                                                
                    FROM swsystem.partition_cleanup_config                
                    WHERE is_active 
                )              
                , cols AS (
                    SELECT table_schema, table_name, 
                            array_agg(column_name::varchar ORDER BY ordinal_position) AS arr
                    FROM information_schema.columns 
                    GROUP BY table_schema, table_name
                )
                SELECT  v.live_table AS table_name, tabrel, 
                        COALESCE(v.dt_col, 'inserted_at') AS dt_col, lifespan,
                        v.live_schema, l.table_name AS live_table, l.arr AS live_cols,
                        a.table_schema AS arch_schema, a.table_name AS arch_table, a.arr AS arch_cols, 
                        r.table_schema AS view_schema, r.table_name AS view_table, r.arr AS view_cols
                FROM arch_tables v                    
                LEFT JOIN cols l
                    ON l.table_schema = v.live_schema
                        AND l.table_name = v.live_table
                LEFT JOIN cols a 
                    ON a.table_schema = COALESCE(v.arch_schema, v.live_schema || v_arch_suff) 
                        AND a.table_name = COALESCE(v.arch_table, v.live_table)
                LEFT JOIN cols r
                    ON r.table_schema = (a.table_schema || v_refl_suff)
                        AND r.table_name = a.table_name
            ) LOOP
        
        /* init */
        v_dead_line := v_today - greatest( coalesce(i_lifespan, c.lifespan), c.lifespan );
    
        /* check */
        IF (c.arch_table IS NULL) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Not found the table %s in the arch schema %s', c.table_name, c.arch_schema) );
            CONTINUE;
        END IF;
        IF (c.live_table IS NULL) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Not found the table %s in the live schema %s', c.table_name, c.live_schema) );
            CONTINUE;
        END IF;
        IF NOT (c.arch_cols @> c.live_cols AND c.arch_cols <@ c.live_cols) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Mismatched structures of table %s in live and arch schemas (%s and %s)', c.table_name, c.live_schema, c.arch_schema) );
            CONTINUE;
        END IF;
        IF (array_position(c.arch_cols, c.dt_col) IS NULL) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Not found column %s in the table %s in the live schema %s', c.dt_col, c.table_name, c.live_schema) );
            CONTINUE;
        END IF;
        
        /* dynamic sql */
        v_sql := CASE WHEN p_check_only 
                THEN format(
                    'SELECT count(*) FROM %s.%s WHERE %s < $1'
                    , c.live_schema, c.live_table, c.dt_col
                )
                ELSE format(
                    'WITH cleared AS (
                        DELETE FROM %s.%s WHERE %s < $1 RETURNING * 
                    ) 
                    , copied AS (
                        INSERT INTO %s.%s 
                        SELECT %s FROM cleared 
                        RETURNING %s
                    )
                    , copied_copied AS (
                        INSERT INTO %s.%s 
                        SELECT %s FROM copied
                        RETURNING %s
                    )
                    SELECT count(*) FROM copied_copied'
                    , c.live_schema, c.live_table, c.dt_col
                    , c.arch_schema, c.arch_table
                    , array_to_string(c.arch_cols, ','), array_to_string(c.arch_cols, ',')
                    , c.view_schema, c.view_table
                    , array_to_string(c.view_cols, ','), c.dt_col
                ) 
            END;
        RAISE INFO '%', v_sql;
        
        /* run */ 
        EXECUTE v_sql USING v_dead_line INTO v_num;
        
        /* log */
        PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, v_num, FALSE, 
                CASE v_num WHEN 0 
                    THEN format('Nothing to move from %s.%s to archive %s.%s / %s.%s'
                                , c.live_schema,c.live_table, c.arch_schema,c.arch_table, c.view_schema,c.view_table )
                    ELSE format('Moved %s rows from %s.%s to archive %s.%s / %s.%s'
                                , v_num, c.live_schema,c.live_table, c.arch_schema,c.arch_table, c.view_schema,c.view_table ) 
                END 
            );
        
        v_count := v_count + sign(v_num);
        v_total := v_total + 1;
    
        /* add to result */        
        v_res := v_res || jsonb_build_object (c.tabrel::text, v_num);
    
    END LOOP;

    RETURN jsonb_build_object('status', 'SUCCESS') 
        || jsonb_build_object('cleaned', format('%s/%s', v_count, v_total)) 
        || jsonb_build_object('tables', v_res);

END $function$
;

RESET search_path;

--rollback SET search_path = swsystem;
--rollback 
--rollback DO $$
--rollback DECLARE   
--rollback     v_fs_name   TEXT;
--rollback     a_roles     TEXT[]      := '{usr_bi_team, usr_dev_team, usr_qa_team}';
--rollback     a_schemas   TEXT[]      := '{swmanagement_archive_ro, swjackpot_archive_ro}';
--rollback     v_sql       TEXT;
--rollback BEGIN
--rollback     SET client_min_messages TO INFO;
--rollback 
--rollback     -- Check for the foreign server
--rollback     BEGIN
--rollback         SELECT srvname FROM pg_foreign_server WHERE srvname ~* '^sw_hcl.+ro$' INTO STRICT v_fs_name;
--rollback     EXCEPTION
--rollback         WHEN no_data_found THEN
--rollback             RAISE INFO '[%] Foreign server not found. Will do emulation', clock_timestamp();
--rollback         WHEN too_many_rows THEN
--rollback             RAISE EXCEPTION '[%] Too many foreign servers. Can''t choose one!', clock_timestamp();
--rollback     END;
--rollback    
--rollback     IF (v_fs_name IS NULL) THEN -- Emulation (CD envs)
--rollback     
--rollback         -- Renaming tables
--rollback         SELECT string_agg(format(
--rollback                     'ALTER TABLE %s.%s RENAME TO %s_bak;', t.schemaname, t.tablename, t.tablename)
--rollback                     , E'\n' ORDER BY t.schemaname, t.tablename)
--rollback             INTO v_sql
--rollback         FROM pg_catalog.pg_tables t        
--rollback         WHERE t.schemaname = ANY(a_schemas)
--rollback         ;        
--rollback         RAISE INFO E'[%] Renaming tables: \n%', clock_timestamp(), v_sql;
--rollback         EXECUTE (v_sql);
--rollback         
--rollback         -- Creating views
--rollback         SELECT string_agg(format(
--rollback                         'CREATE OR REPLACE VIEW %s.%s AS SELECT * FROM %s.%s;'
--rollback                         , t.schemaname, LEFT(t.tablename, -4), LEFT(t.schemaname, -3), LEFT(t.tablename, -4))
--rollback                     , E'\n' ORDER BY t.schemaname, t.tablename)
--rollback             INTO v_sql
--rollback         FROM pg_catalog.pg_tables t        
--rollback         WHERE t.schemaname = ANY(a_schemas)
--rollback         ;        
--rollback         RAISE INFO E'[%] Creating views: \n%', clock_timestamp(), v_sql;
--rollback         EXECUTE (v_sql);    
--rollback 
--rollback         -- Permissions to views
--rollback         SELECT string_agg(format(
--rollback                         'GRANT SELECT ON TABLE %s.%s TO %s;', v.schemaname, v.viewname, r.rolname)
--rollback                     , E'\n' ORDER BY v.schemaname, v.viewname, r.rolname)
--rollback             INTO v_sql
--rollback         FROM pg_catalog.pg_views v
--rollback         CROSS JOIN pg_catalog.pg_roles r
--rollback         WHERE v.schemaname = ANY(a_schemas)
--rollback             AND r.rolname = ANY(a_roles)
--rollback         ;        
--rollback         RAISE INFO E'[%] Permissions to views: \n%', clock_timestamp(), v_sql;
--rollback         EXECUTE (v_sql);    
--rollback         
--rollback     END IF;
--rollback 
--rollback     RESET client_min_messages;
--rollback END $$;
--rollback 
--rollback DROP FUNCTION IF EXISTS fnc_move_data_to_arch;
--rollback ALTER FUNCTION fnc_move_data_to_arch_before_4_43_0 RENAME TO fnc_move_data_to_arch;
--rollback 
--rollback RESET search_path;


--changeset maksim.puzikov:2020-08-03-SWS-20808-Remove-old-audit-system endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Add table to keep audit summary info.
--comment Add audits_summary appropriate to the audit_types
--comment Rename audit_type to audits_summary_id for table audits
--comment drop table audit_types;
SET search_path = swmanagement;
CREATE TYPE enum_action_methods AS ENUM ('post', 'put', 'get', 'patch', 'delete', 'cron', 'service');
CREATE TABLE IF NOT EXISTS audits_summary(
    id SERIAL PRIMARY KEY,
    event_name VARCHAR(255),
    summary VARCHAR(255),
    path VARCHAR(255),
    method enum_action_methods,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW(),
    unique (path, method)
);
COMMENT ON TABLE audits_summary IS 'The table contains summary info for audits. Based on endpoint information';
COMMENT ON COLUMN audits_summary.event_name IS 'Name of audited entity';
COMMENT ON COLUMN audits_summary.path IS 'URL template of audited endpoint';
COMMENT ON COLUMN audits_summary.summary IS 'Description that explain what this endpoint does';
COMMENT ON COLUMN audits_summary.method IS 'Short description of method action';
ALTER TABLE audits_summary OWNER TO swmanagement;
ALTER TYPE enum_action_methods OWNER TO swmanagement;

INSERT INTO audits_summary (id, event_name, summary, path, method, created_at, updated_at)
VALUES (1, 'LOGIN', 'LOGIN', 'LOGIN', 'service', NOW(), NOW()),
       (2, 'UNUSED', 'UNUSED', 'UNUSED', 'service', NOW(), NOW()),
       (3, 'PLAYER_CHANGE', 'PLAYER_CHANGE', 'PLAYER_CHANGE', 'service', NOW(), NOW()),
       (4, 'PLAYER_CHANGE_STATUS', 'PLAYER_CHANGE_STATUS', 'PLAYER_CHANGE_STATUS', 'service', NOW(), NOW()),
       (5, 'ENTITY_SETTINGS_CHANGE', 'ENTITY_SETTINGS_CHANGE', 'ENTITY_SETTINGS_CHANGE', 'service', NOW(), NOW()),
       (6, 'ENTITY_SETTINGS_RESET', 'ENTITY_SETTINGS_RESET', 'ENTITY_SETTINGS_RESET', 'service', NOW(), NOW()),
       (7, 'ENTITY_LANGUAGE_ADD', 'ENTITY_LANGUAGE_ADD', 'ENTITY_LANGUAGE_ADD', 'service', NOW(), NOW()),
       (8, 'ENTITY_LANGUAGE_REMOVE', 'ENTITY_LANGUAGE_REMOVE', 'ENTITY_LANGUAGE_REMOVE', 'service', NOW(), NOW()),
       (9, 'ENTITY_CURRENCY_ADD', 'ENTITY_CURRENCY_ADD', 'ENTITY_CURRENCY_ADD', 'service', NOW(), NOW()),
       (10, 'ENTITY_CURRENCY_REMOVE', 'ENTITY_CURRENCY_REMOVE', 'ENTITY_CURRENCY_REMOVE', 'service', NOW(), NOW()),
       (11, 'ENTITY_COUNTRY_ADD', 'ENTITY_COUNTRY_ADD', 'ENTITY_COUNTRY_ADD', 'service', NOW(), NOW()),
       (12, 'ENTITY_COUNTRY_REMOVE', 'ENTITY_COUNTRY_REMOVE', 'ENTITY_COUNTRY_REMOVE', 'service', NOW(), NOW()),
       (13, 'ENTITY_CREDIT', 'ENTITY_CREDIT', 'ENTITY_CREDIT', 'service', NOW(), NOW()),
       (14, 'ENTITY_DEBIT', 'ENTITY_DEBIT', 'ENTITY_DEBIT', 'service', NOW(), NOW()),
       (15, 'ENTITY_CREATE', 'ENTITY_CREATE', 'ENTITY_CREATE', 'service', NOW(), NOW()),
       (16, 'ENTITY_BRAND_CREATE', 'ENTITY_BRAND_CREATE', 'ENTITY_BRAND_CREATE', 'service', NOW(), NOW()),
       (17, 'ENTITY_MERCHANT_CREATE', 'ENTITY_MERCHANT_CREATE', 'ENTITY_MERCHANT_CREATE', 'service', NOW(), NOW()),
       (18, 'MERCHANT_TEST_PLAYER_REMOVE', 'MERCHANT_TEST_PLAYER_REMOVE', 'MERCHANT_TEST_PLAYER_REMOVE', 'service', NOW(), NOW()),
       (19, 'USER_CREATE', 'USER_CREATE', 'USER_CREATE', 'service', NOW(), NOW()),
       (20, 'USER_REMOVE', 'USER_REMOVE', 'USER_REMOVE', 'service', NOW(), NOW()),
       (21, 'FORCE_FINISH_ROUND', 'FORCE_FINISH_ROUND', 'FORCE_FINISH_ROUND', 'service', NOW(), NOW()),
       (22, 'REVERT_ROUND', 'REVERT_ROUND', 'REVERT_ROUND', 'service', NOW(), NOW()),
       (23, 'SESSION_KILL', 'SESSION_KILL', 'SESSION_KILL', 'service', NOW(), NOW()),
       (24, 'ENTITY_GAME_SUSPEND', 'ENTITY_GAME_SUSPEND', 'ENTITY_GAME_SUSPEND', 'service', NOW(), NOW()),
       (25, 'ENTITY_GAME_RESTORE', 'ENTITY_GAME_RESTORE', 'ENTITY_GAME_RESTORE', 'service', NOW(), NOW()),
       (26, 'ENTITY_GAME_BULK_UPDATE_STATUS', 'ENTITY_GAME_BULK_UPDATE_STATUS', 'ENTITY_GAME_BULK_UPDATE_STATUS', 'service', NOW(), NOW()),
       (27, 'ENTITY_GAME_UPDATE', 'ENTITY_GAME_UPDATE', 'ENTITY_GAME_UPDATE', 'service', NOW(), NOW()),
       (28, 'PLAYER_INIT_DEPOSIT', 'PLAYER_INIT_DEPOSIT', 'PLAYER_INIT_DEPOSIT', 'service', NOW(), NOW()),
       (29, 'PLAYER_INIT_WITHDRAW', 'PLAYER_INIT_WITHDRAW', 'PLAYER_INIT_WITHDRAW', 'service', NOW(), NOW()),
       (30, 'ENTITY_BULK_OPERATION', 'ENTITY_BULK_OPERATION', 'ENTITY_BULK_OPERATION', 'service', NOW(), NOW()),
       (31, 'STATIC_DOMAIN_CREATE', 'STATIC_DOMAIN_CREATE', 'STATIC_DOMAIN_CREATE', 'service', NOW(), NOW()),
       (32, 'STATIC_DOMAIN_UPDATE', 'STATIC_DOMAIN_UPDATE', 'STATIC_DOMAIN_UPDATE', 'service', NOW(), NOW()),
       (33, 'STATIC_DOMAIN_REMOVE', 'STATIC_DOMAIN_REMOVE', 'STATIC_DOMAIN_REMOVE', 'service', NOW(), NOW()),
       (34, 'DYNAMIC_DOMAIN_CREATE', 'DYNAMIC_DOMAIN_CREATE', 'DYNAMIC_DOMAIN_CREATE', 'service', NOW(), NOW()),
       (35, 'DYNAMIC_DOMAIN_UPDATE', 'DYNAMIC_DOMAIN_UPDATE', 'DYNAMIC_DOMAIN_UPDATE', 'service', NOW(), NOW()),
       (36, 'DYNAMIC_DOMAIN_REMOVE', 'DYNAMIC_DOMAIN_REMOVE', 'DYNAMIC_DOMAIN_REMOVE', 'service', NOW(), NOW()),
       (37, 'STATIC_ENTITY_DOMAIN_SET', 'STATIC_ENTITY_DOMAIN_SET', 'STATIC_ENTITY_DOMAIN_SET', 'service', NOW(), NOW()),
       (38, 'STATIC_ENTITY_DOMAIN_RESET', 'STATIC_ENTITY_DOMAIN_RESET', 'STATIC_ENTITY_DOMAIN_RESET', 'service', NOW(), NOW()),
       (39, 'DYNAMIC_ENTITY_DOMAIN_SET', 'DYNAMIC_ENTITY_DOMAIN_SET', 'DYNAMIC_ENTITY_DOMAIN_SET', 'service', NOW(), NOW()),
       (40, 'DYNAMIC_ENTITY_DOMAIN_RESET', 'DYNAMIC_ENTITY_DOMAIN_RESET', 'DYNAMIC_ENTITY_DOMAIN_RESET', 'service', NOW(), NOW()),
       (41, 'SET_SECOND_STEP_AUTH_TYPE', 'SET_SECOND_STEP_AUTH_TYPE', 'SET_SECOND_STEP_AUTH_TYPE', 'service', NOW(), NOW()),
       (42, 'SECOND_STEP_LOGIN', 'SECOND_STEP_LOGIN', 'SECOND_STEP_LOGIN', 'service', NOW(), NOW()),
       (43, 'PLAYER_BULK_OPERATION', 'PLAYER_BULK_OPERATION', 'PLAYER_BULK_OPERATION', 'service', NOW(), NOW()),
       (44, 'ENTITY_GAME_BULK_UPDATE_LIMITS', 'ENTITY_GAME_BULK_UPDATE_LIMITS', 'ENTITY_GAME_BULK_UPDATE_LIMITS', 'service', NOW(), NOW()),
       (45, 'START_FINALIZE_ROUND', 'START_FINALIZE_ROUND', 'START_FINALIZE_ROUND', 'service', NOW(), NOW()),
       (46, 'FINISH_FINALIZE_ROUND', 'FINISH_FINALIZE_ROUND', 'FINISH_FINALIZE_ROUND', 'service', NOW(), NOW()),
       (47, 'ENTITY_STATIC_DOMAIN_TAGS_SET', 'ENTITY_STATIC_DOMAIN_TAGS_SET', 'ENTITY_STATIC_DOMAIN_TAGS_SET', 'service', NOW(), NOW()),
       (48, 'ENTITY_STATIC_DOMAIN_TAGS_RESET', 'ENTITY_STATIC_DOMAIN_TAGS_RESET', 'ENTITY_STATIC_DOMAIN_TAGS_RESET', 'service', NOW(), NOW()),
       (49, 'GAME_LIMITS_CONFIG_CREATE', 'GAME_LIMITS_CONFIG_CREATE', 'GAME_LIMITS_CONFIG_CREATE', 'service', NOW(), NOW()),
       (50, 'GAME_LIMITS_CONFIG_UPDATE', 'GAME_LIMITS_CONFIG_UPDATE', 'GAME_LIMITS_CONFIG_UPDATE', 'service', NOW(), NOW()),
       (51, 'GAME_LIMITS_CONFIG_REMOVE', 'GAME_LIMITS_CONFIG_REMOVE', 'GAME_LIMITS_CONFIG_REMOVE', 'service', NOW(), NOW()),
       (52, 'CURRENCY_MULTIPLIER_SET', 'CURRENCY_MULTIPLIER_SET', 'CURRENCY_MULTIPLIER_SET', 'service', NOW(), NOW());

ALTER TABLE audits
RENAME COLUMN audit_type TO audits_summary_id;
COMMENT ON COLUMN audits.audits_summary_id IS 'Id of an audits_summary';
ALTER TABLE swmanagement_archive.audits RENAME COLUMN audit_type TO audits_summary_id;
ALTER TABLE swmanagement_archive_ro.audits RENAME COLUMN audit_type TO audits_summary_id;
ALTER TABLE audit_types SET SCHEMA swbackup;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE swbackup.audit_types SET SCHEMA swmanagement;
--rollback DROP TABLE audits_summary;
--rollback DROP TYPE enum_action_methods;
--rollback ALTER TABLE audits RENAME COLUMN audits_summary_id TO audit_type;
--rollback COMMENT ON COLUMN audits.audit_type IS 'Id of an audit_types';
--rollback ALTER TABLE swmanagement_archive.audits RENAME COLUMN audits_summary_id TO audit_type;
--rollback ALTER TABLE swmanagement_archive_ro.audits RENAME COLUMN audits_summary_id TO audit_type;
--rollback RESET search_path;


--changeset maksim.puzikov:2020-09-15-SWS-21534-audit-type-error
--comment Add column audit_type to table audits
SET search_path = swmanagement;
ALTER TABLE audits ADD COLUMN audit_type smallint;
COMMENT ON COLUMN audits.audit_type IS 'Audit type id';
ALTER TABLE swmanagement_archive.audits ADD COLUMN audit_type smallint;
ALTER TABLE swmanagement_archive_ro.audits ADD COLUMN audit_type smallint;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE audits DROP COLUMN audit_type;
--rollback ALTER TABLE swmanagement_archive.audits DROP COLUMN audit_type;
--rollback ALTER TABLE swmanagement_archive_ro.audits DROP COLUMN audit_type;
--rollback RESET search_path;


--changeset maksim.puzikov:2020-09-15-SWS-21534-audit-type-error-fix
--comment Remove not null constraint for audits_summary_id
SET search_path = swmanagement;
ALTER TABLE audits ALTER COLUMN audits_summary_id DROP NOT NULL;
ALTER TABLE swmanagement_archive.audits ALTER COLUMN audits_summary_id DROP NOT NULL;
ALTER TABLE swmanagement_archive_ro.audits ALTER COLUMN audits_summary_id DROP NOT NULL;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE audits ALTER COLUMN audits_summary_id SET DEFAULT 1;
--rollback ALTER TABLE audits ALTER COLUMN audits_summary_id SET NOT NULL;
--rollback ALTER TABLE swmanagement_archive.audits ALTER COLUMN audits_summary_id SET DEFAULT 1;
--rollback ALTER TABLE swmanagement_archive.audits ALTER COLUMN audits_summary_id SET NOT NULL;
--rollback ALTER TABLE swmanagement_archive_ro.audits ALTER COLUMN audits_summary_id SET DEFAULT 1;
--rollback ALTER TABLE swmanagement_archive_ro.audits ALTER COLUMN audits_summary_id SET NOT NULL;
--rollback RESET search_path;


--changeset senko.nikita:2020-09-04-SWS-20609-del-permissions-with-bo-and-add-with-bi
--comment del permissions keyentity:bo:report:special:esl:campaign, bo:report:special:esl:campaign
--comment add permissions keyentity:bi:report:special:esl:campaign, bi:report:special:esl:campaign
SET search_path = swmanagement;
DELETE FROM permissions WHERE code='keyentity:bo:report:special:esl:campaign';
DELETE FROM permissions WHERE code='bo:report:special:esl:campaign';
UPDATE roles SET permissions = permissions - 'keyentity:bo:report:special:esl:campaign';
UPDATE roles SET permissions = permissions - 'bo:report:special:esl:campaign';
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:special:esl:campaign' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'bi:report:special:esl:campaign' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:bi:report:special:esl:campaign", "bi:report:special:esl:campaign"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('keyentity:bo:report:special:esl:campaign', 'BO report for special ESL campaign', NOW(), NOW());
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('bo:report:special:esl:campaign', 'BO report for special ESL campaign', NOW(), NOW());
--rollback UPDATE roles SET permissions = permissions || '["keyentity:bo:report:special:esl:campaign", "bo:report:special:esl:campaign"]'::jsonb WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:special:esl:campaign' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:special:esl:campaign' WHERE id = 1;
--rollback RESET search_path;


--changeset stepan.shklyanka:2020-09-04-SWS-20678-update-round-history-to-store-ext-round-id
--comment add jsonb column
SET search_path = swmanagement;
ALTER TABLE rounds_finished ADD COLUMN extra_data jsonb;
ALTER TABLE rounds_unfinished ADD COLUMN extra_data jsonb;
ALTER TABLE rounds_history ADD COLUMN extra_data jsonb;
ALTER TABLE rounds_history_duplicates ADD COLUMN extra_data jsonb;
RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE rounds_finished ADD COLUMN extra_data jsonb;
ALTER TABLE rounds_history ADD COLUMN extra_data jsonb;
RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE rounds_finished ADD COLUMN extra_data jsonb;
ALTER TABLE rounds_history ADD COLUMN extra_data jsonb;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE rounds_finished DROP extra_data;
--rollback ALTER TABLE rounds_unfinished DROP extra_data;
--rollback ALTER TABLE rounds_history DROP extra_data;
--rollback ALTER TABLE rounds_history_duplicates DROP extra_data;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive;
--rollback ALTER TABLE rounds_finished DROP extra_data;
--rollback ALTER TABLE rounds_history DROP extra_data;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive_ro;
--rollback ALTER TABLE rounds_finished DROP extra_data;
--rollback ALTER TABLE rounds_history DROP extra_data;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2020-09-07_DEVOPS-6575_change_owner_2 endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Second part. Set admin (the user swsystem) as owner for all DB objects in the pre-last schema: swjackpot
DO $$
DECLARE
    v_sysuser   TEXT        := 'swsystem';
    a_schemas   TEXT[]      := '{swjackpot}';
    v_sql       TEXT;
BEGIN
    -- Backup DB object ownership
    INSERT INTO swbackup.pg_ownership (obj_type, obj_schema, obj_name, obj_owner)
    SELECT 'D', s.nspname, s.nspname, u.usename 
    FROM pg_catalog.pg_namespace s
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = s.nspowner 
    WHERE s.nspname = ANY(a_schemas)
        AND u.usename <> v_sysuser
    UNION ALL 
    SELECT 'T', schemaname, tablename, tableowner 
    FROM pg_catalog.pg_tables t 
    WHERE schemaname = ANY(a_schemas)
        AND tableowner <> v_sysuser 
    UNION ALL
    SELECT 'V', schemaname, viewname, viewowner 
    FROM pg_catalog.pg_views v     
    WHERE schemaname = ANY(a_schemas)
        AND viewowner <> v_sysuser 
    UNION ALL    
    SELECT 'S', schemaname, sequencename, sequenceowner
    FROM pg_catalog.pg_sequences s 
    WHERE schemaname = ANY(a_schemas)
        AND sequenceowner <> v_sysuser 
    UNION ALL       
    SELECT 'P', s.nspname, p.proname, u.usename 
    FROM pg_catalog.pg_proc p 
    INNER JOIN pg_catalog.pg_namespace s ON s.oid = p.pronamespace
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = p.proowner
    WHERE s.nspname = ANY(a_schemas)
        AND u.usename <> v_sysuser
    UNION ALL
    SELECT 'L', s.nspname, t.typname, u.usename
    FROM pg_catalog.pg_type t 
    INNER JOIN pg_catalog.pg_namespace s ON s.oid = t.typnamespace
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = t.typowner
    WHERE s.nspname = ANY(a_schemas)
        AND u.usename <> v_sysuser
        AND t.typrelid = 0 AND t.typelem = 0 /* independent */
    ;    
    RAISE INFO E' [%] Stored all object ownership in the table swbackup.pg_ownership for backup', clock_timestamp();

    -- Change owner of schema and set default privileges in it
    SELECT string_agg(
                format('REASSIGN OWNED BY "%s" TO "%s";', s.nspname, v_sysuser) || E'\n' ||
                format('GRANT USAGE ON SCHEMA "%s" TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT ALL ON TABLES TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT ALL ON SEQUENCES TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT EXECUTE ON FUNCTIONS TO %s;', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT USAGE ON TYPES TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('GRANT ALL ON ALL TABLES IN SCHEMA "%s" TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('GRANT ALL ON ALL SEQUENCES IN SCHEMA "%s" TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA "%s" TO "%s";', s.nspname, s.nspname)
                , E'\n' ORDER BY s.nspname)
        INTO v_sql
    FROM pg_catalog.pg_namespace s        
    WHERE s.nspname = ANY(a_schemas)
    ;
    IF (v_sql IS NULL) THEN
        RAISE INFO E' [%] Changing owner of schema and granting right: skipped', clock_timestamp();
    ELSE
        RAISE INFO E' [%] Changing owner of schema and granting rights: \n%', clock_timestamp(), v_sql;
        EXECUTE (v_sql);
    END IF;
    
    -- Change owner of data types 
    SELECT string_agg(CASE WHEN lower(t.typname) = t.typname 
                THEN format('GRANT USAGE ON TYPE %s.%s TO %s;', s.nspname, t.typname, s.nspname)
                ELSE format('GRANT USAGE ON TYPE %s."%s" TO %s;', s.nspname, t.typname, s.nspname)
            END, E'\n' ORDER BY s.nspname, t.typname)
        INTO v_sql
    FROM pg_catalog.pg_type t 
    INNER JOIN pg_catalog.pg_namespace s ON s.oid = t.typnamespace
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = t.typowner
    WHERE s.nspname = ANY(a_schemas)
        AND t.typrelid = 0 AND t.typelem = 0 /* independent */
    ;
    IF (v_sql IS NULL) THEN
        RAISE INFO E' [%] Changing owner of data types: skipped', clock_timestamp();
    ELSE
        RAISE INFO E' [%] Changing owner of data types: \n%', clock_timestamp(), v_sql;
        EXECUTE (v_sql);
    END IF;
    
END $$;

--rollback DO $$
--rollback DECLARE
--rollback     v_sysuser   TEXT        := 'swsystem';
--rollback     a_schemas   TEXT[]      := '{swjackpot}';
--rollback     v_sql       TEXT;
--rollback BEGIN
--rollback     
--rollback     -- Change owner of schema and set default privileges in it
--rollback     SELECT string_agg(
--rollback                 format('ALTER SCHEMA %s OWNER TO %s;', s.nspname, o.obj_owner)
--rollback                 , E'\n' ORDER BY s.nspname)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_namespace s 
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.nspname AND o.obj_name = s.nspname       
--rollback     WHERE s.nspname = ANY(a_schemas)
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of schema and granting default rights: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback 
--rollback     -- Change owner of tables and views
--rollback     WITH cte_tables AS 
--rollback     (
--rollback         SELECT schemaname, tablename, tableowner FROM pg_catalog.pg_tables t
--rollback         UNION ALL
--rollback         SELECT schemaname, viewname, viewowner FROM pg_catalog.pg_views v 
--rollback     )
--rollback     SELECT string_agg(CASE WHEN lower(t.tablename) = t.tablename 
--rollback                THEN format('ALTER TABLE %s.%s OWNER TO %s;', t.schemaname, t.tablename, o.obj_owner)
--rollback                ELSE format('ALTER TABLE %s."%s" OWNER TO %s;', t.schemaname, t.tablename, o.obj_owner)
--rollback            END, E'\n' ORDER BY t.schemaname, t.tablename)
--rollback         INTO v_sql
--rollback     FROM cte_tables t
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = t.schemaname AND o.obj_name = t.tablename        
--rollback     WHERE t.schemaname = ANY(a_schemas)
--rollback         AND t.tableowner = v_sysuser
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of tables/views: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback 
--rollback     -- Change owner of sequences 
--rollback     SELECT string_agg(CASE WHEN lower(s.sequencename) = s.sequencename 
--rollback                THEN format('ALTER SEQUENCE %s.%s OWNER TO %s;', s.schemaname, s.sequencename, o.obj_owner)
--rollback                ELSE format('ALTER SEQUENCE %s."%s" OWNER TO %s;', s.schemaname, s.sequencename, o.obj_owner)
--rollback            END, E'\n' ORDER BY s.schemaname, s.sequencename)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_sequences s
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.schemaname AND o.obj_name = s.sequencename
--rollback     WHERE s.schemaname = ANY(a_schemas)
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of sequences: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback     
--rollback     -- Change owner of functions 
--rollback     SELECT string_agg(CASE WHEN lower(p.proname) = p.proname 
--rollback                THEN format('ALTER FUNCTION %s.%s OWNER TO %s;', s.nspname, p.proname, o.obj_owner)
--rollback                ELSE format('ALTER FUNCTION %s."%s" OWNER TO %s;', s.nspname, p.proname, o.obj_owner)
--rollback            END, E'\n' ORDER BY s.nspname, p.proname)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_proc p 
--rollback     INNER JOIN pg_catalog.pg_namespace s ON s.oid = p.pronamespace
--rollback     INNER JOIN pg_catalog.pg_user u ON u.usesysid = p.proowner
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.nspname AND o.obj_name = p.proname
--rollback     WHERE s.nspname = ANY(a_schemas)
--rollback         AND u.usename = v_sysuser
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of functions: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback 
--rollback     -- Change owner of data types 
--rollback     SELECT string_agg(CASE WHEN lower(t.typname) = t.typname 
--rollback                THEN format('ALTER TYPE %s.%s OWNER TO %s;', s.nspname, t.typname, o.obj_owner)
--rollback                ELSE format('ALTER TYPE %s."%s" OWNER TO %s;', s.nspname, t.typname, o.obj_owner)
--rollback            END, E'\n' ORDER BY s.nspname, t.typname)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_type t 
--rollback     INNER JOIN pg_catalog.pg_namespace s ON s.oid = t.typnamespace
--rollback     INNER JOIN pg_catalog.pg_user u ON u.usesysid = t.typowner
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.nspname AND o.obj_name = t.typname
--rollback     WHERE s.nspname = ANY(a_schemas)
--rollback         AND u.usename = v_sysuser
--rollback         AND t.typrelid = 0 AND t.typelem = 0 
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of data types: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback END $$;


--changeset aleksey.ignatenko:2020-09-07_DEVOPS-6575_change_owner_3 endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Third part. Set admin (the user swsystem) as owner for all DB objects in the last schema: swmanagement
DO $$
DECLARE
    v_sysuser   TEXT        := 'swsystem';
    a_schemas   TEXT[]      := '{swmanagement}';
    v_sql       TEXT;
BEGIN
    -- Backup DB object ownership
    INSERT INTO swbackup.pg_ownership (obj_type, obj_schema, obj_name, obj_owner)
    SELECT 'D', s.nspname, s.nspname, u.usename 
    FROM pg_catalog.pg_namespace s
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = s.nspowner 
    WHERE s.nspname = ANY(a_schemas)
        AND u.usename <> v_sysuser
    UNION ALL 
    SELECT 'T', schemaname, tablename, tableowner 
    FROM pg_catalog.pg_tables t 
    WHERE schemaname = ANY(a_schemas)
        AND tableowner <> v_sysuser 
    UNION ALL
    SELECT 'V', schemaname, viewname, viewowner 
    FROM pg_catalog.pg_views v     
    WHERE schemaname = ANY(a_schemas)
        AND viewowner <> v_sysuser 
    UNION ALL    
    SELECT 'S', schemaname, sequencename, sequenceowner
    FROM pg_catalog.pg_sequences s 
    WHERE schemaname = ANY(a_schemas)
        AND sequenceowner <> v_sysuser 
    UNION ALL       
    SELECT 'P', s.nspname, p.proname, u.usename 
    FROM pg_catalog.pg_proc p 
    INNER JOIN pg_catalog.pg_namespace s ON s.oid = p.pronamespace
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = p.proowner
    WHERE s.nspname = ANY(a_schemas)
        AND u.usename <> v_sysuser
    UNION ALL
    SELECT 'L', s.nspname, t.typname, u.usename
    FROM pg_catalog.pg_type t 
    INNER JOIN pg_catalog.pg_namespace s ON s.oid = t.typnamespace
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = t.typowner
    WHERE s.nspname = ANY(a_schemas)
        AND u.usename <> v_sysuser
        AND t.typrelid = 0 AND t.typelem = 0 /* independent */
    ON CONFLICT DO NOTHING
    ;    
    RAISE INFO E' [%] Stored all object ownership in the table swbackup.pg_ownership for backup', clock_timestamp();

    -- Change owner of schema and set default privileges in it
    SELECT string_agg(
                format('REASSIGN OWNED BY "%s" TO "%s";', s.nspname, v_sysuser) || E'\n' ||
                format('GRANT USAGE ON SCHEMA "%s" TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT ALL ON TABLES TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT ALL ON SEQUENCES TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT EXECUTE ON FUNCTIONS TO %s;', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT USAGE ON TYPES TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('GRANT ALL ON ALL TABLES IN SCHEMA "%s" TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('GRANT ALL ON ALL SEQUENCES IN SCHEMA "%s" TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA "%s" TO "%s";', s.nspname, s.nspname)
                , E'\n' ORDER BY s.nspname)
        INTO v_sql
    FROM pg_catalog.pg_namespace s        
    WHERE s.nspname = ANY(a_schemas)
    ;
    IF (v_sql IS NULL) THEN
        RAISE INFO E' [%] Changing owner of schema and granting right: skipped', clock_timestamp();
    ELSE
        RAISE INFO E' [%] Changing owner of schema and granting rights: \n%', clock_timestamp(), v_sql;
        EXECUTE (v_sql);
    END IF;
    
    -- Change owner of data types 
    SELECT string_agg(CASE WHEN lower(t.typname) = t.typname 
                THEN format('GRANT USAGE ON TYPE %s.%s TO %s;', s.nspname, t.typname, s.nspname)
                ELSE format('GRANT USAGE ON TYPE %s."%s" TO %s;', s.nspname, t.typname, s.nspname)
            END, E'\n' ORDER BY s.nspname, t.typname)
        INTO v_sql
    FROM pg_catalog.pg_type t 
    INNER JOIN pg_catalog.pg_namespace s ON s.oid = t.typnamespace
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = t.typowner
    WHERE s.nspname = ANY(a_schemas)
        AND t.typrelid = 0 AND t.typelem = 0 /* independent */
    ;
    IF (v_sql IS NULL) THEN
        RAISE INFO E' [%] Changing owner of data types: skipped', clock_timestamp();
    ELSE
        RAISE INFO E' [%] Changing owner of data types: \n%', clock_timestamp(), v_sql;
        EXECUTE (v_sql);
    END IF;
    
END $$;

--rollback DO $$
--rollback DECLARE
--rollback     v_sysuser   TEXT        := 'swsystem';
--rollback     a_schemas   TEXT[]      := '{swmanagement}';
--rollback     v_sql       TEXT;
--rollback BEGIN
--rollback     
--rollback     -- Change owner of schema and set default privileges in it
--rollback     SELECT string_agg(
--rollback                 format('ALTER SCHEMA %s OWNER TO %s;', s.nspname, o.obj_owner)
--rollback                 , E'\n' ORDER BY s.nspname)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_namespace s 
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.nspname AND o.obj_name = s.nspname       
--rollback     WHERE s.nspname = ANY(a_schemas)
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of schema and granting default rights: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback 
--rollback     -- Change owner of tables and views
--rollback     WITH cte_tables AS 
--rollback     (
--rollback         SELECT schemaname, tablename, tableowner FROM pg_catalog.pg_tables t
--rollback         UNION ALL
--rollback         SELECT schemaname, viewname, viewowner FROM pg_catalog.pg_views v 
--rollback     )
--rollback     SELECT string_agg(CASE WHEN lower(t.tablename) = t.tablename 
--rollback                THEN format('ALTER TABLE %s.%s OWNER TO %s;', t.schemaname, t.tablename, o.obj_owner)
--rollback                ELSE format('ALTER TABLE %s."%s" OWNER TO %s;', t.schemaname, t.tablename, o.obj_owner)
--rollback            END, E'\n' ORDER BY t.schemaname, t.tablename)
--rollback         INTO v_sql
--rollback     FROM cte_tables t
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = t.schemaname AND o.obj_name = t.tablename        
--rollback     WHERE t.schemaname = ANY(a_schemas)
--rollback         AND t.tableowner = v_sysuser
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of tables/views: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback 
--rollback     -- Change owner of sequences 
--rollback     SELECT string_agg(CASE WHEN lower(s.sequencename) = s.sequencename 
--rollback                THEN format('ALTER SEQUENCE %s.%s OWNER TO %s;', s.schemaname, s.sequencename, o.obj_owner)
--rollback                ELSE format('ALTER SEQUENCE %s."%s" OWNER TO %s;', s.schemaname, s.sequencename, o.obj_owner)
--rollback            END, E'\n' ORDER BY s.schemaname, s.sequencename)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_sequences s
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.schemaname AND o.obj_name = s.sequencename
--rollback     WHERE s.schemaname = ANY(a_schemas)
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of sequences: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback     
--rollback     -- Change owner of functions 
--rollback     SELECT string_agg(CASE WHEN lower(p.proname) = p.proname 
--rollback                THEN format('ALTER FUNCTION %s.%s OWNER TO %s;', s.nspname, p.proname, o.obj_owner)
--rollback                ELSE format('ALTER FUNCTION %s."%s" OWNER TO %s;', s.nspname, p.proname, o.obj_owner)
--rollback            END, E'\n' ORDER BY s.nspname, p.proname)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_proc p 
--rollback     INNER JOIN pg_catalog.pg_namespace s ON s.oid = p.pronamespace
--rollback     INNER JOIN pg_catalog.pg_user u ON u.usesysid = p.proowner
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.nspname AND o.obj_name = p.proname
--rollback     WHERE s.nspname = ANY(a_schemas)
--rollback         AND u.usename = v_sysuser
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of functions: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback 
--rollback     -- Change owner of data types 
--rollback     SELECT string_agg(CASE WHEN lower(t.typname) = t.typname 
--rollback                THEN format('ALTER TYPE %s.%s OWNER TO %s;', s.nspname, t.typname, o.obj_owner)
--rollback                ELSE format('ALTER TYPE %s."%s" OWNER TO %s;', s.nspname, t.typname, o.obj_owner)
--rollback            END, E'\n' ORDER BY s.nspname, t.typname)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_type t 
--rollback     INNER JOIN pg_catalog.pg_namespace s ON s.oid = t.typnamespace
--rollback     INNER JOIN pg_catalog.pg_user u ON u.usesysid = t.typowner
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.nspname AND o.obj_name = t.typname
--rollback     WHERE s.nspname = ANY(a_schemas)
--rollback         AND u.usename = v_sysuser
--rollback         AND t.typrelid = 0 AND t.typelem = 0 
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of data types: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback END $$;


--changeset maksim.puzikov:2020-09-10-SWS-21431-Entries-in-the-audits-summary-table-only-started-appearing-after-the-52nd-id-in-the-table
--comment set autoincrement id starting at 100 from the last order
SET search_path = swmanagement;
SELECT setval(pg_get_serial_sequence('audits_summary', 'id'), (select max(id) + 100 from audits_summary));
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback SELECT Now();
--rollback RESET search_path;

--changeset stepanov.aleksey:SWS-21318-games-are-added-to-the-recent-item-when-user-clicks-on-the-favorites-star-icon
--comment Add new column 'is_recently' in 'favorite_games_by_player'
SET search_path = swmanagement;
ALTER TABLE favorite_games_by_player ADD COLUMN is_recently boolean DEFAULT false;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE favorite_games_by_player DROP COLUMN is_recently;
--rollback RESET search_path;
