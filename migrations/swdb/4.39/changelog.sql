--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset pavel.shamshurov:2020-03-30-SWS-XXXX-start-release-4.39.0
--comment label for 4.39.0
select now();
--rollback select now();


--changeset pavel.shamshurov:2020-05-22-SWS-18688-add-extra-permissions-to-role
--comment Add extra permissions to superadmin role
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'bi:report:player:leaving' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:player:leaving' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["bi:report:player:leaving", "keyentity:bi:report:player:leaving"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:player:leaving' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:player:leaving' WHERE id = 1;
--rollback RESET search_path;

--changeset stepanov.alekey:2020-05-29-SWS-15489
--comment Add api to store Favorite games per Player
SET search_path = swmanagement;
ALTER TABLE favorite_games_by_player DROP COLUMN count;
ALTER TABLE favorite_games_by_player ADD COLUMN is_favorite boolean DEFAULT false;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE favorite_games_by_player DROP COLUMN is_favorite;
--rollback ALTER TABLE favorite_games_by_player ADD COLUMN count INTEGER NOT NULL DEFAULT 1;
--rollback RESET search_path;

--changeset stepanov.alekey:2020-06-17-SWS-15489
--comment Add api to store Favorite games per Player - rollback column
SET search_path = swmanagement;
ALTER TABLE favorite_games_by_player ADD COLUMN count INTEGER NOT NULL DEFAULT 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE favorite_games_by_player DROP COLUMN count;
--rollback RESET search_path;
