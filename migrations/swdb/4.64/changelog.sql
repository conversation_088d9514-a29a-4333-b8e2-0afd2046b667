--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset emanuel-alin.railean:2021-07-21-SWS-26113-operator-fetch-phantom
--comment Add new permissions for jackpots
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:jackpot' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:jackpot"]'::jsonb WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:jackpot:instance' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:jackpot:instance"]'::jsonb WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:jackpot:instance:view' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:jackpot:instance:view"]'::jsonb WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:jackpot:instance:create' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:jackpot:instance:create"]'::jsonb WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:jackpot:instance:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:jackpot:instance:edit"]'::jsonb WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:jackpot:instance:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:jackpot:instance:delete"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:jackpot' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:jackpot:instance' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:jackpot:instance:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:jackpot:instance:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:jackpot:instance:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:jackpot:instance:delete' WHERE id = 1;
--rollback RESET search_path;
