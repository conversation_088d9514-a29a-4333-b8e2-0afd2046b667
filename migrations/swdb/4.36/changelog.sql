--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vera.kruhl<PERSON><PERSON>:2020-04-10-SWS-XXXX-start-release-4.36.0
--comment label for 4.36.0
select now();
--rollback select now();


--changeset vera.kruhliakova:2020-03-18-SWS-16535-jp-base-type
--comment Add base jackpot type
SET search_path TO swjackpot;

ALTER TABLE jp_type ADD COLUMN base_type CHARACTER VARYING(32);
UPDATE jp_type SET base_type = name;
ALTER TABLE jp_type ALTER COLUMN base_type SET NOT NULL;
COMMENT ON COLUMN jp_type.base_type IS 'Jackpot base type representing common type name excluding regulations specific';

RESET search_path;
--rollback SET search_path = swjackpot;
--rollback ALTER TABLE jp_type DROP COLUMN base_type;
--rollback RESET search_path;


--changeset vera.kruhliakova:2020-04-10-SWS-17448-live-permission
--comment Add permission to access Live Streaming API
SET search_path = swmanagement;

INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('live:streaming', 'Permission to access Live Streaming API', now(), now());
UPDATE roles SET permissions = permissions - 'live:streaming' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["live:streaming"]'::JSONB WHERE id = 1;

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'live:streaming' WHERE id = 1;
--rollback DELETE FROM permissions WHERE code='live:streaming';
--rollback RESET search_path;

--changeset ivan.kashkov:2020-04-13-SWS-16417
--comment Deferred start of reward for BNS
SET search_path = swmanagement;

ALTER TABLE promotions ADD COLUMN start_reward_on_game_open boolean DEFAULT false NOT NULL;
COMMENT ON COLUMN promotions.start_reward_on_game_open IS 'if to start reward during game open, like deferred start reward';

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE promotions DROP COLUMN start_reward_on_game_open;
--rollback RESET search_path;


--changeset anastasia.kostyukova:2020-04-13-SWS-16126-rename-sets
--comment Rename 'sets' in schema for live games to 'levels'
SET search_path = swmanagement;
UPDATE schema_definitions SET SCHEMA=REPLACE(SCHEMA::text, 'sets', 'levels')::jsonb;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE schema_definitions SET schema=replace(schema::text, 'levels', 'sets')::jsonb;
--rollback RESET search_path;


--changeset anastasia.kostyukova:2020-04-13-SWS-17699-optional-fields-in-schema
--comment Add to schema required fields: all not calculaed fields
SET search_path = swmanagement;
UPDATE schema_definitions s
SET SCHEMA = SCHEMA || info.properties
FROM (SELECT s.id, ('{"required":' || array_to_json(array_agg(p.*)) || '}')::jsonb properties
      FROM schema_definitions s, jsonb_object_keys(SCHEMA -> 'properties') p
      WHERE (SCHEMA->'properties'->p->>'limitConfigurationType')::text != 'calculated'
      GROUP BY id) info
WHERE s.id = info.id;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE schema_definitions s SET schema = schema - 'required';
--rollback RESET search_path;

--changeset valdis.akmens:2020-04-14-SWDB-130-add-missing-column
--comment Add round_ended column to all environment in swmanagement_archive.wallet_win_bet table

SET search_path TO swsystem;
ALTER TABLE swmanagement_archive.wallet_win_bet ADD COLUMN IF NOT EXISTS round_ended BOOLEAN;
ALTER TABLE swmanagement_archive.wallet_win_bet ALTER COLUMN round_ended SET DEFAULT FALSE;
COMMENT ON COLUMN swmanagement_archive.wallet_win_bet.round_ended IS 'Flag that is true if round was ended on this bet/win operation';
RESET search_path;

--rollback SET search_path = swsystem;
--rollback ALTER TABLE swmanagement_archive.wallet_win_bet DROP COLUMN IF EXISTS round_ended;
--rollback RESET search_path;

--changeset valdis.akmens:2020-04-14-SWDB-132-fnc_bo_aggr_refresh_jobs-refactoring endDelimiter:# stripComments:false
--comment Replace CTE(DELETE+INSERT) to temporary tables
SET search_path = swmanagement;
ALTER FUNCTION fnc_bo_aggr_refresh_jobs(TIMESTAMP, VARCHAR) RENAME TO fnc_bo_aggr_refresh_jobs_before_4_36_0;

CREATE OR REPLACE FUNCTION fnc_bo_aggr_refresh_jobs(p_force_end_hour timestamp without time zone, p_work_mem character varying DEFAULT NULL::character varying)
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
*******************************************************************************

   Object Name:   fnc_bo_aggr_refresh_jobs
   Purpose    :   To perform B/O aggregation jobs
   History    :
      1.0.0
         Date    : Feb 03, 2017
         Authors : Timur Luchkin
         Notes   : Release (BYDEVO-260)

      1.0.1
         Date    : Mar 07, 2017
         Authors : Timur Luchkin
         Notes   : Add more details required for watchdog to monitor jobs
                   (BYSWBO-73)

      1.0.2
         Date    : Jun 09, 2017
         Authors : Timur Luchkin
         Notes   : Tables renamed to follow snake style
                   (BYDEVO-578)
                   Add logs history logging

      1.0.3
         Date    : Jul 17, 2017
         Authors : Andrey Shmigiro
         Notes   : Added table "bo_aggr_win_bets" for aggregation data from "wallet_win_bet"
                   (BYDEVO-513)

      1.0.4
         Date    : Jul 24, 2017
         Authors : Andrey Shmigiro
         Notes   : Added table "bo_aggr_player_rounds" for aggregation data about played
                     rounds from "bo_aggr_rounds";
                   Added column "exchange_rate" to table "bo_aggr_win_bets";
                   Changed calculation method for column "played_games_qty" in table "bo_aggr_win_bets";
                   (SWS-1561)

      1.0.5
         Date    : Aug 15, 2017
         Authors : Andrey Shmigiro
         Notes   : Added column "start_balance", "end_balance", "device_code" to table
                   "bo_aggr_player_rounds";
                   (SWS-1651 / SWS-1720)

      1.1.0
         Date    : Sep 28, 2017
         Authors : Timur Luchkin
         Notes   : Fix issues with missed spins (SWS-1873)
                   More logging details

      1.1.1
         Date    : Oct 19, 2017
         Authors : Andrey Shmigiro
         Notes   : Exclude test wallet operations from bo_aggr_win_bets (SWS-1789)
                   Exclude jackpot wins from bo_aggr_win_bets (SWS-1863)
                   Exclude free bets from bo_aggr_win_bets (SWS-1943)

      1.1.2
         Date    : Oct 19, 2017
         Authors : Andrey Shmigiro
         Notes   : Added columns for jackpot's and free_bet's wins to bo_agr_win_bets's tbl (SWS-2805)
                   Exclude NULLs values of start&end balances for bo_aggr_rounds

      1.1.3
            Date    : Jan 12, 2018
            Authors : Valdis Akmens
            Notes   : Change aggregation of wallet_win_bet from payment_date to inserted_at (BYDEVO-1280)

      1.1.4
            Date    : Mar 08, 2018
            Authors : Valdis Akmens
            Notes   : fnc_bo_aggr_refresh_jobs takes too much time after the partitioning has been installed (SWDB-24)
                Added new parameter p_work_mem to set larger work_mem for function to get rid off "Sort Method: external merge  Disk"
                Changed bo_aggr_win_bets aggregation to replace "LEFT JOIN" with sub-queries (because of partitioning, wrong estimations lead to non-optimal JOIN strategies)
                p_work_mem: NULL, 64MB, 128MB, 256MB ..

      1.1.5
            Date    : Jun 11, 2018
            Authors : Valdis Akmens
            Notes   : Change aggregation source for tables "bo_aggr_brand_currency" and "bo_aggr_player_rounds"
                         from "bo_aggr_rounds" to "rounds_history"(SWDB-49)

      1.1.6
            Date    : Sep 17, 2018
            Authors : Valdis Akmens
            Notes   : Remove "bo_aggr_rounds" from aggregation completely (SWDB-69)

      1.1.7
          Date    : Nov 06, 2018
          Authors : Timur Luchkin
          Notes   : Change aggregation logic to allow "bo_aggr_player_rounds" and "bo_aggr_win_bets" tables partitioning (SWDB-44)

      1.1.8
          Date    : Mar 22, 2019
          Authors : Timur Luchkin
          Notes   : Change aggregation source table from rounds_history to rounds_finished

      1.1.9
         Date    : Apr 30, 2019
         Authors : Andrey Shmigiro
         Notes   : Added column "debit", "credit" to table "bo_aggr_win_bets" (DEVOPS-5075);

      1.2.0
         Date    : Jul 30, 2019
         Authors : Valdis Akmens
         Notes   : Change aggregation of wins for table "bo_aggr_win_bets" and "bo_aggr_win_bets_by_brand" because of new wallet_transactions_types (SWS-11908);

      1.2.1
         Date    : Aug 22, 2019
         Authors : Valdis Akmens
         Notes   : Subtract "bet" for table "bo_aggr_win_bets" and "bo_aggr_win_bets_by_brand" aggregation when bet_rollback = TRUE  (SWS-12584);

      1.2.2
         Date    : Apr 15, 2020
         Authors : Valdis Akmens
         Notes   : Replace CTE(DELETE+INSERT) to temporary tables (SWDB-132);

  Sample run:
      SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL);
      SELECT * FROM fnc_bo_aggr_refresh_jobs ('2016-11-28 23:00:00');

	    SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL, NULL);
      SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL, '64MB');
	    SELECT * FROM fnc_bo_aggr_refresh_jobs ('2017-09-25 06:00:00', '64MB');

*******************************************************************************
*/
DECLARE
   v_last_inserted_at      TIMESTAMP;
   v_new_inserted_at       TIMESTAMP;
   v_time_back_msec        INTEGER;
   v_counter               BIGINT;
   v_job_start_time        TIMESTAMP;
   v_force_end_time        TIMESTAMP;
   v_huge_interval_win_bet INTERVAL := '1 day'::INTERVAL;
   v_huge_interval_rounds  INTERVAL := '12 hours'::INTERVAL;
BEGIN

     log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job started'; RETURN NEXT;

     /* Check if MDB */
     IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
     END IF;

     /* Change work_mem parameter */
     IF p_work_mem IS NOT NULL THEN
        EXECUTE 'SET work_mem TO '''||p_work_mem||'''' ;
     END IF;

     /* To prevent misses of the lazy offloaded data */
     SELECT split_part(sett,'=',2)::INTEGER
     INTO   v_time_back_msec
     FROM   (SELECT unnest(useconfig) AS sett
             FROM   pg_user
            WHERE  usename = 'redis_game_offloader'
            ) t1
     WHERE sett LIKE 'statement_timeout=%';

     IF NOT FOUND THEN
        v_time_back_msec := 60 * 1000;
     END IF;

     /* manyachello */
     v_time_back_msec := v_time_back_msec + 5000;

    /* ~~~ bo_aggr_brand_currency ~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   v_job_start_time := clock_timestamp();
   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_brand_currency" started'; RETURN NEXT;

   SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_brand_currency' AND conf_key = 'lock_record' FOR UPDATE;

   IF NOT FOUND THEN
      RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_brand_currency/lock_record" pair';
   END IF;

   v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                             ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                             );

    /* Automatically prevent too huge intervals */
     IF (v_force_end_time - v_last_inserted_at) > v_huge_interval_rounds THEN
        v_force_end_time := v_last_inserted_at + v_huge_interval_rounds;
     END IF;

    WITH fresh_data AS
    (
        SELECT
        Date_Trunc('HOUR', h.finished_at)       AS date_hour
        ,h.brand_id                             AS brand_id
        ,h.currency                             AS currency_code
        ,SUM(h.total_bet)                       AS bet
        ,SUM(h.total_win)                       AS win
        ,SUM(h.total_bet)  - SUM(h.total_win)   AS revenue
        ,COUNT(h.id)::BIGINT                    AS finished_rounds
        ,MAX(COALESCE(inserted_at, started_at)) AS max_inserted_at
    FROM  rounds_finished AS h
    WHERE NOT h.test
      AND COALESCE(inserted_at, started_at) >= v_last_inserted_at
      AND COALESCE(inserted_at, started_at) <  v_force_end_time
    GROUP BY Date_Trunc('HOUR', h.finished_at)
    ,h.brand_id
    ,h.currency
    ),
    cte_upsert AS
    (
        INSERT INTO bo_aggr_brand_currency (date_hour, date_day, brand_id, currency_code, bet, win, revenue, finished_rounds)
        SELECT
                date_hour
                ,Date_Trunc('DAY', date_hour)::DATE     AS date_day
                ,brand_id
                ,currency_code
                ,bet
                ,win
                ,revenue
                ,finished_rounds
        FROM fresh_data
        ON CONFLICT (date_hour, brand_id, currency_code) DO
        UPDATE SET
                bet             = bo_aggr_brand_currency.bet            + EXCLUDED.bet,
                win             = bo_aggr_brand_currency.win            + EXCLUDED.win,
                revenue         = bo_aggr_brand_currency.revenue        + EXCLUDED.revenue,
                finished_rounds = bo_aggr_brand_currency.finished_rounds + EXCLUDED.finished_rounds
    )
   SELECT  MAX(max_inserted_at), COUNT(*)
   FROM   fresh_data
   INTO    v_new_inserted_at, v_counter;

   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_brand_currency" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

   UPDATE bo_aggr_config SET
           conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
   WHERE  aggr_job_name = 'bo_aggr_brand_currency'
   AND  conf_key = 'lock_record';

   -- Log
   INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
      VALUES ('bo_aggr_brand_currency', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time), v_force_end_time);

   /* ~~~ bo_aggr_player_rounds ~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   v_job_start_time := clock_timestamp();
   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_player_rounds" started'; RETURN NEXT;

   SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_player_rounds' AND conf_key = 'lock_record' FOR UPDATE;

   IF NOT FOUND THEN
      RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_player_rounds/lock_record" pair';
   END IF;

   v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                             ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                             );

    /* Automatically prevent too huge intervals */
    IF (v_force_end_time - v_last_inserted_at) > v_huge_interval_rounds THEN
    v_force_end_time := v_last_inserted_at + v_huge_interval_rounds;
    END IF;

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_rounds_finished_fresh AS 
        SELECT Date_Trunc('HOUR', h.finished_at)          AS date_hour
            ,Date_Trunc('HOUR', h.finished_at)::date    AS date_day
            ,h.brand_id                                 AS brand_id
            ,h.player_code
            ,h.game_code
            ,h.currency                                 AS currency_code
            ,MAX(h.device_id)                           AS device_code    /* This is wrong! We should add this field to the primary key!!! */
            ,COUNT(h.id)::BIGINT                        AS rounds_qty
            ,SUM(h.total_events)::INTEGER               AS events_qty
            ,SUM(h.total_bet)                           AS total_bet
            ,SUM(h.total_win)                           AS total_win
            ,SUM(h.total_bet)  - SUM(h.total_win)       AS total_revenue
            ,null::numeric                              AS start_balance
            ,null::numeric                              AS end_balance
            ,min(h.started_at)                          AS first_activity
            ,MAX(h.finished_at)                         AS last_activity
            ,MAX(COALESCE(inserted_at, started_at))     AS max_inserted_at
        FROM rounds_finished AS h
        WHERE
            NOT  h.test
            AND  COALESCE(inserted_at, started_at) >= v_last_inserted_at
            AND  COALESCE(inserted_at, started_at) <  v_force_end_time
        GROUP BY h.player_code, h.brand_id, h.currency, h.game_code, date_trunc('HOUR', h.finished_at);

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_rounds_finished_deleted AS SELECT * FROM bo_aggr_player_rounds LIMIT 0;
    WITH
    cte_delete_existing_data AS (
        DELETE FROM bo_aggr_player_rounds d
        USING tmp_bo_aggr_refresh_jobs_rounds_finished_fresh f
        WHERE  d.date_hour      = f.date_hour
            AND  d.brand_id       = f.brand_id
            AND  d.game_code      = f.game_code
            AND  d.currency_code  = f.currency_code
            AND  d.player_code    = f.player_code
        RETURNING d.*
    )
    INSERT INTO tmp_bo_aggr_refresh_jobs_rounds_finished_deleted 
    SELECT * FROM cte_delete_existing_data;

    INSERT INTO bo_aggr_player_rounds (date_hour, date_day, brand_id, player_code, game_code, device_code, currency_code, exchange_rate, rounds_qty, events_qty, total_bet, total_win, total_revenue, start_balance, end_balance, first_activity, last_activity)
        SELECT fd.date_hour,
            fd.date_hour::DATE,
            fd.brand_id,
            fd.player_code,
            fd.game_code,
            coalesce(fd.device_code, '') AS device_code,
            fd.currency_code,
            fd.exchange_rate,
            fd.rounds_qty,
            fd.events_qty,
            fd.total_bet,
            fd.total_win,
            fd.total_revenue,
            fd.start_balance,
            fd.end_balance,
            fd.first_activity,
            fd.last_activity
        FROM (
                SELECT date_hour
                    ,brand_id
                    ,player_code
                    ,game_code
                    ,currency_code
                    ,MAX(device_code)       AS device_code
                    ,Avg(exchange_rate)     AS exchange_rate
                    ,SUM(rounds_qty   )     AS rounds_qty
                    ,SUM(events_qty   )     AS events_qty
                    ,SUM(total_bet    )     AS total_bet
                    ,SUM(total_win    )     AS total_win
                    ,SUM(total_revenue)     AS total_revenue
                    ,(array_remove(array_agg(start_balance ORDER BY first_activity ASC), NULL))[1] AS start_balance
                    ,(array_remove(array_agg(end_balance ORDER BY last_activity DESC), NULL))[1] AS end_balance
                    ,Min(first_activity)    AS first_activity
                    ,MAX(last_activity)     AS last_activity
                FROM   (
                        SELECT date_hour
                                ,brand_id
                                ,player_code
                                ,game_code
                                ,device_code
                                ,currency_code
                                ,(SELECT cr2.rate
                                FROM   currency_rates cr2
                                WHERE  cr2.currency_code = t0.currency_code
                                    AND  cr2.rate_date <= t0.date_day
                                ORDER BY cr2.rate_date DESC LIMIT 1) AS exchange_rate
                                ,rounds_qty
                                ,events_qty
                                ,total_bet
                                ,total_win
                                ,total_revenue
                                ,start_balance
                                ,end_balance
                                ,first_activity
                                ,last_activity
                        FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_fresh t0
                        UNION ALL
                        SELECT date_hour
                                ,brand_id
                                ,player_code
                                ,game_code
                                ,NULL::VARCHAR AS device_code /* We can't use old value, because insert can fault due to current PK */
                                ,currency_code
                                ,exchange_rate
                                ,rounds_qty
                                ,events_qty
                                ,total_bet
                                ,total_win
                                ,total_revenue
                                ,start_balance
                                ,end_balance
                                ,first_activity
                                ,last_activity
                        FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_deleted
                        ) t
                GROUP BY date_hour, brand_id, game_code, currency_code, player_code
            ) fd;

    SELECT  MAX(max_inserted_at), COUNT(*)
    FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_fresh
    INTO    v_new_inserted_at, v_counter;

    DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_rounds_finished_fresh;
    DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_rounds_finished_deleted;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_player_rounds" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

        UPDATE bo_aggr_config SET
                conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
        WHERE  aggr_job_name = 'bo_aggr_player_rounds'
            AND  conf_key = 'lock_record';

    -- Log
    INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
        VALUES ('bo_aggr_player_rounds', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time), v_force_end_time);

        /* ~~~ bo_aggr_win_bets ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        v_job_start_time := clock_timestamp();
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets" started'; RETURN NEXT;

        SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_win_bets' AND conf_key = 'lock_record' FOR UPDATE;

        IF NOT FOUND THEN
            RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_win_bets/lock_record" pair';
        END IF;

        v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                                ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                                );

        /* Automatically prevent too huge intervals */
        IF (v_force_end_time - v_last_inserted_at) > v_huge_interval_win_bet THEN
            v_force_end_time := v_last_inserted_at + v_huge_interval_win_bet;
        END IF;

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh AS 
        SELECT date_trunc('HOUR', b.payment_date) AS payment_date_hour,
                b.brand_id,
                b.game_code,
                b.player_code,
                b.currency AS currency_code,
                COUNT(DISTINCT b.game_id) AS played_games_qty,
                SUM(case when b.transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN bet_rollback = FALSE THEN b.bet ELSE (-1)*b.bet END) end) AS total_bets,
                SUM(case when b.transaction_type IN ('jackpot', 'free_bet') then 0 else b.win end) AS total_wins,
                MAX(b.payment_date)::timestamp(0) as last_payment_ts,
                SUM(case when b.transaction_type = 'jackpot' then b.win else 0 end) as total_jp_wins,
                SUM(case when b.transaction_type = 'free_bet' then b.win else 0 end) as total_freebet_wins,
                MAX(COALESCE(inserted_at, payment_date)) AS max_inserted_at,
                SUM(debit) AS debit,
                SUM(credit) AS credit
        FROM  wallet_win_bet b
        WHERE COALESCE(inserted_at, payment_date) >= v_last_inserted_at
            AND COALESCE(inserted_at, payment_date) < v_force_end_time
            AND COALESCE(b.is_test, false) = false
                -- AND b.transaction_type is null
        GROUP BY b.player_code, b.brand_id, b.currency, b.game_code, date_trunc('HOUR', b.payment_date);

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted AS SELECT * FROM bo_aggr_win_bets LIMIT 0;
    WITH cte_delete_existing_data AS (
            DELETE FROM bo_aggr_win_bets d
            USING tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh f
            WHERE d.payment_date_hour = f.payment_date_hour
            AND d.brand_id          = f.brand_id
            AND d.game_code         = f.game_code
            AND d.currency_code     = f.currency_code
            AND d.player_code       = f.player_code
            RETURNING d.*
        )
    INSERT INTO tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted 
    SELECT * FROM cte_delete_existing_data;

    INSERT INTO bo_aggr_win_bets (payment_date_hour, payment_date_day, brand_id, game_code, player_code,
            currency_code, played_games_qty, total_bets, total_wins, last_payment_ts,
            exchange_rate, total_jp_wins, total_freebet_wins, debit, credit)
    SELECT
        d.payment_date_hour,
        d.payment_date_hour::DATE AS payment_date_day,
        d.brand_id,
        d.game_code,
        d.player_code,
        d.currency_code,
        /* bo_aggr_player_rounds - contains only finished rounds. TLU: This value is incorrect and should be fixed. wallet_win_bet should also has finished flags */
        COALESCE( (SELECT pr.rounds_qty
                    FROM   bo_aggr_player_rounds pr
                    WHERE  d.payment_date_hour = pr.date_hour
                    AND  d.brand_id = pr.brand_id
                    AND  d.game_code = pr.game_code
                    AND  d.currency_code = pr.currency_code
                    AND  d.player_code = pr.player_code
                    LIMIT 1), 0) AS played_games_qty,
        d.total_bets,
        d.total_wins,
        d.last_payment_ts,
        (SELECT cr2.rate
        FROM   currency_rates cr2
        WHERE  cr2.currency_code = d.currency_code
            AND  cr2.rate_date <= d.payment_date_hour::DATE
        ORDER BY cr2.rate_date DESC LIMIT 1) AS exchange_rate,
        d.total_jp_wins,
        d.total_freebet_wins,
        d.debit,
        d.credit
        FROM (
                SELECT payment_date_hour
                    ,brand_id
                    ,game_code
                    ,player_code
                    ,currency_code
                    --,SUM(played_games_qty) AS played_games_qty
                    ,SUM(total_bets) AS total_bets
                    ,SUM(total_wins) AS total_wins
                    ,MAX(last_payment_ts) AS last_payment_ts
                    ,SUM(total_jp_wins     ) AS total_jp_wins
                    ,SUM(total_freebet_wins) AS total_freebet_wins
                    ,SUM(debit) AS debit
                    ,SUM(credit) AS credit
                FROM   (
                        SELECT payment_date_hour
                                ,brand_id
                                ,game_code
                                ,player_code
                                ,currency_code
                                --,played_games_qty
                                ,total_bets
                                ,total_wins
                                ,last_payment_ts
                                ,total_jp_wins
                                ,total_freebet_wins
                                ,debit
                                ,credit
                        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh
                        UNION ALL
                        SELECT payment_date_hour
                                ,brand_id
                                ,game_code
                                ,player_code
                                ,currency_code
                                --,played_games_qty
                                ,total_bets
                                ,total_wins
                                ,last_payment_ts
                                ,total_jp_wins
                                ,total_freebet_wins
                                ,debit
                                ,credit
                        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted
                        ) uni_on
                GROUP BY payment_date_hour, brand_id, game_code, currency_code, player_code
            ) d;

        SELECT MAX(max_inserted_at), COUNT(*)
        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh
        INTO   v_new_inserted_at, v_counter;

        DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh;
        DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted;

        -- GET DIAGNOSTICS v_counter = ROW_COUNT;
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

            UPDATE bo_aggr_config SET
                conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
            WHERE  aggr_job_name = 'bo_aggr_win_bets'
            AND  conf_key = 'lock_record';

        -- Log
        INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
            VALUES ('bo_aggr_win_bets', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, v_new_inserted_at, v_force_end_time);


        /* ~~~ bo_aggr_win_bets_by_brand ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        v_job_start_time := clock_timestamp();
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets_by_brand" started'; RETURN NEXT;

        SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_win_bets_by_brand' AND conf_key = 'lock_record' FOR UPDATE;

        IF NOT FOUND THEN
            RAISE EXCEPTION 'Table "bo_aggr_win_bets_by_brand" has no valid record for "bo_aggr_win_bets_by_brand/lock_record" pair';
        END IF;

        /* To limit huge load intervals */
        v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                                ,current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)
                                ,v_last_inserted_at + v_huge_interval_win_bet
                                );


        WITH fresh_data as
        ( SELECT b.brand_id,
                b.currency as currency_code,
                SUM(case when transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN bet_rollback = FALSE THEN bet ELSE (-1)*bet END) end) as bet,
                SUM(case when transaction_type IN ('jackpot', 'free_bet') then 0 else win end) as win,
                SUM(case when transaction_type = 'jackpot' then win else 0 end) as jackpot_win,
                SUM(case when transaction_type = 'free_bet' then win else 0 end) as free_bet_win,
                COUNT(*) as events_count,
                MAX(b.payment_date) as last_payment_ts,
                MAX(COALESCE(inserted_at, payment_date)) AS max_inserted_at
            FROM wallet_win_bet b
            WHERE COALESCE(inserted_at, payment_date) > v_last_inserted_at
                AND COALESCE(inserted_at, payment_date) < v_force_end_time
                AND coalesce(b.is_test, false) = false
            GROUP BY b.brand_id, b.currency
        ),
        upsert_aggr AS
        ( INSERT INTO bo_aggr_win_bets_by_brand (brand_id, currency_code, bet, win, revenue, jackpot_win, free_bet_win)
                SELECT brand_id, currency_code, bet, win, (bet - win) as revenue, jackpot_win, free_bet_win
                FROM fresh_data
        ON CONFLICT ON CONSTRAINT bo_aggr_win_bets_by_brand_pkey DO
                UPDATE SET bet = bo_aggr_win_bets_by_brand.bet + EXCLUDED.bet,
                        win = bo_aggr_win_bets_by_brand.win + EXCLUDED.win,
                        revenue = bo_aggr_win_bets_by_brand.revenue + EXCLUDED.revenue,
                        jackpot_win = bo_aggr_win_bets_by_brand.jackpot_win + EXCLUDED.jackpot_win,
                        free_bet_win = bo_aggr_win_bets_by_brand.free_bet_win + EXCLUDED.free_bet_win
        )
        SELECT MAX(max_inserted_at), SUM(events_count)
        FROM fresh_data
        INTO v_new_inserted_at, v_counter;


        -- GET DIAGNOSTICS v_counter = ROW_COUNT;
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets_by_brand" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

            UPDATE bo_aggr_config SET
                conf_value = coalesce(v_new_inserted_at, v_force_end_time - '1 msec'::INTERVAL)
            WHERE  aggr_job_name = 'bo_aggr_win_bets_by_brand'
            AND  conf_key = 'lock_record';

        -- Log
        INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
            VALUES ('bo_aggr_win_bets_by_brand', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, v_new_inserted_at, v_force_end_time);

        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job finished'; RETURN NEXT;

        /* ~~~ Maintenance ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        -- Clear old logs
        DELETE FROM bo_aggr_history WHERE started_at < (current_date - Interval '1 MONTH');

        -- Reset config parameters
        reset work_mem;

        RETURN;
    END;
$function$
;
RESET search_path;

--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_aggr_refresh_jobs(TIMESTAMP, VARCHAR);
--rollback ALTER FUNCTION fnc_bo_aggr_refresh_jobs_before_4_36_0(TIMESTAMP, VARCHAR) RENAME TO fnc_bo_aggr_refresh_jobs;
--rollback RESET search_path;


--changeset timur.luchkin:2020-04-24-SWDB-136-hashid-support
--comment Add support for pg_hashids extension
CREATE EXTENSION IF NOT EXISTS pg_hashids WITH SCHEMA public;

--rollback DROP EXTENSION pg_hashids;

--changeset valdis.akmens:2020-03-25-SWS-16933-remove-rounds_history-table-from-fnc_bo_rounds_history endDelimiter:# stripComments:false
--comment Remove rounds_history table from fnc_bo_rounds_history
SET search_path TO swmanagement;

ALTER FUNCTION fnc_bo_rounds_history (VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN) RENAME TO fnc_bo_rounds_history_before_4_36_0;
ALTER FUNCTION fnc_bo_rounds_history_inner(VARCHAR[], VARCHAR, INT, INT, VARCHAR, BOOLEAN, VARCHAR[], BOOLEAN, VARCHAR) RENAME TO fnc_bo_rounds_history_inner_before_4_36_0;

CREATE OR REPLACE FUNCTION fnc_bo_rounds_history(p_where_filters character varying[], p_sort_by character varying[] DEFAULT NULL::character varying[], p_limit integer DEFAULT 20, p_offset integer DEFAULT 0, p_incl_sub_brands boolean DEFAULT false, p_incl_test boolean DEFAULT true)
 RETURNS SETOF rounds_finished
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************

    Object Name:   fnc_bo_rounds_history
    Purpose    : Provide read access to rounds_history table and rounds_unfinished and rounds_finished
                If not partition key in WHERE, than only to new partitions. If is partition key, than check pathman_config, and pathman_partition_list to see if we need:
                new table
                old table
                or both tables union
    History    :
        1.0.0(?)
            Date    : Oct 16, 2018
            Authors : Valdis Akmens
            Notes   : Release (SWS-6904)
        1.0.1
            Date    : Jan 30, 2019
            Authors : Valdis Akmens
            Notes   : Change SELECT list for tables to dynamic generated to automatically include new columns to function result (SWDB-82)
        1.0.2
            Date    : May 02, 2019
            Authors : Valdis Akmens
            Notes   : Add parameters for including sub-entities in result (SWS-9902)
                    Work with sub-entities moved to special function - fnc_bo_rounds_history_inner - to optimize performance.
        1.0.3
            Date    : May 30, 2019
            Authors : Valdis Akmens
            Notes   : Add support for the historical data in Rounds history related calls.
                    Added call to "swmanagement_archive.rounds_history" if is definied "started_at" in p_where_filters and it points to "swmanagement_archive.rounds_history"
                    time interval (SWS-10600)
        1.0.4
            Date    : Oct 14, 2019
            Authors : Valdis Akmens
            Notes   : Add support for "swmanagement_archive.rounds_finished" and remove unfinished rounds view "swmanagement.v_rounds_unfinished"
                    (SWDB-117)
        1.0.5
            Date    : Mar 25, 2020
            Authors : Valdis Akmens
            Notes   : Remove rounds_history table from "fnc_bo_rounds_history" and check on started_at parition key 
                    (SWS-16933)                    
    Sample run:
    SELECT brand_id,started_at,finished_at FROM fnc_bo_rounds_history(
                                        p_where_filters => '{"brand_id = 51", "started_at >=''2017-12-08 00:00:00''","started_at < ''2017-12-09 00:00:00''" }',
                                        p_sort_by => '{"finished_at DESC"}',
                                        p_limit => 20,
                                        p_offset => 0
                                        );
    SELECT brand_id,started_at,finished_at FROM fnc_bo_rounds_history(
                                        p_where_filters => '{"brand_id = 50", "started_at >=''2017-12-08 00:00:00''","started_at < ''2017-12-09 00:00:00''" }',
                                        p_sort_by => '{"finished_at DESC"}',
                                        p_limit => 20,
                                        p_offset => 0,
                                        p_incl_sub_brands => TRUE,
                                        p_incl_test => FALSE
                                        );
    SELECT * FROM fnc_bo_rounds_history(
                                            p_where_filters => '{"brand_id = 51", "started_at >=''2017-12-08 00:00:00''","started_at < ''2017-12-09 00:00:00''", "finished_at is not null" }'
                                            );
********************************************************************************************************/
DECLARE
    v_filter                VARCHAR;
    v_select                VARCHAR;
    v_select_unf            VARCHAR;
    v_where                 VARCHAR:='WHERE ';
    v_sort_by               VARCHAR:='ORDER BY ';
    v_limit                 VARCHAR:=COALESCE('LIMIT '||p_limit::TEXT,'');
    v_offset                VARCHAR:=COALESCE('OFFSET '||p_offset::TEXT,'');
    v_exec_sql              VARCHAR:='';
    v_line                  VARCHAR;
    v_rounds_table          VARCHAR:='swmanagement.rounds_finished';
    v_rounds_archive_table  VARCHAR:='swmanagement_archive.rounds_finished';
    v_unfinished_table      VARCHAR:='swmanagement.rounds_unfinished';
    v_old_archive_table     VARCHAR:='swmanagement_archive.rounds_history';
    v_partiton_key          VARCHAR:='finished_at';
    v_old_partiton_key      VARCHAR:='started_at';
    v_partiton_key_filters  VARCHAR[];
    v_is_in_actual          BOOLEAN:=FALSE;
    v_is_in_archive         BOOLEAN:=FALSE;
    v_sub_brands            VARCHAR[];
    v_brand_id              VARCHAR:='';
    v_range_min             TIMESTAMP;
    v_range_max             TIMESTAMP;
BEGIN

     /* Check mandatory params */
    IF p_where_filters IS NULL THEN
        RAISE EXCEPTION 'Filter for WHERE must be defined!';
    END IF;

    -- Get partition key from pathman configuration table
    SELECT expr
        INTO v_old_partiton_key
    FROM public.pathman_config
    WHERE
        partrel  = v_old_archive_table::regclass
    AND parttype = 2;

    -- Get column list
    SELECT 'SELECT '||string_agg(attname, ', ' ORDER BY attnum)||' FROM '
    INTO v_select
    FROM pg_catalog.pg_attribute
    WHERE  attrelid = v_rounds_table::regclass
    AND    attnum > 0
    AND    NOT attisdropped;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No column list for %.', v_rounds_table;
    END IF;

    -- Make select list for swmanagement.rounds_unfinished
    v_select_unf:=REPLACE(v_select,'finished_at','NULL::TIMESTAMP AS finished_at');

    --Build ORDER BY
    IF p_sort_by IS NULL OR p_sort_by = '{}'::VARCHAR[] THEN
        v_sort_by:= v_sort_by||v_partiton_key||' DESC';
    ELSE
        FOREACH v_filter IN ARRAY p_sort_by LOOP
            v_sort_by:=v_sort_by||CASE WHEN array_position(p_sort_by, v_filter) <> 1 THEN ' ,' ELSE '' END ||chr(10)||v_filter;
        END LOOP;
    END IF;

    --Build WHERE filter
    FOREACH v_filter IN ARRAY p_where_filters LOOP
        v_where:=v_where||CASE WHEN array_position(p_where_filters, v_filter) <> 1 THEN ' AND' ELSE '' END ||chr(10)||v_filter;
        -- Get datetime values from partition key filters, to match them with pathman configuration tables
        IF  (v_filter ILIKE v_partiton_key||'%' AND v_filter NOT ILIKE '%true%' AND v_filter NOT ILIKE '%false%') THEN
            v_partiton_key_filters:= array_append(v_partiton_key_filters, (SELECT substring(v_filter from '\''.*?\''' ))::VARCHAR);
        END IF;
    END LOOP;

    -- Check if exists partition key filters
    IF v_partiton_key_filters IS NOT NULL THEN

        v_range_max:= (SELECT                     
                            GREATEST(
                                (SELECT MAX(range_max::timestamp)
                                FROM public.pathman_partition_list
                                WHERE parent IN (v_old_archive_table::regclass)
                                ), 
                                (SELECT MAX(range_max::timestamp)
                                FROM public.pathman_partition_list
                                WHERE parent IN (v_rounds_archive_table::regclass)
                                )                         
                        ));
        -- Check if partition filters points to archive table
        FOREACH v_filter IN ARRAY v_partiton_key_filters LOOP
            SELECT TRUE AS in_interval
                INTO v_is_in_archive
            FROM
            (
                SELECT v_range_max AS range_max
            ) AS q
            WHERE v_filter::timestamp < range_max;
        EXIT WHEN v_is_in_archive ;
        END LOOP;
        v_is_in_archive:=COALESCE(v_is_in_archive,FALSE);
    END IF;

    IF p_incl_sub_brands = FALSE THEN
            -- Build EXEC string based on which tables need to use
            CASE
                -- Add swmanagement_archive.rounds_history table to union
                WHEN v_is_in_archive THEN
                    v_exec_sql:=v_select||' (('||chr(10)||
                    v_select||v_rounds_table||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||') 
                        UNION ALL ( SELECT * FROM ( '||
                    v_select_unf||v_unfinished_table||') AS q '||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                        UNION ALL ('||
                    v_select||v_old_archive_table||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                        UNION ALL ('||
                    v_select||v_rounds_archive_table||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                    ) AS q '||v_sort_by||chr(10)||v_limit||chr(10)||v_offset||';';
                ------------------------------------------------------------------------------------------------------------------------
                ELSE
                    v_exec_sql:=v_select||' (('||chr(10)||
                    v_select||v_rounds_table||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                        UNION ALL ( SELECT * FROM ( '||
                    v_select_unf||v_unfinished_table||') AS q '||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                    ) AS q '||v_sort_by||chr(10)||v_limit||chr(10)||v_offset||';';
            END CASE;
    ELSE
        -- If needed sub-brands get all brand_id in ARRAY
        FOREACH v_filter IN ARRAY p_where_filters LOOP
            IF v_filter ILIKE '%brand_id%' THEN
                    --RAISE INFO '[%]: v_filter : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_filter;
                    FOR v_brand_id IN  EXECUTE 'WITH RECURSIVE hierarchy AS
                                                (
                                                    SELECT brand_id, parent, name, type, 1 AS deep_level, title, key, path, is_test
                                                    FROM   (SELECT id as brand_id, parent, name, type, 1 AS deep_level, title, key, path, is_test FROM entities) AS x
                                                    WHERE  '||v_filter||'
                                                    UNION ALL
                                                    SELECT en.id AS brand_id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.key, en.path, en.is_test
                                                    FROM   entities en
                                                    INNER JOIN hierarchy h ON en.parent = h.brand_id
                                                )
                                                SELECT ''brand_id = ''||brand_id::VARCHAR AS sub_brand_id
                                                FROM   hierarchy
                                                WHERE  is_test = COALESCE(CASE WHEN '||p_incl_test||' <> FALSE THEN NULL ELSE '||p_incl_test||' END, is_test);' LOOP
                        --RAISE INFO '[%]: v_brand_id : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_brand_id;
                        v_sub_brands:= array_append(v_sub_brands, v_brand_id);
                    END LOOP;
            END IF;
        END LOOP;
        -- Call sub-function for brand_id array
        v_exec_sql:=v_select||'( '||chr(10)||
                'SELECT * FROM fnc_bo_rounds_history_inner( '||quote_literal(p_where_filters::TEXT)||', '''||v_sort_by::TEXT||''', '||p_limit::TEXT||', '||p_offset::TEXT||', '''||v_select||''' ,'||v_is_in_archive::TEXT||', '''||v_sub_brands::TEXT||''', TRUE, NULL
)'||chr(10)||
                ') AS x '||v_sort_by||chr(10)||v_limit||chr(10)||v_offset||';';
    END IF;

    --RAISE INFO '[%]: v_exec_sql: %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),chr(10)||v_exec_sql;

    -- Check execution plan
    /*FOR v_line IN EXECUTE 'EXPLAIN ANALYZE '||v_exec_sql LOOP
        RAISE INFO '% ' , v_line;
    END LOOP;*/
    RETURN QUERY
    EXECUTE v_exec_sql;

END;
$function$
;


CREATE OR REPLACE FUNCTION fnc_bo_rounds_history_inner(p_where_filters character varying[], p_sort_by character varying, p_limit integer, p_offset integer, p_select character varying, p_is_in_archive boolean, p_sub_brands character varying[], p_inital_call boolean, p_brand_id character varying)
 RETURNS SETOF rounds_finished
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************

    Object Name:   fnc_bo_rounds_history_inner
    Purpose    : Provide read access to rounds_history table and rounds_unfinished and rounds_finished
                If not partition key in WHERE, than only to new partitions. If is partition key, than check pathman_config, and pathman_partition_list to see if we need:
                new table
                old table
                or both tables union
                This function is called from fnc_bo_rounds_history in case when is needed return records for sub-brands
    History    :
        1.0.0(?)
            Date    : Jun 03, 2019
            Authors : Valdis Akmens
            Notes   : Release (SWS-9902)
        1.0.4
            Date    : Oct 14, 2019
            Authors : Valdis Akmens
            Notes   : Add support for "swmanagement_archive.rounds_finished" and remove unfinished rounds view "swmanagement.v_rounds_unfinished"
                    (SWDB-117)
        1.0.5
            Date    : Mar 25, 2020
            Authors : Valdis Akmens
            Notes   : Remove rounds_history table from "fnc_bo_rounds_history"
                    (SWS-16933)   
    Sample run:
    SELECT * FROM fnc_bo_rounds_history_inner(
                                            '{"finished_at >=''2018-05-08 10:00:00''","finished_at < ''2018-05-08 13:00:00''", "brand_id = 51"}','ORDER BY finished_at DESC', 20, 10,'SELECT * FROM ' , FALSE, '{"brand_id = 51", "brand_id = 211"}', TRUE, NULL
                                            );
********************************************************************************************************/
DECLARE
    v_filter                VARCHAR;
    v_select                VARCHAR;
    v_select_unf            VARCHAR;
    v_where                 VARCHAR:='WHERE 1=1 ';
    v_sort_by               VARCHAR:='ORDER BY ';
    v_limit                 VARCHAR:=COALESCE('LIMIT '||p_limit::TEXT,'');
    v_offset                VARCHAR:=COALESCE('OFFSET '||p_offset::TEXT,'');
    v_exec_sql              VARCHAR:='';
    v_line                  VARCHAR;
    v_rounds_table          VARCHAR:='swmanagement.rounds_finished';
    v_rounds_archive_table  VARCHAR:='swmanagement_archive.rounds_finished';
    v_unfinished_table      VARCHAR:='swmanagement.rounds_unfinished';
    v_old_archive_table     VARCHAR:='swmanagement_archive.rounds_history';
    v_partiton_key          VARCHAR:='finished_at';
    v_old_partiton_key      VARCHAR:='started_at';
    v_partiton_key_filters  VARCHAR[];
    v_is_in_actual          BOOLEAN:=FALSE;
    v_is_in_archive         BOOLEAN:=FALSE;
    v_sub_brand             VARCHAR;
    v_brand_id              VARCHAR:='';
BEGIN

    v_select_unf:=REPLACE(p_select,'finished_at', 'NULL::timestamp AS finished_at');

    IF p_inital_call = TRUE THEN

        FOREACH v_sub_brand IN ARRAY p_sub_brands LOOP
            RETURN QUERY SELECT * FROM fnc_bo_rounds_history_inner(p_where_filters, p_sort_by, p_limit, p_offset, p_select, p_is_in_archive, NULL, FALSE, v_sub_brand);
        END LOOP;

        RETURN;

    ELSE
        --Build WHERE filter
        FOREACH v_filter IN ARRAY p_where_filters LOOP
            IF v_filter ILIKE '%brand_id%' THEN
                v_filter:= p_brand_id;
            END IF;
            v_where:=v_where||' AND'||chr(10)||v_filter;
        END LOOP;

        -- Build EXEC string based on which tables need to use
        CASE
            -- Add swmanagement_archive.rounds_history table to union
            WHEN p_is_in_archive THEN
                v_exec_sql:=p_select||' (('||chr(10)||
                p_select||v_rounds_table||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                ||') 
                    UNION ALL ( SELECT * FROM ( '||
                v_select_unf||v_unfinished_table||') AS q '||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                ||')
                    UNION ALL ('||
                p_select||v_old_archive_table||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                ||')
                    UNION ALL ('||
                p_select||v_rounds_archive_table||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                ) AS q '||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)||';';
            ------------------------------------------------------------------------------------------------------------------------
            ELSE
                v_exec_sql:=p_select||' (('||chr(10)||
                p_select||v_rounds_table||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                ||') 
                    UNION ALL ( SELECT * FROM ( '||
                v_select_unf||v_unfinished_table||') AS q '||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                ||')
                ) AS q '||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)||';';
        END CASE;

        --RAISE INFO '[%]: v_exec_sql: %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),chr(10)||v_exec_sql;

        -- Check execution plan
        /*FOR v_line IN EXECUTE 'EXPLAIN ANALYZE '||v_exec_sql LOOP
            RAISE INFO '% ' , v_line;
        END LOOP;*/

        RETURN QUERY
            EXECUTE v_exec_sql;
    END IF;

END;
$function$
;

RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_rounds_history(VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN);
--rollback DROP FUNCTION IF EXISTS fnc_bo_rounds_history_inner(VARCHAR[], VARCHAR, INT, INT, VARCHAR, BOOLEAN, VARCHAR[], BOOLEAN, VARCHAR);
--rollback ALTER FUNCTION fnc_bo_rounds_history_before_4_36_0 (VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN) RENAME TO fnc_bo_rounds_history;
--rollback ALTER FUNCTION fnc_bo_rounds_history_inner_before_4_36_0 (VARCHAR[], VARCHAR, INT, INT, VARCHAR, BOOLEAN, VARCHAR[], BOOLEAN, VARCHAR) RENAME TO fnc_bo_rounds_history_inner;
--rollback RESET search_path;