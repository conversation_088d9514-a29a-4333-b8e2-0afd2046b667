--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement, public;
--example: CREATE TABLE tmp(id int);
--example: --rollback DROP TABLE tmp;


--changeset andrey.shmigiro:2019-12-16-SWS-XXXX-start-release-4.29.0
--comment label for 4.29.0
select now();
--rollback select now();


--changeset anastasia.kostyukova:2019-12-27-SWS-15204-new-pop-merchant-params
--comment add new merchant parameter to support shooting games for pop
UPDATE swmanagement.merchant_types SET schema = schema || '{
"walletPerGame": {
"type": "boolean",
"title": "MERCHANT.PARAMETERS.walletPerGame",
"defaultValue": false
}
}'::JSONB
WHERE type IN('pop_asia', 'pop_moorgate', 'pop_eu', 'pop');
--rollback UPDATE swmanagement.merchant_types SET schema = schema - 'walletPerGame' WHERE type IN('pop_asia', 'pop_moorgate', 'pop_eu', 'pop');


--changeset sergey.malkov:2020-01-16-DEVOPS-7322-[Asia][PROD]-Queries-that-requests-huge-amount-data endDelimiter:# stripComments:false
--comment add stored function to add game to the child hierarchy, add function for jackpot settings validation before any changes
SET search_path TO swmanagement;
CREATE OR REPLACE FUNCTION fnc_validate_entity_game_jp_settings(p_entity_id INTEGER, p_game_code VARCHAR, validation_result OUT BOOLEAN)
 RETURNS BOOLEAN
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name: fnc_validate_entity_game_jp_settings
    Purpose    : Validate jackpot settings of parent entity_games. All jackpot types in game.features should be defined at least in one of parent entity_games
    History    :
    1.0.0
        Date    : Dec 16, 2019
        Authors : Sergey Malkov
        Notes   : Release (DEVOPS-7322)
    Example ('sw_ps')
            game.features = {"jackpotTypes":["sw-grand-pot-shot","sw-super-shot"]}
            valid parent  entity_game.settings = {"jackpotId":{"sw-super-shot":"SW-SUPER-SHOT_AUTO","sw-grand-pot-shot":"SW-GRAND-POT-SHOT_AUTO"}}
            invalid parent  entity_game.settings = null or entity_game.settings = {"jackpotId":{"sw-super-shot":"SW-SUPER-SHOT_AUTO"}}

    Sample run(CD2):
    SELECT * FROM fnc_validate_entity_game_jp_settings( p_entity_id => 98302,
                                        p_game_code => 'sw_ps'
                                        );
********************************************************************************************************/
DECLARE
BEGIN

     /* Check mandatory params */
    IF p_entity_id IS NULL OR p_game_code IS NULL OR p_game_code ='' THEN
        RAISE EXCEPTION 'Parameters must be defined!';
    END IF;

    WITH RECURSIVE
    game_sub AS (SELECT games.id, features FROM games JOIN game_providers AS gapr ON provider_id = gapr.id WHERE games.code = p_game_code AND features IS NOT NULL AND games.status = 'available' AND gapr.status = 'normal'),
    ent_game_id AS (SELECT enga.id AS parent_eg_id FROM entities AS ent JOIN entity_games AS enga ON enga.entity_id = ent.parent  JOIN game_sub  AS game ON game.id = enga.game_id WHERE ent.id = p_entity_id),
    enga_hier_up
        AS
        (
            SELECT id, parent_entity_game_id, settings, 0 AS deep_level
            FROM entity_games
            WHERE id = (SELECT parent_eg_id FROM ent_game_id)
            UNION ALL
            SELECT enga.id, enga.parent_entity_game_id, enga.settings,  h.deep_level + 1 AS deep_level
            FROM enga_hier_up AS h
            INNER JOIN entity_games AS enga ON enga.id = h.parent_entity_game_id
    ),
    hier_settings AS
    (
        SELECT (settings->> 'jackpotId')::jsonb AS jp_settings from  enga_hier_up WHERE  settings->> 'jackpotId'  IS NOT null
    ),
    jackpot_types_from_features AS (SELECT jsonb_array_elements_text((features->>'jackpotTypes')::jsonb) AS jp_types_from_features FROM game_sub),
    existing_jp_types_from_settings AS (SELECT DISTINCT(jp_types_from_features) FROM  jackpot_types_from_features JOIN hier_settings AS s ON  jp_settings->jp_types_from_features IS NOT NULL)
    SELECT COUNT(*) =( SELECT COUNT(*) FROM existing_jp_types_from_settings) INTO validation_result FROM  jackpot_types_from_features;

    RETURN;
END;
$function$;

CREATE OR REPLACE FUNCTION fnc_add_entity_games(p_entity_id INTEGER,p_game_codes VARCHAR[], po_games_added OUT INTEGER)
 RETURNS INTEGER
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name: fnc_add_entity_games
    Purpose    : Add records in entity_games base for given entity_id and its childs. Records based on parents entity_games record.
    History    :
        1.0.0
            Date    : Dec 16, 2019
            Authors : Valdis Akmens
            Notes   : Release (DEVOPS-7322)
    Sample run:
    SELECT * FROM fnc_add_entity_games( p_entity_id => 256,
                                        p_game_codes => '{sw_ps, sw_mr, sw_gs}'::VARCHAR[]
                                        );
********************************************************************************************************/
DECLARE
    v_code  VARCHAR;
BEGIN

     /* Check mandatory params */
    IF p_entity_id IS NULL OR p_game_codes IS NULL OR p_game_codes ='{}' THEN
        RAISE EXCEPTION 'Parameters must be defined!';
    END IF;

    FOREACH v_code IN ARRAY p_game_codes LOOP
            IF fnc_validate_entity_game_jp_settings(p_entity_id, v_code) = FALSE THEN
                RAISE EXCEPTION 'Jackpot settings is not valid for game %', v_code;
            END IF;
    END LOOP;

    -- Get full hierarchy tree for entity UP and DOWN direction for given entity_id
    WITH RECURSIVE cte_hier_down
    AS
    (
        SELECT id, parent, name, type,  0 AS deep_level, title,  path, is_test
        FROM entities
        WHERE id = p_entity_id
        UNION ALL
        SELECT en.id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.path, en.is_test
        FROM entities AS en
        INNER JOIN cte_hier_down AS h ON en.parent = h.id
    ),
    -- Get parent of p_entity_id entity_game record which will be used for new records
    cte_entity_game AS (
        SELECT enga.*
        FROM entity_games      AS enga
        JOIN games             AS game ON enga.game_id = game.id
        JOIN game_providers    AS gapr ON game.provider_id = gapr.id
        JOIN cte_hier_down     AS cthi ON enga.entity_id = cthi.parent
        WHERE
            game.status = 'available'
        AND gapr.status = 'normal'
        AND game.code = ANY(p_game_codes)
        AND cthi.deep_level = 0
    ), cte_ins AS (
        INSERT INTO entity_games (entity_id, game_id, created_at, updated_at, parent_entity_game_id, settings, status, royalties, limit_filters, url_params)
        SELECT cthi.id AS entity_id,cega.game_id,NOW(),NOW(),cega.id AS parent_entity_game_id,NULL AS settings,cega.status,cega.royalties,cega.limit_filters, NULL AS url_params
        FROM cte_hier_down AS cthi
        CROSS JOIN cte_entity_game AS cega
        WHERE cthi.deep_level >=0
        AND NOT EXISTS(SELECT id FROM entity_games AS x WHERE cthi.id = x.entity_id AND cega.game_id = x.game_id)
        RETURNING *
    )
    SELECT COUNT(*)
    FROM cte_ins
    INTO po_games_added
    ;

    RETURN;
END;
$function$;

ALTER FUNCTION fnc_add_entity_games(p_entity_id integer, p_game_codes character varying[], OUT po_games_added integer) OWNER TO swmanagement;
ALTER FUNCTION fnc_validate_entity_game_jp_settings(p_entity_id integer, p_game_code character varying, OUT validation_result boolean) OWNER TO swmanagement;

RESET search_path;
--rollback DROP FUNCTION fnc_validate_entity_game_jp_settings(p_entity_id INTEGER, p_game_code VARCHAR, validation_result OUT BOOLEAN);
--rollback DROP FUNCTION fnc_add_entity_games(p_entity_id INTEGER,p_game_codes VARCHAR[], po_games_added OUT INTEGER);


--changeset vera.kruhliakova:2020-01-28-SWS-14351-session-interruption
--comment Add field interruption_reason to sessions_history_duplicates table
SET search_path TO swmanagement;
ALTER TABLE sessions_history_duplicates ADD COLUMN interruption_reason enum_sessions_history_interruption_reason;
COMMENT ON COLUMN sessions_history_duplicates.interruption_reason IS 'The reason why session has been interrupted';
RESET search_path;
--rollback ALTER TABLE sessions_history_duplicates DROP COLUMN interruption_reason;
