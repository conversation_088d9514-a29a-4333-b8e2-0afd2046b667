--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2022-09-01-SWS-36637-add-permission-entity-game-delete-cascade
--comment Add permission 'entity:game:delete-cascade'
SET search_path = swmanagement;
DELETE FROM permissions WHERE code IN ('entity:game:delete-cascade');
INSERT INTO permissions(code, description, created_at, updated_at) VALUES
('entity:game:delete-cascade', 'Remove game from entity with child entity games', NOW(), NOW());
UPDATE roles SET permissions = permissions - 'entity:game:delete-cascade' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["entity:game:delete-cascade"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM permissions WHERE code IN ('entity:game:delete-cascade');
--rollback UPDATE roles SET permissions = permissions - 'entity:game:delete-cascade' where id = 1;
--rollback RESET search_path;

--changeset vladimir.minakov:2022-09-05_SWS-36314-table-name-settings-per-operator
--comment Add 'title' and 'features' fields for 'entity_games'
SET search_path TO swmanagement;
ALTER TABLE entity_games ADD COLUMN title TEXT;
ALTER TABLE entity_games ADD COLUMN features JSONB;
COMMENT ON COLUMN entity_games.title IS 'title will overwrite game.title per entity';
COMMENT ON COLUMN entity_games.features IS 'features will overwrite game.features per entity';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE entity_games DROP COLUMN title;
--rollback ALTER TABLE entity_games DROP COLUMN features;
--rollback RESET search_path;


--changeset vladimir.minakov:2022-09-14_SWS-36864-add-column-operator-country-to-session-history
--comment Add 'operator_country' fields for 'sessions_history'
SET search_path TO swmanagement;
ALTER TABLE sessions_history ADD COLUMN operator_country VARCHAR(6);
COMMENT ON COLUMN sessions_history.operator_country IS 'Country player from operator';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_country;
--rollback RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE sessions_history ADD COLUMN operator_country VARCHAR(6);
COMMENT ON COLUMN sessions_history.operator_country IS 'Country player from operator';
RESET search_path;
--rollback SET search_path TO swmanagement_archive;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_country;
--rollback RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE sessions_history ADD COLUMN operator_country VARCHAR(6);
COMMENT ON COLUMN sessions_history.operator_country IS 'Country player from operator';
RESET search_path;
--rollback SET search_path TO swmanagement_archive_ro;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_country;
--rollback RESET search_path;
