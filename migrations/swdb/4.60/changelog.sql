--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset stepanov.aleksey:2021-05-13-SWS-26457-add-new-game-type runInTransaction:false
--comment Add new "live" game type
SET search_path = swmanagement;
ALTER TYPE enum_games_type ADD VALUE IF NOT EXISTS 'live';
UPDATE games SET type='live' WHERE provider_game_code in ('sw_live_erol', 'sw_live_cbac', 'sw_live_ncbac', 'sw_live_dt', 'sw_live_erol_rush', 'sw_live_bj');
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE games SET type='table' WHERE provider_game_code in ('sw_live_erol', 'sw_live_cbac', 'sw_live_ncbac', 'sw_live_dt', 'sw_live_erol_rush', 'sw_live_bj');
--rollback DELETE FROM pg_enum WHERE enumlabel = 'live' and enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_games_type');
--rollback RESET search_path;


--changeset valdis.akmens:2021-05-11-SWS-27170-bo_aggr_jp_player_contributions-partitioning endDelimiter:# stripComments:false
--comment Enable partitioning on bo_aggr_jp_player_contributions and adjust fnc_bo_aggr_refresh_jackpot_jobs
SET search_path TO swjackpot;

ALTER FUNCTION fnc_bo_aggr_refresh_jackpot_jobs (timestamp without time zone) RENAME TO fnc_bo_aggr_refresh_jackpot_jobs_4_60_0;

CREATE OR REPLACE FUNCTION fnc_bo_aggr_refresh_jackpot_jobs(p_force_end_hour timestamp without time zone)
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   fnc_bo_aggr_refresh_jackpot_jobs
   Purpose    :   To perform B/O aggregation jobs for the swjackpot schema
   History    :
      1.0.0
         Date    : Dec 18, 2017
         Authors : Timur Luchkin
         Notes   : Initial release (SWS-2880)

     1.0.1
         Date    : Mar 16, 2018
         Authors : Valdis Akmens
         Notes   : Add support for different base currencies for jackpots (BYDEVO-1766, SWS-4072)

     1.0.2
         Date    : May 28, 2018
         Authors : Valdis Akmens
         Notes   : Add aggregate fields "jp_win_log.seed" and "jp_win_log.progressive" from "jp_win_log" table  to tables:
                    bo_aggr_jp_player_contributions, bo_aggr_jp_brand_contributions   (SWS-4853)

     1.0.3
         Date    : May 07, 2021
         Authors : Valdis Akmens
         Notes   : Change aggregation logic to allow "bo_aggr_jp_player_contributions" partitioning (SWS-27170)

   Sample run:
      SELECT * FROM fnc_bo_aggr_refresh_jackpot_jobs (NULL);
      SELECT * FROM fnc_bo_aggr_refresh_jackpot_jobs ('2017-10-06 23:00:00');


~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   v_last_end_time         TIMESTAMP;
   v_new_inserted_at       TIMESTAMP;
   v_time_back_msec        INTEGER;
   v_cnt_players           BIGINT;
   v_cnt_brands            BIGINT;
   v_job_start_time        TIMESTAMP;
   v_job_history_id        BIGINT;

   v_force_end_time        TIMESTAMP;
   v_huge_interval         INTERVAL := Interval '10 days';
BEGIN
   log_time := clock_timestamp(); log_msg := 'INFO: JP aggregation job just started'; RETURN NEXT;

   /* Check if MDB */
   IF (SELECT pg_is_in_recovery()) THEN
      log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
      RETURN;
   END IF;

   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~ bo_aggr_jp_player_contributions ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~ bo_aggr_jp_brand_contributions  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   v_job_start_time := clock_timestamp();
   v_job_history_id := nextval ('seq_bo_aggr_jp_history');

   SELECT To_Timestamp(conf_value, 'YYYY-MM-DD HH24:MI:SS') INTO v_last_end_time FROM bo_aggr_jp_config WHERE aggr_job_name = 'bo_aggr_jp_player_contributions' AND conf_key = 'last_end_time' FOR UPDATE;
   IF NOT FOUND THEN
      RAISE EXCEPTION 'Table "bo_aggr_jp_config" has no valid record for "bo_aggr_jp_player_contributions/last_end_time" pair';
   END IF;

   /* Safe time to prevent mistakes due to lazy inserts */
   v_time_back_msec := 60 * 1000;

   /* Lets find first record on first run */
   IF v_last_end_time = to_timestamp('2016-01-01', 'YYYY-MM-DD') THEN

      SELECT date_trunc('second', Min(min_inserted_at))
      INTO   v_last_end_time
      FROM   (
               SELECT Min(Coalesce(inserted_at, trx_date)) AS min_inserted_at
               FROM   jp_contribution_log
               UNION
               SELECT Min(inserted_at) AS min_inserted_at
               FROM   jp_win_log
             ) t
      ;

      -- DEBUG:
      v_last_end_time := Coalesce(v_last_end_time, to_timestamp('2016-01-01', 'YYYY-MM-DD'));
   END IF;

   /* To apply manual end hour, if provided */
   v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                             ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                             );
   /* Automatically prevent too huge intervals */
   IF (v_force_end_time - v_last_end_time) > v_huge_interval THEN
      v_force_end_time := v_last_end_time + v_huge_interval;
   END IF;
   /* Just to be sure no microseconds here */
   v_force_end_time := date_trunc('second', v_force_end_time);

   log_time := clock_timestamp(); log_msg := 'INFO: Start aggregation of "bo_aggr_jp_player_contributions" and "bo_aggr_jp_brand_contributions" between ['||To_Char(v_last_end_time, 'YYYY-MM-DD HH24:MI:SS')||'; '||To_Char(v_force_end_time, 'YYYY-MM-DD HH24:MI:SS')||')'; RETURN NEXT;


    CREATE TEMP TABLE tmp_bo_aggr_jp_player_contributions_fresh (
        date_hour                   timestamp,
        brand_id                    INT,
        game_code                   varchar(255),
        player_code                 varchar(255),
        currency_code               CHAR(3),
        jackpot_id                  varchar(255),
        pool                        varchar(255),
        jp_currency_code            CHAR(3),
        seed_amount                 numeric,
        progressive_amount          numeric,
        total_bet_amount            numeric,
        jp_win_amount               numeric,
        seed_amount_jp_cur          numeric,
        progressive_amount_jp_cur   numeric,
        total_bet_amount_jp_cur     numeric,
        jp_win_amount_jp_cur        numeric,
        total_bet_count             BIGINT,
        jp_win_count                BIGINT,
        first_activity              timestamp,
        last_activity               timestamp,
        seed_win                    numeric,
        progressive_win             numeric
    );


   WITH
   cte_jp_contrib AS (
      SELECT date_trunc('HOUR', jcl.trx_date) AS date_hour
            ,jcl.brand_id
            ,jcl.game_code AS game_code
            ,jcl.player_code
            ,jcl.player_currency AS currency_code
            ,jcl.jackpot_id
            ,jcl.pool
            ,jcl.currency AS jp_currency_code --!
            ,Sum(jcl.seed / jcl.currency_rate) AS seed_amount
            ,Sum(jcl.progressive / jcl.currency_rate) AS progressive_amount
            ,Sum(jcl.contribution_amount)  AS total_bet_amount
            ,0::NUMERIC AS jp_win_amount
            ,Sum(jcl.seed) AS seed_amount_jp_cur--!
            ,Sum(jcl.progressive) AS progressive_amount_jp_cur--!
            ,Sum(jcl.contribution_amount * jcl.currency_rate) AS total_bet_amount_jp_cur--!
            ,0::NUMERIC AS jp_win_amount_jp_cur--!
            ,Count(*) AS total_bet_count
            ,0::BIGINT AS jp_win_count
            ,Min(jcl.trx_date) AS first_activity
            ,Max(jcl.trx_date) AS last_activity
            ,0::NUMERIC AS seed_win
            ,0::NUMERIC AS progressive_win
      FROM   jp_contribution_log jcl
      WHERE  Coalesce(jcl.inserted_at, jcl.trx_date) >= v_last_end_time
        AND  Coalesce(jcl.inserted_at, jcl.trx_date) <  v_force_end_time
      GROUP BY
             date_trunc('HOUR', jcl.trx_date)
            ,jcl.brand_id
            ,jcl.game_code
            ,jcl.player_code
            ,jcl.player_currency
            ,jcl.jackpot_id
            ,jcl.pool
            ,jcl.currency
   ),
   cte_jp_wins AS (
      SELECT date_trunc('HOUR', jwl.trx_date) AS date_hour
            ,jwl.brand_id
            ,jwl.game_code AS game_code
            ,jwl.player_code
            ,jwl.player_currency AS currency_code
            ,jwl.jackpot_id
            ,jwl.pool
            ,jwl.currency AS jp_currency_code
            ,0::NUMERIC AS seed_amount
            ,0::NUMERIC AS progressive_amount
            ,0::NUMERIC AS total_bet_amount
            ,Sum(jwl.win_amount) AS jp_win_amount
            ,0::NUMERIC AS seed_amount_jp_cur
            ,0::NUMERIC AS progressive_amount_jp_cur
            ,0::NUMERIC AS total_bet_amount_jp_cur
            ,Sum(jwl.win_amount * jwl.currency_rate) AS jp_win_amount_jp_cur
            ,0::BIGINT AS total_bet_count
            ,Count(*)               AS jp_win_count
            ,Min(jwl.trx_date)      AS first_activity
            ,Max(jwl.trx_date)      AS last_activity
            ,SUM(seed)              AS seed_win
            ,SUM(progressive)       AS progressive_win
      FROM   jp_win_log jwl
      WHERE  jwl.inserted_at >= v_last_end_time
        AND  jwl.inserted_at <  v_force_end_time
      GROUP BY
             date_trunc('HOUR', jwl.trx_date)
            ,jwl.brand_id
            ,jwl.game_code
            ,jwl.player_code
            ,jwl.player_currency
            ,jwl.jackpot_id
            ,jwl.pool
            ,jwl.currency
   ),
   cte_fresh_data AS (
      SELECT date_hour
            ,brand_id
            ,game_code
            ,player_code
            ,currency_code
            ,jackpot_id
            ,pool
            ,jp_currency_code
            ,Sum(seed_amount                    )       AS seed_amount
            ,Sum(progressive_amount             )       AS progressive_amount
            ,Sum(total_bet_amount               )       AS total_bet_amount
            ,Sum(jp_win_amount                  )       AS jp_win_amount
            ,Sum(seed_amount_jp_cur             )       AS seed_amount_jp_cur
            ,Sum(progressive_amount_jp_cur      )       AS progressive_amount_jp_cur
            ,Sum(total_bet_amount_jp_cur        )       AS total_bet_amount_jp_cur
            ,Sum(jp_win_amount_jp_cur           )       AS jp_win_amount_jp_cur
            ,Sum(total_bet_count)::BIGINT       AS total_bet_count
            ,Sum(jp_win_count)::BIGINT          AS jp_win_count
            ,Min(first_activity)                AS first_activity
            ,Max(last_activity)                 AS last_activity
            ,SUM(seed_win)                      AS seed_win
            ,SUM(progressive_win)               AS progressive_win
      FROM   (
               SELECT * FROM cte_jp_contrib
               UNION ALL
               SELECT * FROM cte_jp_wins
             ) t1
      GROUP BY
            date_hour
           ,brand_id
           ,game_code
           ,player_code
           ,currency_code
           ,jackpot_id
           ,pool
           ,jp_currency_code
   )
   INSERT INTO tmp_bo_aggr_jp_player_contributions_fresh
   SELECT * FROM cte_fresh_data;


   CREATE TEMP TABLE tmp_bo_aggr_jp_player_contributions_deleted AS SELECT * FROM bo_aggr_jp_player_contributions LIMIT 0;

    WITH
    cte_delete_existing_data AS (
        DELETE FROM bo_aggr_jp_player_contributions AS d
        USING tmp_bo_aggr_jp_player_contributions_fresh AS f
        WHERE  d.date_hour          = f.date_hour
            AND  d.brand_id         = f.brand_id
            AND  d.game_code        = f.game_code
            AND  d.currency_code    = f.currency_code
            AND  d.player_code      = f.player_code
            AND  d.jackpot_id       = f.jackpot_id
            AND  d.pool             = f.pool
            AND  d.jp_currency_code = f.jp_currency_code
        RETURNING d.*
    )
    INSERT INTO tmp_bo_aggr_jp_player_contributions_deleted
    SELECT * FROM cte_delete_existing_data;

    WITH cte_history AS
    (
        SELECT  date_hour, brand_id, game_code, player_code,currency_code,jackpot_id, pool,jp_currency_code,
            array_agg(history_job_id) AS history_job_id
        FROM
        (    SELECT
                    date_hour, brand_id, game_code, player_code,currency_code,jackpot_id, pool,jp_currency_code,
                    v_job_history_id AS history_job_id
            FROM   tmp_bo_aggr_jp_player_contributions_fresh
            UNION ALL
            SELECT
                    date_hour, brand_id, game_code, player_code,currency_code,jackpot_id, pool,jp_currency_code,
                    UNNEST(history_job_id)
            FROM   tmp_bo_aggr_jp_player_contributions_deleted
        ) AS x
        GROUP BY date_hour, brand_id, game_code, player_code,currency_code,jackpot_id, pool,jp_currency_code
    ),
    cte_insert_bo_aggr_jp_player_contributions AS (
    INSERT INTO bo_aggr_jp_player_contributions (date_hour,brand_id,game_code,player_code,currency_code,jackpot_id,pool,jp_currency_code,seed_amount,progressive_amount,total_bet_amount,jp_win_amount,seed_amount_jp_cur,progressive_amount_jp_cur,total_bet_amount_jp_cur,jp_win_amount_jp_cur,total_bet_count,jp_win_count,first_activity,last_activity,history_job_id, seed_win, progressive_win)
    SELECT
        date_hour,
        brand_id,
        game_code,
        player_code,
        currency_code,
        jackpot_id,
        pool,
        jp_currency_code,
        SUM(seed_amount)            AS seed_amount,
        SUM(progressive_amount)     AS progressive_amount,
        SUM(total_bet_amount)       AS total_bet_amount,
        SUM(jp_win_amount)          AS jp_win_amount,
        SUM(seed_amount_jp_cur)     AS seed_amount_jp_cur,
        SUM(progressive_amount_jp_cur) AS progressive_amount_jp_cur,
        SUM(total_bet_amount_jp_cur) AS total_bet_amount_jp_cur,
        SUM(jp_win_amount_jp_cur)   AS jp_win_amount_jp_cur,
        SUM(total_bet_count)        AS total_bet_count,
        SUM(jp_win_count)           AS jp_win_count,
        MIN(first_activity)         AS first_activity,
        MAX(last_activity)          AS last_activity,
        (SELECT f.history_job_id
        FROM cte_history AS f
        WHERE  t.date_hour          = f.date_hour
            AND  t.brand_id         = f.brand_id
            AND  t.game_code        = f.game_code
            AND  t.currency_code    = f.currency_code
            AND  t.player_code      = f.player_code
            AND  t.jackpot_id       = f.jackpot_id
            AND  t.pool             = f.pool
            AND  t.jp_currency_code = f.jp_currency_code)
                                    AS history_job_id,
        SUM(seed_win)               AS seed_win,
        SUM(progressive_win)        AS progressive_win
    FROM
    (
        SELECT
                date_hour,
                brand_id,
                game_code,
                player_code,
                currency_code,
                jackpot_id,
                pool,
                jp_currency_code,
                seed_amount,
                progressive_amount,
                total_bet_amount,
                jp_win_amount,
                seed_amount_jp_cur,
                progressive_amount_jp_cur,
                total_bet_amount_jp_cur,
                jp_win_amount_jp_cur,
                total_bet_count,
                jp_win_count,
                first_activity,
                last_activity,
                seed_win,
                progressive_win
        FROM   tmp_bo_aggr_jp_player_contributions_fresh
        UNION ALL
        SELECT
                date_hour,
                brand_id,
                game_code,
                player_code,
                currency_code,
                jackpot_id,
                pool,
                jp_currency_code,
                seed_amount,
                progressive_amount,
                total_bet_amount,
                jp_win_amount,
                seed_amount_jp_cur,
                progressive_amount_jp_cur,
                total_bet_amount_jp_cur,
                jp_win_amount_jp_cur,
                total_bet_count,
                jp_win_count,
                first_activity,
                last_activity,
                seed_win,
                progressive_win
        FROM   tmp_bo_aggr_jp_player_contributions_deleted
    ) AS t
    GROUP BY
            date_hour,
            brand_id,
            game_code,
            player_code,
            currency_code,
            jackpot_id,
            pool,
            jp_currency_code
    RETURNING date_hour,brand_id,game_code,player_code,currency_code,jackpot_id,pool,jp_currency_code)
    SELECT Count(*)
    FROM   cte_insert_bo_aggr_jp_player_contributions
    INTO   v_cnt_players;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_jp_player_contributions" completed. Rows processed: '||quote_nullable(Coalesce(v_cnt_players, 0)); RETURN NEXT;

--- Brands aggregation
    WITH
    cte_upsert_brands AS (
        INSERT INTO bo_aggr_jp_brand_contributions (date_hour,brand_id,game_code,currency_code,seed_amount_jp_cur,progressive_amount_jp_cur,total_bet_amount_jp_cur,jp_win_amount_jp_cur,total_bet_count,jp_win_count,first_activity,last_activity,history_job_id, seed_win, progressive_win)
            SELECT date_hour,brand_id,game_code,jp_currency_code
                ,Sum(seed_amount_jp_cur        ) AS seed_amount_jp_cur
                ,Sum(progressive_amount_jp_cur ) AS progressive_amount_jp_cur
                ,Sum(total_bet_amount_jp_cur   ) AS total_bet_amount_jp_cur
                ,Sum(jp_win_amount_jp_cur      ) AS jp_win_amount_jp_cur
                ,Sum(total_bet_count        ) AS total_bet_count
                ,Sum(jp_win_count           ) AS jp_win_count
                ,Min(first_activity         ) AS first_activity
                ,Max(last_activity          ) AS last_activity
                ,Array_Append(NULL::BIGINT[], v_job_history_id) AS history_job_id
                ,SUM(seed_win)                AS seed_win
                ,SUM(progressive_win)         AS progressive_win
            FROM   tmp_bo_aggr_jp_player_contributions_fresh
            GROUP  BY date_hour,brand_id,game_code, jp_currency_code
        ON CONFLICT (date_hour, brand_id, game_code, currency_code) DO
            UPDATE SET
                seed_amount_jp_cur          = bo_aggr_jp_brand_contributions.seed_amount_jp_cur        + EXCLUDED.seed_amount_jp_cur
            ,progressive_amount_jp_cur   = bo_aggr_jp_brand_contributions.progressive_amount_jp_cur + EXCLUDED.progressive_amount_jp_cur
            ,total_bet_amount_jp_cur     = bo_aggr_jp_brand_contributions.total_bet_amount_jp_cur   + EXCLUDED.total_bet_amount_jp_cur
            ,jp_win_amount_jp_cur        = bo_aggr_jp_brand_contributions.jp_win_amount_jp_cur      + EXCLUDED.jp_win_amount_jp_cur
            ,total_bet_count             = bo_aggr_jp_brand_contributions.total_bet_count        + EXCLUDED.total_bet_count
            ,jp_win_count                = bo_aggr_jp_brand_contributions.jp_win_count           + EXCLUDED.jp_win_count
            ,first_activity              = Least(bo_aggr_jp_brand_contributions.first_activity, EXCLUDED.first_activity)
            ,last_activity               = Greatest(bo_aggr_jp_brand_contributions.last_activity, EXCLUDED.last_activity)
            ,history_job_id              = Array_Append(bo_aggr_jp_brand_contributions.history_job_id, v_job_history_id)
            ,seed_win                    = bo_aggr_jp_brand_contributions.seed_win                   + EXCLUDED.seed_win
            ,progressive_win             = bo_aggr_jp_brand_contributions.progressive_win            + EXCLUDED.progressive_win
        RETURNING date_hour, brand_id, game_code, currency_code
    )
    SELECT Count(*)
    FROM   cte_upsert_brands
    INTO   v_cnt_brands;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_jp_brand_contributions" completed. Rows processed: '||quote_nullable(Coalesce(v_cnt_brands, 0)); RETURN NEXT;

        -- Log
    INSERT INTO bo_aggr_jp_history (history_job_id,aggr_job_name,started_at,finished_at,records_processed,records_from_ts,records_to_ts,details)
        VALUES (v_job_history_id, 'bo_aggr_jp_player_contributions', v_job_start_time, clock_timestamp(), Coalesce(v_cnt_brands, 0) +  Coalesce(v_cnt_players, 0), v_last_end_time, v_force_end_time, NULL);

    UPDATE bo_aggr_jp_config SET
            conf_value = To_Char(v_force_end_time, 'YYYY-MM-DD HH24:MI:SS')
    WHERE  aggr_job_name = 'bo_aggr_jp_player_contributions'
        AND  conf_key = 'last_end_time';

   /* ~~~ Maintenance ~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
    DROP TABLE IF EXISTS tmp_bo_aggr_jp_player_contributions_deleted;
    DROP TABLE IF EXISTS tmp_bo_aggr_jp_player_contributions_fresh;

    -- Clear old logs
    DELETE FROM bo_aggr_jp_history WHERE finished_at < (current_date - Interval '1 MONTH');

    RETURN;
END;
$function$
;

RESET search_path;

SELECT public.set_init_callback('swjackpot.bo_aggr_jp_player_contributions', 'public.pathman_callback(jsonb)');
SELECT public.create_range_partitions('swjackpot.bo_aggr_jp_player_contributions', 'date_hour',  date_trunc('month', (SELECT COALESCE(MIN(date_hour),NOW()) FROM swjackpot.bo_aggr_jp_player_contributions))::date,INTERVAL '1 month', (SELECT CASE WHEN (SELECT COUNT(*) FROM swjackpot.bo_aggr_jp_player_contributions)=0 THEN 1 ELSE NULL END), false);

--rollback SET search_path = swjackpot;
--rollback DROP FUNCTION IF EXISTS fnc_bo_aggr_refresh_jackpot_jobs(timestamp without time zone);
--rollback ALTER FUNCTION fnc_bo_aggr_refresh_jackpot_jobs_4_60_0 (timestamp without time zone) RENAME TO fnc_bo_aggr_refresh_jackpot_jobs;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2021-05-17_SWS-27171_new_index_on_audits_session runInTransaction:false
--comment Created an index on the table audits_session for its partitioning
SET search_path = swmanagement;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audits_session_started_at ON audits_session (started_at);
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP INDEX idx_audits_session_started_at;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2021-05-17_SWS-27171_partition_audits_session
--comment Partitioned the big table swmanagement.audits_session with 1 week interval
SELECT public.set_init_callback (
                        relation        => 'swmanagement.audits_session'::regclass,
                        callback        => 'public.pathman_callback(jsonb)' );

SELECT public.create_range_partitions (
                        parent_relid    => 'swmanagement.audits_session'::regclass,
                        expression      => 'started_at',
                        start_value     => (SELECT date_trunc('week', COALESCE (min(started_at), now()))::date FROM swmanagement.audits_session),
                        p_interval      => '7 days'::interval,
                        p_count         => (CASE WHEN EXISTS (SELECT 1 FROM swmanagement.audits_session) THEN NULL ELSE 1 END),
                        partition_data  => FALSE );
                        
--rollback SELECT public.drop_partitions(parent_relid => 'swmanagement.audits_session'::regclass);
