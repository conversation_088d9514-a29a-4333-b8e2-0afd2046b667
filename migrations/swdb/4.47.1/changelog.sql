--liquibase formatted sql

--changeset artur.stepovyi:2020-11-09-SWS-14765-add-label-groups
--comment creates labels groups, updates labels to use group id, creates entity_labels relation
SET search_path = swmanagement;
CREATE TYPE enum_label_groups_types AS ENUM ('game', 'entity');
CREATE TABLE IF NOT EXISTS label_groups (
    id SERIAL PRIMARY KEY,
    label_group varchar(255) NOT NULL,
    label_type enum_label_groups_types NOT NULL,
    relation_type char NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE label_groups IS 'LabelGroup generic table';
COMMENT ON COLUMN label_groups.id IS 'Id of LabelGroup';
COMMENT ON COLUMN label_groups.label_group IS 'LabelGroup group name';
COMMENT ON COLUMN label_groups.label_type IS 'LabelGroup type of enum_label_groups_types';
COMMENT ON COLUMN label_groups.relation_type IS 'LabelGroup relation: o: one to one, m: one to many.';
COMMENT ON COLUMN label_groups.created_at IS 'Created At';
COMMENT ON COLUMN label_groups.updated_at IS 'Updated At';

INSERT INTO label_groups (label_group, label_type, relation_type)
SELECT "group"::TEXT
     ,'game'::enum_label_groups_types
     ,'m'
FROM labels
GROUP BY "group"::TEXT;

ALTER TABLE labels ADD group_id INTEGER;

UPDATE labels l SET
    group_id = lg.id
FROM  label_groups lg
WHERE lg.label_group = l."group"::TEXT;

ALTER TABLE labels ALTER group_id SET NOT NULL;
ALTER TABLE labels DROP "group";
DROP TYPE IF EXISTS enum_labels_group;

CREATE TABLE entity_labels (
    label_id integer NOT NULL,
    entity_id integer NOT NULL
);
COMMENT ON TABLE entity_labels IS 'Stores a list of references to entities and labels';
COMMENT ON COLUMN entity_labels.label_id IS 'Reference to label';
COMMENT ON COLUMN entity_labels.entity_id IS 'Reference to entity';

ALTER TABLE ONLY entity_labels
    ADD CONSTRAINT entity_labels_pkey PRIMARY KEY (label_id, entity_id);
ALTER TABLE ONLY entity_labels
    ADD CONSTRAINT entity_labels_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES entities(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY entity_labels
    ADD CONSTRAINT entity_labels_label_id_fkey FOREIGN KEY (label_id) REFERENCES labels(id) ON UPDATE CASCADE ON DELETE CASCADE;
CREATE INDEX IF NOT EXISTS idx_entity_labels_entity_id ON entity_labels (entity_id);

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP INDEX IF EXISTS idx_entity_labels_entity_id;
--rollback DROP TABLE IF EXISTS entity_labels;
--rollback CREATE TYPE enum_labels_group AS ENUM ('class', 'platform', 'feature', 'promotion');
--rollback ALTER TABLE labels ADD "group" enum_labels_group;
--rollback UPDATE labels l SET "group" = lg.label_group::enum_labels_group FROM  label_groups lg WHERE lg.id = l.group_id;
--rollback ALTER TABLE labels ALTER "group" SET NOT NULL;
--rollback ALTER TABLE labels DROP group_id;
--rollback DELETE FROM label_groups WHERE label_type = 'game'::enum_label_groups_types;
--rollback DROP TABLE IF EXISTS label_groups;
--rollback DROP TYPE IF EXISTS enum_label_groups_types;
--rollback RESET search_path;

--changeset aleh.rudzko:2020-11-15-SWS-14765-add-permission
--comment add new permissions for game/entity labels management

SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'entitylabels' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'entitylabels:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'entitylabels:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:entitylabels' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:entitylabels:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:entitylabels:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamelabels' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamelabels:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamelabels:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:label-groups' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:label-groups:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:label-groups:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'promolabels' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'promolabels:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'promolabels:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:promolabels' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:promolabels:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:promolabels:create' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["promolabels","promolabels:view", "promolabels:create", "keyentity:promolabels", "keyentity:promolabels:view", "keyentity:promolabels:create", "keyentity:label-groups", "keyentity:label-groups:view", "keyentity:label-groups:create","entitylabels", "entitylabels:view", "entitylabels:create", "keyentity:entitylabels", "keyentity:entitylabels:view", "keyentity:entitylabels:create", "gamelabels", "gamelabels:view", "gamelabels:create"]' WHERE id = 1;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'entitylabels' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entitylabels:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entitylabels:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:entitylabels' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:entitylabels:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:entitylabels:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'gamelabels' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'gamelabels:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'gamelabels:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:label-groups' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:label-groups:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:label-groups:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'promolabels' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'promolabels:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'promolabels:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:promolabels' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:promolabels:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:promolabels:create' WHERE id = 1;
--rollback RESET search_path;


--changeset aleh.rudzko:2020-11-17-SWS-14765-add-label-groups endDelimiter:# stripComments:false
--comment return some fields for backward compatibility
--validCheckSum: 7:0daea96ea17c97b1dd05cb6fa7ed4090

SET search_path = swmanagement;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_labels_group') THEN
        CREATE TYPE enum_labels_group AS ENUM ('class', 'platform', 'feature', 'promotion');
    END IF;
END $$;

ALTER TABLE labels ADD IF NOT EXISTS "group" enum_labels_group;

UPDATE labels l 
SET "group" = lg.label_group::enum_labels_group 
FROM label_groups lg 
WHERE lg.id = l.group_id AND lg.label_group IN ('class', 'platform', 'feature', 'promotion')
    AND l."group" IS NULL;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE labels DROP "group";
--rollback DROP TYPE IF EXISTS enum_labels_group;
--rollback RESET search_path;

--changeset aleh.rudzko:2020-11-17-SWS-23102-add-promotion-type runInTransaction:false
--comment add missing promotion type to enum

SET search_path = swmanagement;
ALTER TYPE enum_label_groups_types ADD VALUE IF NOT EXISTS 'promotion';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DELETE FROM pg_enum where enumlabel = 'promotion' and enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_label_groups_types');
--rollback RESET search_path;

--changeset aleh.rudzko:2020-11-19-SWS-23184-Migration-for-promotion-group
--comment change promotion group to correct type

SET search_path = swmanagement;
UPDATE label_groups SET label_type = 'promotion'::enum_label_groups_types WHERE label_group = 'promotion' AND label_type = 'game';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE label_groups SET label_type = 'game'::enum_label_groups_types WHERE label_group = 'promotion' AND label_type = 'promotion';
--rollback RESET search_path;