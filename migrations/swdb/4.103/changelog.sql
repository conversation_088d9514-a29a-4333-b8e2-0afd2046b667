--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset georgiy.kovrey:2023-02-06-SWS-39105-toEurMultiplier-decimal-value
--comment Alter column 'to_eur_multiplier' of 'game_limits_currencies' to be decimal type
SET search_path TO swmanagement;
ALTER TABLE game_limits_currencies ALTER COLUMN to_eur_multiplier TYPE numeric(25,15);
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE game_limits_currencies ALTER COLUMN to_eur_multiplier TYPE integer;
--rollback RESET search_path;


--changeset vladimir.minakov:2023-02-09-SWS-38396-saving-canceled-rounds
--comment Add table to save canceled bet/win for pariplay reconciliation
SET search_path TO swmanagement;

CREATE SEQUENCE bet_win_histories_id_seq;

CREATE TABLE bet_win_histories (
   id bigint DEFAULT nextval('bet_win_histories_id_seq'::regclass) PRIMARY KEY,
   brand_id integer NOT NULL,
   round_id bigint NOT NULL,
   player_code character varying(255) NOT NULL,
   game_code character varying(255),
   currency character(3) NOT NULL,
   trx_id character varying(255) NOT NULL,
   bet numeric,
   win numeric,
   balance_before numeric,
   balance_after numeric,
   is_test boolean,
   inserted_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_bet_win_histories_round_brand ON bet_win_histories USING btree (round_id, brand_id);

COMMENT ON TABLE bet_win_histories IS 'Canceled bet/win records';

COMMENT ON COLUMN bet_win_histories.brand_id IS 'Brand identifier';
COMMENT ON COLUMN bet_win_histories.player_code IS 'Player code';
COMMENT ON COLUMN bet_win_histories.player_code IS 'Player code';
COMMENT ON COLUMN bet_win_histories.game_code IS 'Game code in management system';
COMMENT ON COLUMN bet_win_histories.currency IS 'Currency code';
COMMENT ON COLUMN bet_win_histories.trx_id IS 'Transaction id';
COMMENT ON COLUMN bet_win_histories.win IS 'Win amount';
COMMENT ON COLUMN bet_win_histories.bet IS 'Bet amount';
COMMENT ON COLUMN bet_win_histories.balance_before IS 'Balance at the beginning of round';
COMMENT ON COLUMN bet_win_histories.balance_after IS 'Balance at the end of round';
COMMENT ON COLUMN bet_win_histories.is_test IS 'Is it test game';
COMMENT ON COLUMN bet_win_histories.inserted_at IS 'Timestamp when row was inserted';

--rollback SET search_path TO swmanagement;
--rollback DROP TABLE bet_win_histories;
--rollback DROP SEQUENCE IF EXISTS bet_win_histories_id_seq;

--changeset dmitriy.palaznik:2023-02-15_SWS-39140-add-is_hidden-to-spin_history
--comment Add is_hidden column in spins_history table
SET search_path = swmanagement;
ALTER TABLE spins_history ADD IF NOT EXISTS is_hidden boolean;
ALTER TABLE spins_history_duplicates ADD IF NOT EXISTS is_hidden boolean;
COMMENT ON COLUMN spins_history.is_hidden IS 'Optional flag. Hidden for history visualization';
COMMENT ON COLUMN spins_history_duplicates.is_hidden IS 'Optional flag. Hidden for history visualization';
RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE spins_history ADD IF NOT EXISTS is_hidden boolean;
COMMENT ON COLUMN spins_history.is_hidden IS 'Optional flag. Hidden for history visualization';
RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE spins_history ADD IF NOT EXISTS is_hidden boolean;
COMMENT ON COLUMN spins_history.is_hidden IS 'Optional flag. Hidden for history visualization';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE spins_history DROP IF EXISTS is_hidden;
--rollback ALTER TABLE spins_history_duplicates DROP IF EXISTS is_hidden;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive;
--rollback ALTER TABLE spins_history DROP IF EXISTS is_hidden;
--rollback RESET search_path;

--rollback SET search_path = swmanagement_archive_ro;
--rollback ALTER TABLE spins_history DROP IF EXISTS is_hidden;
--rollback RESET search_path;
