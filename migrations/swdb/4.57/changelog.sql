--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset aleh.rudzko:01-04-21-SWS-26183-add-constraint-to-available-sites
--comment create constraint in available_sites
SET search_path = swmanagement;
CREATE UNIQUE INDEX IF NOT EXISTS idx_available_sites_entity_id_code_key_and_code_is_not_null ON available_sites USING btree (entity_id, external_code) WHERE external_code IS NOT NULL;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP INDEX IF EXISTS idx_available_sites_entity_id_code_key_and_code_is_not_null;
--rollback RESET search_path