--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset anastasia.k<PERSON><PERSON>kova:2020-07-24-SWS-XXXX-start-release-4.43.0
--comment label for 4.43.0
select now();
--rollback select now();

--changeset nikita.senko:2020-08-04-SWS-18422-rollback-add-column
--comment del column
SET search_path = swmanagement;
ALTER TABLE entity_games DROP royalties;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entity_games ADD royalties numeric;
--rollback RESET search_path;


--changeset kashkov.ivan:2020-08-05-SWS-20316-merchant-player-game-group-release-4.43.0
--comment db changes for merchant player game group api
SET search_path = swmanagement;

CREATE SEQUENCE IF NOT EXISTS merchant_player_game_group_id_seq;

CREATE TABLE IF NOT EXISTS merchant_player_game_group (
	id INTEGER NOT NULL DEFAULT nextval('merchant_player_game_group_id_seq') PRIMARY KEY,
	merchant_id int4 NOT NULL,
	player_code varchar(255) NOT NULL,
	game_group_id int4 NOT NULL,
	created_at timestamp NULL,
	updated_at timestamp NULL,
	FOREIGN KEY (game_group_id) REFERENCES game_groups(id),
	FOREIGN KEY (merchant_id) REFERENCES merchants(brand_id),
	UNIQUE (merchant_id, player_code)
);

ALTER SEQUENCE merchant_player_game_group_id_seq OWNER TO swmanagement;
ALTER TABLE merchant_player_game_group OWNER TO swmanagement;

COMMENT ON TABLE merchant_player_game_group IS 'This is mapping for player of merchant to game group';
COMMENT ON COLUMN merchant_player_game_group.merchant_id IS 'Id of the hidden merchant';
COMMENT ON COLUMN merchant_player_game_group.player_code IS 'Player code';
COMMENT ON COLUMN merchant_player_game_group.game_group_id IS 'Game group id assigned to player';

CREATE INDEX IF NOT EXISTS idx_merchant_player_game_group_ggid ON merchant_player_game_group (game_group_id);

UPDATE roles SET permissions = permissions - 'merchant:player:gamegroup' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["merchant:player:gamegroup"]'::jsonb WHERE id = 1;

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP TABLE IF EXISTS public.merchant_player_game_group;
--rollback DROP SEQUENCE IF EXISTS public.merchant_player_game_group_id_seq;
--rollback UPDATE roles SET permissions = permissions - 'merchant:player:gamegroup' WHERE id = 1;
--rollback RESET search_path;


--changeset valdis.akmens:2020-08-11-SWS-20866-correction-fnc_bo_spins_history-fnc_bo_audits-for-archive-cluster-search-criteria endDelimiter:# stripComments:false
--comment Change logic to correctly search in archive and live clusters when only date in archive interval is defined.
SET search_path TO swmanagement;

ALTER FUNCTION fnc_bo_spins_history (VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN) RENAME TO fnc_bo_spins_history_before_4_43_0;
ALTER FUNCTION fnc_bo_audits (VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN) RENAME TO fnc_bo_audits_before_4_43_0;

CREATE OR REPLACE FUNCTION fnc_bo_spins_history(p_where_filters character varying[], p_sort_by character varying[] DEFAULT NULL::character varying[], p_limit integer DEFAULT 20, p_offset integer DEFAULT 0, p_incl_sub_brands boolean DEFAULT false, p_incl_test boolean DEFAULT true)
 RETURNS SETOF spins_history
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************

    Object Name:   fnc_bo_spins_history
    Purpose    :   Provide read access to spins_history table and archived spins_history_archive
                If not partition key in WHERE, than only to new partitions. If is partition key, than check pathman_config, and pathman_partition_list to see if we need:
                new table
                old table
                or both tables union
    History    :
        1.0.0
            Date    : Oct 16, 2018
            Authors : Valdis Akmens
            Notes   : Release (SWS-6904)
        1.0.1
            Date    : Jan 30, 2019
            Authors : Valdis Akmens
            Notes   : Change SELECT list for tables to dynamic generated to automatically include new columns to function result (SWDB-82)
        1.0.2
            Date    : May 02, 2019
            Authors : Valdis Akmens
            Notes   : Add parameters for including sub-entities in result (SWS-9902)
        1.0.3
            Date    : Aug 11, 2020
            Authors : Valdis Akmens
            Notes   : Correct criteria to search in historical and live clusters cluster (SWS-18819, SWS-20866)
    Sample run:
    SELECT brand_id,player_code,ts FROM fnc_bo_spins_history(
                                        p_where_filters => '{"brand_id = 51", "ts >=''2017-07-11 00:00:00''","ts < ''2017-07-19 00:00:00''" }',
                                        p_sort_by => '{"ts DESC"}',
                                        p_limit => 5,
                                        p_offset => 0
                                        );
    SELECT brand_id,player_code,ts FROM fnc_bo_spins_history(
                                        p_where_filters => '{"brand_id = 50", "ts >=''2017-07-11 00:00:00''","ts < ''2017-07-19 00:00:00''" }',
                                        p_sort_by => '{"ts DESC"}',
                                        p_limit => 5,
                                        p_offset => 0,
                                        p_incl_sub_brands => TRUE,
                                        p_incl_test => FALSE
                                        );
********************************************************************************************************/
DECLARE
    v_filter                VARCHAR;
    v_select                VARCHAR;
    v_where                 VARCHAR:='WHERE ';
    v_sort_by               VARCHAR:='ORDER BY ';
    v_limit                 VARCHAR:=COALESCE('LIMIT '||p_limit::TEXT,'');
    v_offset                VARCHAR:=COALESCE('OFFSET '||p_offset::TEXT,'');
    v_exec_sql              VARCHAR:='';
    v_line                  VARCHAR;
    v_actual_table          VARCHAR:='swmanagement.spins_history';
    v_archive_table         VARCHAR:='swmanagement_archive.spins_history';
    v_partiton_key          VARCHAR;
    v_partiton_key_filters  VARCHAR[];
    v_is_in_archive         BOOLEAN:=FALSE;
    v_sub_brands            VARCHAR:='';
    v_new_line              VARCHAR:=chr(10);
BEGIN

     /* Check mandatory params */
    IF p_where_filters IS NULL THEN
        RAISE EXCEPTION 'Filter for WHERE must be defined!';
    END IF;

    -- Get partition key from pathman configuration table
    SELECT expr
        INTO v_partiton_key
    FROM public.pathman_config
    WHERE
        partrel  = v_actual_table::regclass
    AND parttype = 2;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Table % has no valid partitions.', v_actual_table;
    END IF;

    -- Get column list
    SELECT 'SELECT '||string_agg(attname, ', ' ORDER BY attnum)||' FROM '
    INTO v_select
    FROM pg_catalog.pg_attribute
    WHERE  attrelid = v_actual_table::regclass
    AND    attnum > 0
    AND    NOT attisdropped;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No column list for %.', v_actual_table;
    END IF;

    --Build WHERE filter
    FOREACH v_filter IN ARRAY p_where_filters LOOP
        -- If includes sub-brands then need to find all brands for brand_id from p_where_filters
        IF p_incl_sub_brands = TRUE AND v_filter ILIKE '%brand_id%' THEN
                    --RAISE INFO '[%]: v_filter : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_filter;
                    EXECUTE 'WITH RECURSIVE hierarchy AS
                                            (
                                                SELECT brand_id, parent, name, type, 1 AS deep_level, title, key, path, is_test
                                                FROM   (SELECT id as brand_id, parent, name, type, 1 AS deep_level, title, key, path, is_test FROM entities) AS x
                                                WHERE  '||v_filter||'
                                                UNION ALL
                                                SELECT en.id AS brand_id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.key, en.path, en.is_test
                                                FROM   entities en
                                                INNER JOIN hierarchy h ON en.parent = h.brand_id
                                            )
                                            SELECT ''brand_id IN (''|| string_agg(brand_id::varchar, '', '' ORDER BY brand_id) ||'')''
                                            FROM   hierarchy
                                            WHERE  is_test = COALESCE(CASE WHEN '||p_incl_test||' <> FALSE THEN NULL ELSE '||p_incl_test||' END, is_test);' INTO v_sub_brands;
                    --RAISE INFO '[%]: v_sub_brands : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_sub_brands;
                    v_filter:= v_sub_brands;
        END IF;

        v_where:=v_where||CASE WHEN array_position(p_where_filters, v_filter) <> 1 THEN ' AND' ELSE '' END ||v_new_line||v_filter;

        -- Get datetime values from partition key filters, to match them with pathman configuration tables
        IF v_filter ILIKE v_partiton_key||'%' THEN
            v_partiton_key_filters:= array_append(v_partiton_key_filters, (SELECT substring(v_filter from '\''.*?\''' ))::VARCHAR);
        END IF;
    END LOOP;

    --Build ORDER BY
    IF p_sort_by IS NULL OR p_sort_by = '{}'::VARCHAR[] THEN
        v_sort_by:= v_sort_by||v_partiton_key||' DESC';
    ELSE
        FOREACH v_filter IN ARRAY p_sort_by LOOP
            v_sort_by:=v_sort_by||CASE WHEN array_position(p_sort_by, v_filter) <> 1 THEN ' ,' ELSE '' END ||v_new_line||v_filter;
        END LOOP;
    END IF;

    -- Check if exists partition key filters
    IF v_partiton_key_filters IS NOT NULL THEN
        --RAISE INFO '[%]: Partition key exists in WHERE ', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS');

        -- Check if partition filters points to archive table
        FOREACH v_filter IN ARRAY v_partiton_key_filters LOOP
            SELECT TRUE AS in_interval
                INTO v_is_in_archive
            FROM
            (
                SELECT
                    (SELECT MIN(range_min::timestamp)
                    FROM public.pathman_partition_list
                    WHERE parent IN (v_archive_table::regclass)
                    ) AS range_min,
                    (SELECT MAX(range_max::timestamp)
                    FROM public.pathman_partition_list
                    WHERE parent IN (v_archive_table::regclass)
                    ) AS range_max
            ) AS q
            WHERE v_filter::timestamp BETWEEN range_min AND range_max;
        EXIT WHEN v_is_in_archive ;
        END LOOP;
        v_is_in_archive:=COALESCE(v_is_in_archive,FALSE);

        -- Build EXEC string based on which tables need to use
        IF v_is_in_archive = TRUE THEN 
            v_exec_sql:=v_select||' (('||v_new_line||
            v_select||v_actual_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
            ||') UNION ALL ('||
            v_select||v_archive_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
            ||')) AS q '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
        ELSE 
            v_exec_sql:=v_select||v_actual_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
        END IF;
    ELSE
        -- If partition key doesnt exist in WHERE use only actual table
        v_exec_sql:=v_select||v_actual_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
    END IF;

    --RAISE INFO '[%]: v_exec_sql: %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_new_line||v_exec_sql;

    RETURN QUERY
        EXECUTE v_exec_sql
        ;
END;
$function$
;

CREATE OR REPLACE FUNCTION fnc_bo_audits(p_where_filters character varying[], p_sort_by character varying[] DEFAULT NULL::character varying[], p_limit integer DEFAULT 20, p_offset integer DEFAULT 0, p_incl_sub_brands boolean DEFAULT false, p_incl_test boolean DEFAULT true)
 RETURNS SETOF audits
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************

    Object Name:   fnc_bo_audits
    Purpose    :   Provide read access to audits table and archived audits_archive
                If not partition key in WHERE, than only to new partitions. If is partition key, than check pathman_config, and pathman_partition_list to see if we need:
                new table
                old table
                or both tables union
    History    :
        1.0.0
            Date    : Oct 16, 2018
            Authors : Valdis Akmens
            Notes   : Release (SWS-6904)
        1.0.1
            Date    : Jan 30, 2019
            Authors : Valdis Akmens
            Notes   : Change SELECT list for tables to dynamic generated to automatically include new columns to function result (SWDB-82)
        1.0.2
            Date    : May 02, 2019
            Authors : Valdis Akmens
            Notes   : Add parameters for including sub-entities in result (SWS-9902)
        1.0.3
            Date    : Aug 02, 2019
            Authors : Valdis Akmens
            Notes   : Postgres optimizator chooses wrong index when WHERE filter is definied with entity_id IN (..,..,)
                        it should choose index (entity_id, ts), not (ts) to force use right index string:", entity_id" is added to ORDER BY (SWDB-104)
        1.0.4
            Date    : Aug 11, 2020
            Authors : Valdis Akmens
            Notes   : Correct criteria to search in historical and live clusters (SWS-18819)
    Sample run:
    SELECT audit_id, entity_id, ts,initiator_type FROM fnc_bo_audits(
                                        p_where_filters => '{"entity_id = 51", "ts >=''2017-07-11 00:00:00''","ts < ''2017-07-19 00:00:00''" }',
                                        p_sort_by => '{"entity_id DESC"}',
                                        p_limit => 5,
                                        p_offset => 0
                                        );
    SELECT audit_id, entity_id, ts,initiator_type FROM fnc_bo_audits(
                                        p_where_filters => '{"entity_id = 50", "ts >=''2017-07-11 00:00:00''","ts < ''2017-07-19 00:00:00''" }',
                                        p_sort_by => '{"entity_id DESC"}',
                                        p_limit => 5,
                                        p_offset => 0,
                                        p_incl_sub_brands => TRUE,
                                        p_incl_test => FALSE
                                        );
********************************************************************************************************/
DECLARE
    v_filter                VARCHAR;
    v_select                VARCHAR;
    v_where                 VARCHAR:='WHERE ';
    v_sort_by               VARCHAR:='ORDER BY ';
    v_limit                 VARCHAR:=COALESCE('LIMIT '||p_limit::TEXT,'');
    v_offset                VARCHAR:=COALESCE('OFFSET '||p_offset::TEXT,'');
    v_exec_sql              VARCHAR:='';
    v_line                  VARCHAR;
    v_actual_table          VARCHAR:='swmanagement.audits';
    v_archive_table         VARCHAR:='swmanagement_archive.audits';
    v_partiton_key          VARCHAR;
    v_partiton_key_filters  VARCHAR[];
    v_is_in_archive         BOOLEAN:=FALSE;
    v_sub_brands            VARCHAR:='';
    v_new_line              VARCHAR:=chr(10);
BEGIN

     /* Check mandatory params */
    IF p_where_filters IS NULL THEN
        RAISE EXCEPTION 'Filter for WHERE must be defined!';
    END IF;

    -- Get partition key from pathman configuration table
    SELECT expr
    INTO v_partiton_key
    FROM public.pathman_config
    WHERE
        partrel  = v_actual_table::regclass
    AND parttype = 2;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Table % has no valid partitions.', v_actual_table;
    END IF;

    -- Get column list
    SELECT 'SELECT '||string_agg(attname, ', ' ORDER BY attnum)||' FROM '
    INTO v_select
    FROM pg_catalog.pg_attribute
    WHERE  attrelid = v_actual_table::regclass
    AND    attnum > 0
    AND    NOT attisdropped;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No column list for %.', v_actual_table;
    END IF;


    --Build WHERE filter
    FOREACH v_filter IN ARRAY p_where_filters LOOP
        -- If includes sub-brands then need to find all brands for brand_id from p_where_filters
        IF p_incl_sub_brands = TRUE AND v_filter ILIKE '%entity_id%' THEN
                    --RAISE INFO '[%]: v_filter : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_filter;
                    EXECUTE 'WITH RECURSIVE hierarchy AS
                                            (
                                                SELECT entity_id, parent, name, type, 1 AS deep_level, title, key, path, is_test
                                                FROM   (SELECT id as entity_id, parent, name, type, 1 AS deep_level, title, key, path, is_test FROM entities) AS x
                                                WHERE  '||v_filter||'
                                                UNION ALL
                                                SELECT en.id AS entity_id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.key, en.path, en.is_test
                                                FROM   entities en
                                                INNER JOIN hierarchy h ON en.parent = h.entity_id
                                            )
                                            SELECT ''entity_id IN (''|| string_agg(entity_id::varchar, '', '' ORDER BY entity_id) ||'')''
                                            FROM   hierarchy
                                            WHERE  is_test = COALESCE(CASE WHEN '||p_incl_test||' <> FALSE THEN NULL ELSE '||p_incl_test||' END, is_test);' INTO v_sub_brands;
                    --RAISE INFO '[%]: v_sub_brands : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_sub_brands;
                    v_filter:= v_sub_brands;
        END IF;
        v_where:=v_where||CASE WHEN array_position(p_where_filters, v_filter) <> 1 THEN ' AND' ELSE '' END ||v_new_line||v_filter;

        -- Get datetime values from partition key filters, to match them with pathman configuration tables
        IF v_filter ILIKE v_partiton_key||'%' THEN
            v_partiton_key_filters:= array_append(v_partiton_key_filters, (SELECT substring(v_filter from '\''.*?\''' ))::VARCHAR);
        END IF;
    END LOOP;

    --Build ORDER BY
    IF p_sort_by IS NULL OR p_sort_by = '{}'::VARCHAR[] THEN
        v_sort_by:= v_sort_by||v_partiton_key||' DESC';
    ELSE
        FOREACH v_filter IN ARRAY p_sort_by LOOP
            v_sort_by:=v_sort_by||CASE WHEN array_position(p_sort_by, v_filter) <> 1 THEN ' ,' ELSE '' END ||v_new_line||v_filter;
        END LOOP;
    END IF;

    --2019-08-02 Special hack to make Postgres use index (entity_id, ts) when conditions is with IN like:  "WHERE entity_id IN ('2219', '2221', '2270', '2533')"
    IF v_where ILIKE '%entity_id in%' THEN
        v_sort_by:=v_sort_by||' ,'||v_new_line||' entity_id';
    END IF;

    -- Check if exists partition key filters
    IF v_partiton_key_filters IS NOT NULL THEN
        --RAISE INFO '[%]: Partition key exists in WHERE ', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS');

        -- Check if partition filters points to archive table
        FOREACH v_filter IN ARRAY v_partiton_key_filters LOOP
            SELECT TRUE AS in_interval
                INTO v_is_in_archive
            FROM
            (
                SELECT
                    (SELECT MIN(range_min::timestamp)
                    FROM public.pathman_partition_list
                    WHERE parent IN (v_archive_table::regclass)
                    ) AS range_min,
                    (SELECT MAX(range_max::timestamp)
                    FROM public.pathman_partition_list
                    WHERE parent IN (v_archive_table::regclass)
                    ) AS range_max
            ) AS q
            WHERE v_filter::timestamp BETWEEN range_min AND range_max;
        EXIT WHEN v_is_in_archive ;
        END LOOP;
        v_is_in_archive:=COALESCE(v_is_in_archive,FALSE);

        -- Build EXEC string based on which tables need to use
        IF v_is_in_archive = TRUE THEN 
            v_exec_sql:=v_select||' (('||v_new_line||
            v_select||v_actual_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
            ||') UNION ALL ('||
            v_select||v_archive_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
            ||')) AS q '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
        ELSE 
            v_exec_sql:=v_select||v_actual_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
        END IF;
    ELSE
        -- If partition key doesnt exist in WHERE use only actual table
        v_exec_sql:=v_select||v_actual_table||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
    END IF;

    --RAISE INFO '[%]: v_exec_sql: %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_new_line||v_exec_sql;

    RETURN QUERY
        EXECUTE v_exec_sql
        ;
END;
$function$
;

RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_spins_history(VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN);
--rollback DROP FUNCTION IF EXISTS fnc_bo_audits(VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN);
--rollback ALTER FUNCTION fnc_bo_spins_history_before_4_43_0 (VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN) RENAME TO fnc_bo_spins_history;
--rollback ALTER FUNCTION fnc_bo_audits_before_4_43_0 (VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN) RENAME TO fnc_bo_audits;
--rollback RESET search_path;


--changeset stepanov.alekey:2020-08-12-SWS-20577-permission-for-storing-terminal-token
--comment Create add/remove API for storing terminal token in blacklist
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'terminal:token:add' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'terminal:token:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["terminal:token:add", "terminal:token:delete"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'terminal:token:delete' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'terminal:token:add' WHERE id = 1;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2020-08-05_DEVOPS-9291_move_old_data_to_arch_init endDelimiter:# stripComments:false rollbackEndDelimiter:# 
--comment Archive DB emulation on CDs(Dev/QA) environments. Created views, granted rights, scheduled procs
DO $$
DECLARE   
    v_fs_name   TEXT;
    a_roles     TEXT[]      := '{usr_bi_team, usr_dev_team, usr_qa_team}';
    a_schemas   TEXT[]      := '{swmanagement_archive_ro, swjackpot_archive_ro}';
    v_sql       TEXT;
BEGIN
    SET client_min_messages TO INFO;

    -- Check for the foreign server
    BEGIN
        SELECT srvname FROM pg_foreign_server WHERE srvname ~* '^sw_hcl.+ro$' INTO STRICT v_fs_name;
    EXCEPTION
        WHEN no_data_found THEN
            RAISE INFO '[%] Foreign server not found. Will do emulation', clock_timestamp();
        WHEN too_many_rows THEN
            RAISE EXCEPTION '[%] Too many foreign servers. Can''t choose one!', clock_timestamp();
    END;
   
    IF (v_fs_name IS NULL) THEN -- Emulation (CD envs)
    
        -- Renaming tables
        SELECT string_agg(format(
                    'ALTER TABLE %s.%s RENAME TO %s_bak;', t.schemaname, t.tablename, t.tablename)
                    , E'\n' ORDER BY t.schemaname, t.tablename)
            INTO v_sql
        FROM pg_catalog.pg_tables t        
        WHERE t.schemaname = ANY(a_schemas) AND t.tablename NOT ILIKE '%_bak'
        ;        
        RAISE INFO E'[%] Renaming tables: \n%', clock_timestamp(), v_sql;
        EXECUTE (COALESCE(v_sql, ''));
        
        -- Creating views
        SELECT string_agg(format(
                        'CREATE OR REPLACE VIEW %s.%s AS SELECT * FROM %s.%s;'
                        , t.schemaname, LEFT(t.tablename, -4), LEFT(t.schemaname, -3), LEFT(t.tablename, -4))
                    , E'\n' ORDER BY t.schemaname, t.tablename)
            INTO v_sql
        FROM pg_catalog.pg_tables t        
        WHERE t.schemaname = ANY(a_schemas)
            AND NOT EXISTS (SELECT 1 FROM pg_catalog.pg_views v 
                        WHERE v.schemaname = t.schemaname AND v.viewname = LEFT(t.tablename, -4) ) 
        ;        
        RAISE INFO E'[%] Creating views: \n%', clock_timestamp(), v_sql;
        EXECUTE (COALESCE(v_sql, ''));    
    
        -- Permissions to schemas
        SELECT string_agg(format(
                        'GRANT USAGE ON SCHEMA %s TO %s;', s.nspname, r.rolname)
                    , E'\n' ORDER BY s.nspname, r.rolname)
            INTO v_sql
        FROM pg_catalog.pg_namespace s
        CROSS JOIN pg_catalog.pg_roles r
        WHERE s.nspname = ANY(a_schemas) 
            AND r.rolname = ANY(a_roles)
        ;        
        RAISE INFO E'[%] Permissions to schemas: \n%', clock_timestamp(), v_sql;
        EXECUTE (COALESCE(v_sql, ''));
        
        -- Permissions to views
        SELECT string_agg(format(
                        'GRANT SELECT ON TABLE %s.%s TO %s;', v.schemaname, v.viewname, r.rolname)
                    , E'\n' ORDER BY v.schemaname, v.viewname, r.rolname)
            INTO v_sql
        FROM pg_catalog.pg_views v
        CROSS JOIN pg_catalog.pg_roles r
        WHERE v.schemaname = ANY(a_schemas)
            AND r.rolname = ANY(a_roles)
        ;        
        RAISE INFO E'[%] Permissions to views: \n%', clock_timestamp(), v_sql;
        EXECUTE (COALESCE(v_sql, ''));
        
        -- Schedule
        IF NOT EXISTS (SELECT 1 FROM cron.job 
                        WHERE  command ILIKE '%fnc_move_data_to_arch%'
                            OR command ILIKE '%fnc_remove_empty_partitions%' )
        THEN
            PERFORM cron.schedule('0 3 * * *'
                , '/* Cleanup. Moving old data to archive */ SET SESSION search_path TO swsystem; SELECT fnc_move_data_to_arch();'
            );
            PERFORM cron.schedule('30 3 * * *'
                , '/* Cleanup. Removing old empty portitions */ SET SESSION search_path TO swsystem; SELECT fnc_remove_empty_partitions();'
            );
            RAISE INFO E'[%] Schedule has been set', clock_timestamp();
        END IF;
    END IF;

    RESET client_min_messages;
END $$;

--rollback DO $$
--rollback DECLARE   
--rollback     v_fs_name   TEXT;
--rollback     a_roles     TEXT[]      := '{usr_bi_team, usr_dev_team, usr_qa_team}';
--rollback     a_schemas   TEXT[]      := '{swmanagement_archive_ro, swjackpot_archive_ro}';
--rollback     v_sql       TEXT;
--rollback BEGIN
--rollback     SET client_min_messages TO INFO;
--rollback 
--rollback     -- Check for the foreign server
--rollback     BEGIN
--rollback         SELECT srvname FROM pg_foreign_server WHERE srvname ~* '^sw_hcl.+ro$' INTO STRICT v_fs_name;
--rollback     EXCEPTION
--rollback         WHEN no_data_found THEN
--rollback             RAISE INFO '[%] Foreign server not found. Will do emulation', clock_timestamp();
--rollback         WHEN too_many_rows THEN
--rollback             RAISE EXCEPTION '[%] Too many foreign servers. Can''t choose one!', clock_timestamp();
--rollback     END;
--rollback    
--rollback     IF (v_fs_name IS NULL) THEN -- Emulation (CD envs)
--rollback         -- Removing views
--rollback         SELECT string_agg(format(
--rollback                     'DROP VIEW %s.%s;', t.schemaname, t.viewname)
--rollback                 , E'\n' ORDER BY t.schemaname, t.viewname)
--rollback             INTO v_sql
--rollback         FROM pg_catalog.pg_views t        
--rollback         WHERE t.schemaname = ANY(a_schemas)
--rollback         ;        
--rollback         RAISE INFO E'[%] Removing views: \n%', clock_timestamp(), v_sql;
--rollback         EXECUTE (COALESCE(v_sql, ''));
--rollback         
--rollback         -- Renaming back tables
--rollback         SELECT string_agg(format(
--rollback                     'ALTER TABLE %s.%s RENAME TO %s;', t.schemaname, t.tablename, LEFT(t.tablename, -4))
--rollback                 , E'\n' ORDER BY t.schemaname, t.tablename)
--rollback             INTO v_sql
--rollback         FROM pg_catalog.pg_tables t        
--rollback         WHERE t.schemaname = ANY(a_schemas) 
--rollback             AND t.tablename ILIKE '%_bak'
--rollback         ;        
--rollback         RAISE INFO E'[%] Renaming back tables: \n%', clock_timestamp(), v_sql;
--rollback         EXECUTE (COALESCE(v_sql, ''));
--rollback         
--rollback         -- Clearing cleanup schedule
--rollback         SELECT string_agg(format('SELECT cron.unschedule(%s);', jobid), E'\n') 
--rollback             INTO v_sql
--rollback         FROM cron.job
--rollback         WHERE  command ILIKE '%fnc_move_data_to_arch%'
--rollback             OR command ILIKE '%fnc_remove_empty_partitions%'
--rollback         ;
--rollback         RAISE INFO E'[%] Clearing schedule: \n%', clock_timestamp(), v_sql;
--rollback         EXECUTE (COALESCE(v_sql, ''));
--rollback     END IF;
--rollback     
--rollback     RESET client_min_messages;
--rollback END $$;


--changeset aleksey.ignatenko:2020-08-05_DEVOPS-9291_move_old_data_to_arch_tables
--comment Archive DB emulation on CDs(Dev/QA) environments. Created tables
SET search_path = swsystem;

CREATE TABLE IF NOT EXISTS partition_cleanup_config
(
    tabrel      regclass    NOT NULL,
    dt_col      varchar(50) NOT NULL,
    lifespan    INTERVAL    NOT NULL,
    arch_tabrel regclass    NULL,
    is_active   bool        NOT NULL    DEFAULT true,
    inserted_at timestamp   NOT NULL    DEFAULT (now() AT TIME ZONE 'UTC'),
    updated_at  timestamp   NULL,
    PRIMARY KEY (tabrel),
    CHECK (arch_tabrel IS NULL OR arch_tabrel <> tabrel)
);
COMMENT ON TABLE partition_cleanup_config IS 'Settings of big tables to move old data to archive and remove empty partitions ';
COMMENT ON COLUMN partition_cleanup_config.tabrel IS 'Table classreg id';
COMMENT ON COLUMN partition_cleanup_config.dt_col IS 'Date-time field of the table determining actuality of data';
COMMENT ON COLUMN partition_cleanup_config.lifespan IS 'Period during that data in the table is considered as actual';
COMMENT ON COLUMN partition_cleanup_config.arch_tabrel IS 'Archive table classreg id. By default: the same table name in proper archive schema';
COMMENT ON COLUMN partition_cleanup_config.is_active IS 'Identify the setting is enable';
COMMENT ON COLUMN partition_cleanup_config.inserted_at IS 'Time when the setting was added';
COMMENT ON COLUMN partition_cleanup_config.updated_at IS 'Time when the setting was changed';

INSERT INTO partition_cleanup_config (tabrel, dt_col, lifespan) VALUES 
 ('swjackpot.jp_contribution_log'::regclass,'trx_date','1 MONTH')
,('swjackpot.jp_wallet_operation_log'::regclass,'ts','1 MONTH')
,('swmanagement.audits'::regclass,'ts','1 MONTH')
,('swmanagement.sessions_history'::regclass,'started_at','1 MONTH')
,('swmanagement.wallet_operation_log'::regclass,'ts','1 MONTH')
,('swmanagement.wallet_win_bet'::regclass,'payment_date','1 MONTH')
,('swmanagement.spins_history'::regclass,'ts','1 MONTH')
,('swmanagement.wallet_entity_payment_log'::regclass,'ts','1 MONTH')
,('swmanagement.rounds_finished'::regclass,'finished_at','1 MONTH')
,('swmanagement.rounds_history'::regclass,'inserted_at','1 MONTH')
ON CONFLICT DO NOTHING
;

CREATE TABLE IF NOT EXISTS partition_cleanup_history
(
    log_ts          timestamp   NOT NULL    DEFAULT (clock_timestamp() AT TIME ZONE 'UTC'),
    partition_name  text        NOT NULL,
    schema_name     text        NOT NULL,
    table_name      text        NOT NULL,
    dt_col_name     text        NOT NULL,
    range_min       text, 
    range_max       text
);
COMMENT ON TABLE partition_cleanup_history IS 'Old pathman partitions removed by the function fnc_remove_empty_partitions after moving their data to archive';
COMMENT ON COLUMN partition_cleanup_history.log_ts IS 'Time point';
COMMENT ON COLUMN partition_cleanup_history.partition_name IS 'Partition';
COMMENT ON COLUMN partition_cleanup_history.schema_name IS 'Schema';
COMMENT ON COLUMN partition_cleanup_history.table_name IS 'Parent table';
COMMENT ON COLUMN partition_cleanup_history.dt_col_name IS 'Partitioning field of date/time type (Partitioning key)';
COMMENT ON COLUMN partition_cleanup_history.range_min IS 'Low limit of partitioning key';
COMMENT ON COLUMN partition_cleanup_history.range_max IS 'High limit of partitioning key';

CREATE TABLE IF NOT EXISTS partition_cleanup_log
(
    log_ts          timestamp   NOT NULL    DEFAULT (clock_timestamp() AT TIME ZONE 'UTC'),
    tabrel          regclass    NOT NULL,   
    schema_name     text        NOT NULL,
    table_name      text        NOT NULL,
    cleaned_till    timestamp   NOT NULL,
    deleted_rows    int         NOT NULL,
    is_error        bool        NOT NULL    DEFAULT FALSE,
    log_text        text        NOT NULL    DEFAULT 'Done'
);
COMMENT ON TABLE partition_cleanup_log IS 'Cleanups tables and removing partitions log';
COMMENT ON COLUMN partition_cleanup_log.log_ts IS 'Time point';
COMMENT ON COLUMN partition_cleanup_log.tabrel IS 'Table classreg ID';
COMMENT ON COLUMN partition_cleanup_log.schema_name IS 'Schema';
COMMENT ON COLUMN partition_cleanup_log.table_name IS 'Table/Partition';
COMMENT ON COLUMN partition_cleanup_log.cleaned_till IS 'Dead line of the cleanup';
COMMENT ON COLUMN partition_cleanup_log.deleted_rows IS 'Number of rows removed from the table';
COMMENT ON COLUMN partition_cleanup_log.is_error IS 'Indicates whether the cleanup was failed';
COMMENT ON COLUMN partition_cleanup_log.log_text IS 'Result message of the cleanup';

RESET search_path;

--rollback SET search_path = swsystem;
--rollback DROP TABLE partition_cleanup_config;
--rollback DROP TABLE partition_cleanup_history;
--rollback DROP TABLE partition_cleanup_log;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2020-08-05_DEVOPS-9291_move_old_data_to_arch_procs endDelimiter:# stripComments:false
--comment Archive DB emulation on CDs(Dev/QA) environments. Stored procedures
SET search_path = swsystem;

CREATE OR REPLACE FUNCTION fnc_log_partition_cleanup 
(
    p_tabrel        regclass,   
    p_cleaned_till  timestamp,
    p_check_only    bool,
    p_deleted_rows  int         DEFAULT 0,
    p_is_error      bool        DEFAULT FALSE,
    p_log_text      text        DEFAULT ''
)
 RETURNS timestamp 
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name:   fnc_log_partition_cleanup
    Purpose    : Logging cleanup process point
    History    :
        1.0.0
            Date    : June 15, 2020
            Authors : Ales
            Notes   : Release (DEVOPS-9291)
             
    Sample run:
        SELECT swsystem.fnc_log_partition_cleanup('swsystem.partition_cleanup_log'::regclass, timezone('UTC', now()), true, 0, false, 'Test message');
        SELECT swsystem.fnc_log_partition_cleanup('partition_cleanup_log'::regclass, timezone('UTC', now()), false, 0, true, 'Test erroe');
********************************************************************************************************/
DECLARE    
    v_log_ts        timestamp;
    v_schema_name   text := split_part (p_tabrel::text, '.', 1);
    v_table_name    text := split_part (p_tabrel::text, '.', 2);
    i_log_text      text := CASE WHEN p_check_only THEN 'Test: '||p_log_text ELSE p_log_text END;
BEGIN
    
    /* get schema if empty - in case one name table */
    IF (v_table_name = '') THEN
        v_table_name := v_schema_name;
        
        SELECT schemaname
        INTO v_schema_name
        FROM pg_tables
        WHERE tablename = v_table_name;
    END IF;
    
    /* log */
    INSERT INTO swsystem.partition_cleanup_log (tabrel, schema_name, table_name, cleaned_till, deleted_rows, 
            is_error, log_text)
    VALUES (p_tabrel, v_schema_name, v_table_name, p_cleaned_till, p_deleted_rows,
            p_is_error, i_log_text)
    RETURNING log_ts 
    INTO v_log_ts;
    
    /* show if debug */
    IF (p_check_only) THEN
        RAISE INFO '% %', v_log_ts::text, i_log_text;
    END IF;
        
    RETURN v_log_ts;
END $function$
;

CREATE OR REPLACE FUNCTION fnc_move_data_to_arch 
(
    p_lifespan      text    DEFAULT NULL, 
    p_check_only    bool    DEFAULT FALSE 
)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name:   fnc_move_data_to_arch
    Purpose    : Copy rows older than certain date (1 month ago usually, from config for each table, or from <p_lifespan> - will be used the greatest interval) 
                from a set of big tables listed and configured in the table "swsystem.cleanups"
                to proper archive tables usually placed in schemas "sw*_archive" 
                and clear the original tables from these old rows
    History    :
        1.0.0
            Date    : June 09, 2020
            Authors : Ales
            Notes   : Release (DEVOPS-9291)
             
    Sample run:
        SELECT swsystem.fnc_move_data_to_arch(null, true);
        SELECT jsonb_pretty( swsystem.fnc_move_data_to_arch(null) );
********************************************************************************************************/
DECLARE
    i_lifespan  interval    := p_lifespan::interval;
    v_res       jsonb       := '{}'::jsonb;
    v_today     timestamp   := date_trunc('DAY', now() AT TIME ZONE 'UTC');
    v_arch_suff varchar(30) := '_archive';
    c           record;  
    v_dead_line timestamp;
    v_num       int;
    v_sql       text;
    v_count     int         := 0;
    v_total     int         := 0;
BEGIN
    
    FOR c IN (
                WITH arch_tables AS (
                    SELECT  split_part(tabrel::text, '.', 2) AS live_table,
                            split_part(tabrel::text, '.', 1) AS live_schema,
                            split_part(arch_tabrel::text, '.', 2) AS arch_table,
                            split_part(arch_tabrel::text, '.', 1) AS arch_schema,
                            tabrel, dt_col, lifespan                                                
                    FROM swsystem.partition_cleanup_config                
                    WHERE is_active 
                )              
                , cols AS (
                    SELECT table_schema, table_name, 
                            array_agg(column_name::varchar ORDER BY ordinal_position) AS arr
                    FROM information_schema.columns 
                    GROUP BY table_schema, table_name
                )
                SELECT  v.live_table AS table_name, v.live_schema, tabrel,                          
                        COALESCE(v.dt_col, 'inserted_at') AS dt_col, lifespan,
                        a.table_schema AS arch_schema,
                        a.table_name AS arch_table, a.arr AS arch_cols, 
                        l.table_name AS live_table, l.arr AS live_cols
                FROM arch_tables v                    
                LEFT JOIN cols l 
                    ON l.table_schema = v.live_schema
                        AND l.table_name = v.live_table
                LEFT JOIN cols a 
                    ON a.table_schema = COALESCE(v.arch_schema, v.live_schema || v_arch_suff) 
                        AND a.table_name = COALESCE(v.arch_table, v.live_table)                    
            ) LOOP
        
        /* init */
        v_dead_line := v_today - greatest( coalesce(i_lifespan, c.lifespan), c.lifespan );
    
        /* check */
        IF (c.arch_table IS NULL) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Not found the table %s in the arch schema %s', c.table_name, c.arch_schema) );
            CONTINUE;
        END IF;
        IF (c.live_table IS NULL) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Not found the table %s in the live schema %s', c.table_name, c.live_schema) );
            CONTINUE;
        END IF;
        IF NOT (c.arch_cols @> c.live_cols AND c.arch_cols <@ c.live_cols) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Mismatched structures of table %s in live and arch schemas (%s and %s)', c.table_name, c.live_schema, c.arch_schema) );
            CONTINUE;
        END IF;
        IF (array_position(c.arch_cols, c.dt_col) IS NULL) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Not found column %s in the table %s in the live schema %s', c.dt_col, c.table_name, c.live_schema) );
            CONTINUE;
        END IF;
        
        /* dynamic sql */
        v_sql := CASE WHEN p_check_only 
                THEN format(
                    'SELECT count(*) FROM %s.%s WHERE %s < $1'
                    , c.live_schema, c.live_table, c.dt_col
                )
                ELSE format(
                    'WITH cleared AS (
                        DELETE FROM %s.%s WHERE %s < $1 RETURNING * 
                    ) 
                    , copied AS (
                        INSERT INTO %s.%s 
                        SELECT %s FROM cleared 
                        RETURNING %s
                    )
                    SELECT count(*) FROM copied'
                    , c.live_schema, c.live_table, c.dt_col
                    , c.arch_schema, c.arch_table
                    , array_to_string(c.arch_cols, ','), c.dt_col
                ) 
            END;
        --RAISE INFO '%', v_sql;
        
        /* run */ 
        EXECUTE v_sql USING v_dead_line INTO v_num;
        
        /* log */
        PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, v_num, FALSE, 
                CASE v_num WHEN 0 
                    THEN format('Nothing to move from %s.%s to archive %s.%s', c.live_schema, c.live_table, c.arch_schema, c.arch_table )
                    ELSE format('Moved %s rows from %s.%s to archive %s.%s', v_num, c.live_schema, c.live_table, c.arch_schema, c.arch_table ) 
                END 
            );
        
        v_count := v_count + sign(v_num);
        v_total := v_total + 1;
    
        /* add to result */        
        v_res := v_res || jsonb_build_object (c.tabrel::text, v_num);
    
    END LOOP;

    RETURN jsonb_build_object('status', 'SUCCESS') 
        || jsonb_build_object('cleaned', format('%s/%s', v_count, v_total)) 
        || jsonb_build_object('tables', v_res);

END $function$
;

CREATE OR REPLACE FUNCTION fnc_remove_empty_partitions
(
    p_lifespan      text    DEFAULT NULL,
    p_check_only    bool    DEFAULT FALSE 
)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name:   fnc_remove_empty_partitions
    Purpose    : Check pathman partitions older than certain date (1 month ago usually, from config or <p_lifespan> - the oldest date) on row existance
                for a set of big tables listed and configured in the table "swsystem.cleanups"                
                and remove empty ones of them
    History    :
        1.0.0
            Date    : June 12, 2020
            Authors : Ales
            Notes   : Release (DEVOPS-9291)
             
    Sample run:
        SELECT swsystem.fnc_remove_empty_partitions(null, true);
        SELECT swsystem.fnc_remove_empty_partitions('3 MONTH', true);
        SELECT jsonb_pretty( swsystem.fnc_remove_empty_partitions() );
********************************************************************************************************/
DECLARE
    i_lifespan  interval    := p_lifespan::interval;
    v_res       jsonb       := '{}'::jsonb;   
    v_today     timestamp   := date_trunc('DAY', now() AT TIME ZONE 'UTC');
    c           record;  
    v_num       int;
    v_sql       text;
    v_part_name text;
    v_count     int         := 0;
    v_total     int         := 0;
BEGIN
    
    FOR c IN (
                WITH tabs AS (
                    SELECT  tabrel, dt_col, lifespan,
                            (v_today - greatest(coalesce(i_lifespan, lifespan), lifespan)) AS dead_line 
                    FROM swsystem.partition_cleanup_config                
                    WHERE is_active 
                )              
                , parts AS (
                    SELECT parent AS tabrel, partition AS partrel, 
                        expr, range_min, range_max
                    FROM public.pathman_partition_list p                    
                    WHERE parttype = 2 
                )
                SELECT p.tabrel, p.partrel, v.dead_line,
                        p.expr AS dt_col, p.range_min, p.range_max
                FROM tabs v
                INNER JOIN parts p 
                    ON p.tabrel = v.tabrel 
                        AND p.range_max < (v.dead_line)::text
                        AND p.expr = v.dt_col
            ) LOOP
        
        /* dynamic sql */
        v_sql := format(
                    'SELECT count(*) FROM %s'
                    , c.partrel::text, c.dt_col
                );            
        --RAISE INFO '%', v_sql;
        
        /* run */ 
        EXECUTE v_sql INTO v_num;
        
        IF (v_num = 0 AND NOT p_check_only) THEN
            /* log */
            PERFORM swsystem.fnc_log_partition_cleanup( c.partrel, c.dead_line, p_check_only, v_num, FALSE, 
                    format('Partition %s of table %s is empty and being removed', c.partrel::text, c.tabrel::text) );                       
            
            /* remove partition */
            SELECT public.drop_range_partition(c.partrel, TRUE) --detach_range_partition(c.partrel);
                INTO v_part_name;
            
            /* save to history */
            INSERT INTO swsystem.partition_cleanup_history (partition_name, schema_name, table_name, dt_col_name, range_min, range_max)
            VALUES (v_part_name, split_part(c.tabrel::text, '.', 1), split_part(c.tabrel::text, '.', 2), c.dt_col, c.range_min, c.range_max);
        
            v_count := v_count + 1;
        ELSE
            /* log */
            PERFORM swsystem.fnc_log_partition_cleanup( c.partrel, c.dead_line, p_check_only, v_num, TRUE, 
                    format('Partition %s of table %s has %s rows', c.partrel::text, c.tabrel::text, v_num) );
        
            v_part_name := c.partrel::text;
        END IF;
    
        v_total := v_total + 1;
        
        /* add to result */        
        v_res := v_res || jsonb_build_object( v_part_name, CASE v_num WHEN 0 THEN 'removed' ELSE v_num::text||' rows' END );
    
    END LOOP;

    RETURN jsonb_build_object('status', 'SUCCESS') 
        || jsonb_build_object('removed', format('%s/%s', v_count, v_total))
        || jsonb_build_object('partitions', v_res);

END $function$
;

RESET search_path;

--rollback SET search_path = swsystem;
--rollback DROP FUNCTION fnc_log_partition_cleanup;
--rollback DROP FUNCTION fnc_move_data_to_arch;
--rollback DROP FUNCTION fnc_remove_empty_partitions;
--rollback RESET search_path;



--changeset valdis.akmens:2020-05-20-SWDB-106-improve-rounds-related-monitoring endDelimiter:# stripComments:false
--comment New aggregated table and related functions to aggregate rounds
SET search_path TO swsystem;

CREATE SCHEMA IF NOT EXISTS monitoring AUTHORIZATION swsystem;
RESET search_path;

SET search_path TO monitoring;


CREATE TABLE IF NOT EXISTS aggr_rounds (
	ts_period       TIMESTAMP NOT NULL,
    brand_id        INTEGER NOT NULL,
	game_code       VARCHAR(255) NOT NULL,
	player_code     VARCHAR(255) NOT NULL,
	currency        CHAR(3) NOT NULL,
	is_test_round   BOOLEAN NOT NULL,
    is_test_brand   BOOLEAN NOT NULL,
	device_id       VARCHAR(255) NOT NULL,
	rounds_cnt      INTEGER NULL,
	rounds_fin_cnt  INTEGER NULL,
	events_cnt      BIGINT NULL,
	sessions_cnt    INTEGER NULL,
	max_unload_lag  interval NULL,
	bet_sum_base    NUMERIC NULL,
	win_sum_base    NUMERIC NULL,
	jp_contrib_sum_base NUMERIC NULL,
	jp_win_sum_base NUMERIC NULL,
	bet_sum         NUMERIC NULL,
	win_sum         NUMERIC NULL,
	jp_contrib_sum  NUMERIC NULL,
	jp_win_sum      NUMERIC NULL,
	max_finished_at TIMESTAMP NULL
);
CREATE INDEX IF NOT EXISTS idx_aggr_rounds ON aggr_rounds USING btree (ts_period, brand_id, game_code, player_code, currency, is_test_round, device_id);
CREATE INDEX IF NOT EXISTS idx_aggr_rounds_max_finished_at ON aggr_rounds USING btree (max_finished_at);

COMMENT ON TABLE aggr_rounds                      IS 'Table with spins_history aggregations. For monitoring usage only!';
COMMENT ON COLUMN aggr_rounds.ts_period           IS 'Aggregation time period.';
COMMENT ON COLUMN aggr_rounds.brand_id            IS 'Brand ID from "entities" table.';
COMMENT ON COLUMN aggr_rounds.game_code           IS 'Game code for filtering by games.';
COMMENT ON COLUMN aggr_rounds.player_code         IS 'Player code.';
COMMENT ON COLUMN aggr_rounds.currency            IS 'Currency code.';
COMMENT ON COLUMN aggr_rounds.is_test_round       IS 'Is it test round';
COMMENT ON COLUMN aggr_rounds.is_test_brand       IS 'Is it test brand';
COMMENT ON COLUMN aggr_rounds.device_id           IS 'Device from user was playing.';
COMMENT ON COLUMN aggr_rounds.rounds_cnt          IS 'Rounds count.';
COMMENT ON COLUMN aggr_rounds.rounds_fin_cnt      IS 'Finished rounds count.';
COMMENT ON COLUMN aggr_rounds.events_cnt          IS 'Spins count.';
COMMENT ON COLUMN aggr_rounds.sessions_cnt        IS 'Sessions count.';
COMMENT ON COLUMN aggr_rounds.max_unload_lag      IS 'Lag for unloading data to DB.';
COMMENT ON COLUMN aggr_rounds.bet_sum_base        IS 'Bets in base currency (USD).';
COMMENT ON COLUMN aggr_rounds.win_sum_base        IS 'Wins in base currency (USD).';
COMMENT ON COLUMN aggr_rounds.jp_contrib_sum_base IS 'Jackpot contribution in base currency (USD).';
COMMENT ON COLUMN aggr_rounds.jp_win_sum_base     IS 'Jackpot wins in base currency (USD).';
COMMENT ON COLUMN aggr_rounds.bet_sum             IS 'Bets in player currency.';
COMMENT ON COLUMN aggr_rounds.win_sum             IS 'Wins in player currency.';
COMMENT ON COLUMN aggr_rounds.jp_contrib_sum      IS 'Jackpot contribution in player currency.';
COMMENT ON COLUMN aggr_rounds.jp_win_sum          IS 'Jackpot wins in player currency.';
COMMENT ON COLUMN aggr_rounds.max_finished_at     IS 'Max timestamp in aggregation period.';


CREATE TABLE IF NOT EXISTS aggr_rounds_external (
	ts_period               TIMESTAMP NOT NULL,
    player_code             VARCHAR(255) NOT NULL,
    is_test_round           BOOLEAN NULL,
    game_provider           VARCHAR NOT NULL,
    rounds_cnt              INTEGER NULL
);
CREATE INDEX IF NOT EXISTS idx_aggr_rounds_external ON aggr_rounds_external USING btree (ts_period, game_provider, player_code);
COMMENT ON TABLE aggr_rounds_external                        IS 'Table with swadaptergos.ext_bet_win_history aggregations. For monitoring usage only!';
COMMENT ON COLUMN aggr_rounds_external.ts_period             IS 'Aggregation time period.';
COMMENT ON COLUMN aggr_rounds_external.game_provider         IS 'Game provider from "ext_bet_win_history.game_provider_code" ';
COMMENT ON COLUMN aggr_rounds_external.is_test_round         IS 'Is it test round';
COMMENT ON COLUMN aggr_rounds_external.player_code           IS 'Player code';
COMMENT ON COLUMN aggr_rounds_external.rounds_cnt            IS 'Rounds count';


CREATE OR REPLACE FUNCTION fnc_aggr_rounds()
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
*******************************************************************************
    Object Name:   fnc_aggr_rounds
    Purpose    :   Aggregate aggr_rounds table. Table is used for monitoring tools.
    History    :
        1.0.0
            Date    :  May 20, 2020
            Authors : Valdis Akmens
            Notes   : Release (SWDB-106)

    Sample run:
      SELECT * FROM monitoring.fnc_aggr_rounds();
*******************************************************************************
*/
DECLARE
    v_lock_id               INTEGER := 'monitoring.aggr_rounds'::regclass::integer;
    v_max_finished_at       TIMESTAMP;
    v_min_finished_at       TIMESTAMP;
    v_counter               BIGINT;
    v_off_users             VARCHAR[]:='{"kafka_offloader", "redis_game_offloader"}';
    v_lower_limit           TIMESTAMP;
    v_upper_limit           TIMESTAMP;
    v_huge_interval         INTERVAL:= '10 minutes'::INTERVAL;
    v_partiton_prune        TIMESTAMP;
    v_lower_limit_prev      TIMESTAMP;
    v_upper_limit_prev      TIMESTAMP;
    v_exec_sql              VARCHAR;
BEGIN

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job started'; RETURN NEXT;

    /* Check if MDB */
    IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
    END IF;

    /* Check if another job is running*/
    IF NOT (SELECT pg_try_advisory_xact_lock(v_lock_id)) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Another function is running. Skip'; RETURN NEXT;
        RETURN;
    END IF;

    v_lower_limit:=(SELECT COALESCE(MAX(max_finished_at),date_trunc('MINUTE',now())) FROM monitoring.aggr_rounds);
    v_upper_limit:=(SELECT date_trunc('MINUTE', ts_upper_limit) + FLOOR(date_part('SECOND', ts_upper_limit))::INTEGER / 20 * interval '20 sec' AS ts_upper_20sec_limit
                    FROM  (
                                SELECT LEAST((SELECT MIN(xact_start)
                                                FROM pg_stat_activity sa
                                                WHERE state <> 'idle'::text
                                                AND usename = ANY(v_off_users)
                                            ), Now() )::TIMESTAMP AS ts_upper_limit
                            ) AS x);
    v_partiton_prune:=(SELECT date_trunc('MINUTE',now()) - v_huge_interval);

        /* Automatically prevent too huge intervals */
    IF (v_upper_limit - v_lower_limit) > v_huge_interval THEN
        v_lower_limit := v_upper_limit - v_huge_interval;
    END IF;

    v_lower_limit_prev:= v_lower_limit - '40 SECONDS'::INTERVAL;
    v_upper_limit_prev:= v_lower_limit;
    --RAISE INFO 'v_lower_limit = % ; v_upper_limit = % ; v_lower_limit_prev= % ; v_upper_limit_prev = %', v_lower_limit,v_upper_limit,v_lower_limit_prev,v_upper_limit_prev;
    v_exec_sql:= '
    WITH cte_xrates AS (
            SELECT r.currency_code, r.rate
            FROM swmanagement.currency_rates AS r
            JOIN (
                    SELECT currency_code, MAX(rate_date) AS rate_date
                    FROM swmanagement.currency_rates
                    WHERE rate_date <= CURRENT_DATE
                    GROUP BY currency_code
                )
                    AS m ON r.currency_code = m.currency_code AND r.rate_date = m.rate_date
            ORDER BY r.currency_code, r.rate_date DESC
        ), cte_prev AS (
            SELECT DISTINCT x.round_id
            FROM swmanagement.spins_history AS x
            WHERE
                x.ts > '''||v_lower_limit_prev||'''
            AND x.ts < '''||v_upper_limit_prev||'''
        ),
    cte_insert AS (
        INSERT INTO monitoring.aggr_rounds (ts_period, brand_id, game_code, player_code, currency, is_test_round, is_test_brand, device_id, rounds_cnt,rounds_fin_cnt, events_cnt, sessions_cnt, max_unload_lag, bet_sum_base, win_sum_base, jp_contrib_sum_base, jp_win_sum_base, bet_sum, win_sum, jp_contrib_sum, jp_win_sum, max_finished_at)
        SELECT date_trunc(''MINUTE'', h.ts) + FLOOR(date_part(''SECOND'', h.ts))::INTEGER / 20 * interval ''20 sec'' AS ts_20sec
            ,h.brand_id                                              AS brand_id
            ,h.game_code                                             AS game_code
            ,h.player_code                                           AS player_code
            ,h.currency                                              AS currency_code
            ,h.test                                                  AS is_test
            ,e.is_test                                               AS is_test_brand
            ,h.device_id                                             AS device
            --
            ,COALESCE(COUNT(DISTINCT
                    CASE WHEN 
                            NOT EXISTS(SELECT pr.round_id
                                        FROM cte_prev AS pr
                                        WHERE pr.round_id = h.round_id
                                        )
                        THEN
                        h.round_id
                        END
                    ),0)                                              AS rounds_cnt
            ,COALESCE(COUNT(DISTINCT
                    CASE WHEN h.round_ended = TRUE THEN h.round_id END
                    ),0)                                              AS rounds_fin_cnt
            ,COALESCE(COUNT(*)::BIGINT,0)                             AS events_cnt
            ,COALESCE(COUNT(DISTINCT
                    CASE WHEN 
                            NOT EXISTS(SELECT pr_2.round_id
                                        FROM cte_prev AS pr_2
                                        WHERE pr_2.round_id = h.round_id                  
                                        )
                        THEN
                        h.session_id
                        END
                    ),0)                                                    AS sessions_cnt
            ,MAX(GREATEST(h.inserted_at, h.ts) - h.ts)                      AS max_unload_lag
            --
            ,COALESCE(SUM(h.bet * xr.rate)::NUMERIC(20,2),0)                 AS bet_sum_base
            ,COALESCE(SUM(h.win * xr.rate)::NUMERIC(20,2),0)                 AS win_sum_base
            ,COALESCE(SUM(h.total_jp_contribution * xr.rate)::NUMERIC(20,2),0)     AS jp_contrib_sum_base
            ,COALESCE(SUM(h.total_jp_win * xr.rate)::NUMERIC(20,2),0)              AS jp_win_sum_base
            --
            ,COALESCE(SUM(h.bet),0)                                         AS bet_sum
            ,COALESCE(SUM(h.win),0)                                         AS win_sum
            ,COALESCE(SUM(h.total_jp_contribution),0)                       AS jp_contrib_sum
            ,COALESCE(SUM(h.total_jp_win),0)                                AS jp_win_sum
            --
            ,MAX(h.inserted_at)                                             AS max_finished_at
        FROM   swmanagement.spins_history AS h
        JOIN   swmanagement.entities        AS e ON e.id = h.brand_id
        JOIN   cte_xrates                   AS xr ON h.currency = xr.currency_code
        WHERE
            h.ts >= '''||v_partiton_prune||'''
        AND COALESCE(h.inserted_at, h.ts) > '''||v_lower_limit||'''
        AND COALESCE(h.inserted_at, h.ts) < '''||v_upper_limit||'''
        GROUP BY
            date_trunc(''MINUTE'', h.ts) + FLOOR(date_part(''SECOND'', h.ts))::INTEGER / 20 * interval ''20 sec''
            ,h.brand_id
            ,h.game_code
            ,h.player_code
            ,h.currency
            ,h.test
            ,e.is_test
            ,h.device_id
        RETURNING *
        )
    SELECT MAX(max_finished_at), MIN(max_finished_at),COUNT(*)
    FROM cte_insert ';

    --RAISE INFO 'v_exec_sql: %', v_exec_sql;

    EXECUTE v_exec_sql
    INTO v_max_finished_at, v_min_finished_at,v_counter;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "aggr_rounds" finished. Rows processed: '||quote_nullable(COALESCE(v_counter, 0))||' Timeframe processed: ('||quote_nullable(COALESCE(v_min_finished_at,v_lower_limit))||'; '||quote_nullable(COALESCE(v_max_finished_at,v_upper_limit))||')'; RETURN NEXT;

    v_exec_sql:='
    WITH cte_insert AS (
        INSERT INTO monitoring.aggr_rounds_external (ts_period, player_code,is_test_round, game_provider, rounds_cnt)
        SELECT date_trunc(''MINUTE'', inserted_at) + FLOOR(date_part(''SECOND'', inserted_at))::INTEGER / 20 * interval ''20 sec'' AS ts_20sec
            ,player_code
            ,is_test
            ,CASE game_provider_code 
                WHEN ''QS'' THEN ''qs'' 
                WHEN ''eyecon'' THEN ''eyecon'' 
                ELSE ''ext_other''  
            END::VARCHAR(20)             AS group_type 
            ,Count(Distinct(round_id))    AS rounds_cnt   
        FROM   swadaptergos.ext_bet_win_history           
        WHERE 
            inserted_at >  '''||v_lower_limit||'''::TIMESTAMP  
        AND  inserted_at <  '''||v_upper_limit||'''::TIMESTAMP  
        GROUP BY date_trunc(''MINUTE'', inserted_at) + FLOOR(date_part(''SECOND'', inserted_at))::INTEGER / 20 * interval ''20sec''                              
            ,player_code
            ,is_test
            ,CASE game_provider_code 
                WHEN ''QS'' THEN ''qs'' 
                WHEN ''eyecon'' THEN ''eyecon'' 
                ELSE ''ext_other''  
            END
        RETURNING *
        )
    SELECT COUNT(*)
    FROM cte_insert ';
    EXECUTE v_exec_sql
    INTO v_counter;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "aggr_rounds_external" finished. Rows processed: '||quote_nullable(COALESCE(v_counter, 0)); RETURN NEXT;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job finished'; RETURN NEXT;
    RETURN;
END;
$function$
;

CREATE OR REPLACE FUNCTION fnc_clean_aggr_rounds()
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
*******************************************************************************
    Object Name:   fnc_clean_aggr_rounds
    Purpose    :   Clean-up aggr_rounds table. 
    History    :
        1.0.0
            Date    :  May 20, 2020
            Authors : Valdis Akmens
            Notes   : Release (SWDB-106)

    Sample run:
      SELECT * FROM swsystem.fnc_clean_aggr_rounds();
*******************************************************************************
*/
DECLARE
    v_counter               BIGINT;
	v_lock_id               BIGINT := (-1)*('monitoring.aggr_rounds'::regclass::integer);
    v_rec_new_candidate     RECORD;
    v_clean_up              INTERVAL:='1 DAY';
BEGIN
    log_time := clock_timestamp(); log_msg := 'INFO: Clean-up job started'; RETURN NEXT;

    /* Check if MDB */
    IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
    END IF;

    /* Check if another job is running*/
    IF NOT (SELECT pg_try_advisory_xact_lock(v_lock_id)) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Another function is running. Skip'; RETURN NEXT;
		RETURN;
    END IF;
    -- PERFORM pg_sleep(10);

    -- Check partitions older than 1 day 
    WITH cte_range_partitions AS (
        SELECT *
        FROM   public.pathman_partition_list
        WHERE  parttype = 2
            AND parent IN ('monitoring.aggr_rounds'::regclass,'monitoring.aggr_rounds_external'::regclass)
    )
    SELECT t.relname::VARCHAR AS table_name
        ,s.nspname::VARCHAR AS schema_name
        ,t.oid AS table_regclass
    INTO v_rec_new_candidate
    FROM  cte_range_partitions     AS p
    JOIN pg_catalog.pg_class       AS t ON p.partition = t.oid
    JOIN pg_catalog.pg_namespace   AS s ON t.relnamespace = s.oid
    WHERE  range_min::DATE < (current_date - v_clean_up)
    ORDER BY p.range_min::TIMESTAMP, p.parent
    LIMIT 1;

    --RAISE INFO 'v_rec_new_candidate = %', v_rec_new_candidate;

    IF v_rec_new_candidate IS NULL THEN 
        log_time := clock_timestamp(); log_msg := 'INFO: No new candidates to process. Exit now.'; RETURN NEXT;
        RETURN;
    END IF;

    EXECUTE FORMAT('SELECT * FROM public.detach_range_partition(%s);', v_rec_new_candidate.table_regclass);
    EXECUTE FORMAT('DROP TABLE %s.%s ;', v_rec_new_candidate.schema_name, v_rec_new_candidate.table_name);
    log_time := clock_timestamp(); log_msg := 'INFO: Partition '||v_rec_new_candidate.schema_name||'.'||v_rec_new_candidate.table_name ||' deatched and droped.'; RETURN NEXT;

    log_time := clock_timestamp(); log_msg := 'INFO: Clean-up job finished'; RETURN NEXT;
    RETURN;
END;
$function$
;

SELECT public.set_init_callback('monitoring.aggr_rounds', 'public.pathman_callback(jsonb)');
SELECT public.create_range_partitions('monitoring.aggr_rounds', 'ts_period', date_trunc('week', now())::date, INTERVAL '1 day', 1, false);


SELECT public.set_init_callback('monitoring.aggr_rounds_external', 'public.pathman_callback(jsonb)');
SELECT public.create_range_partitions('monitoring.aggr_rounds_external', 'ts_period', date_trunc('week', now())::date, INTERVAL '1 day', 1, false);

RESET search_path;

--rollback SET search_path = monitoring;
--rollback DROP TABLE IF EXISTS aggr_rounds;
--rollback DROP TABLE IF EXISTS aggr_rounds_external;
--rollback DROP FUNCTION IF EXISTS fnc_aggr_rounds();
--rollback DROP FUNCTION IF EXISTS fnc_clean_aggr_rounds();
--rollback RESET search_path;


--changeset valdis.akmens:2020-06-05-DEVOPS-9068-optimize-jackpot-games-checking endDelimiter:# stripComments:false
--comment Optimize entity jackopt games settings checks 
SET search_path TO monitoring;

CREATE TABLE IF NOT EXISTS cache_jackpot_settings (
	game_id     INTEGER NULL,
	entity_id   INTEGER NULL,
	tree_pos    INTEGER NULL,
	brand_path  VARCHAR NULL
);
CREATE INDEX IF NOT EXISTS idx_cache_jackpot_settings_game_id_tree_pos ON cache_jackpot_settings USING btree (game_id, tree_pos);
COMMENT ON TABLE cache_jackpot_settings                        IS 'Cache table for jackpot games and entities used to optimize zabbix scripts jackpot games settings check';
COMMENT ON COLUMN cache_jackpot_settings.game_id               IS 'Game id from swmanagement.games.id';
COMMENT ON COLUMN cache_jackpot_settings.entity_id             IS 'Enitity id from swmanagement.entities.id ';
COMMENT ON COLUMN cache_jackpot_settings.tree_pos              IS 'Entity position in brand_path(used to determine subbrands)';
COMMENT ON COLUMN cache_jackpot_settings.brand_path            IS 'Entity path from swmanagement.entities.path';


CREATE OR REPLACE FUNCTION fnc_refresh_cache_jackpot_settings()
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
*******************************************************************************
    Object Name:   fnc_refresh_cache_jackpot_settings
    Purpose    :   Refresh cache_jackpot_settings table. 
    History    :
        1.0.0
            Date    :  Jun 05, 2020
            Authors : Valdis Akmens
            Notes   : Release (DEVOPS-9068)

    Sample run:
      SELECT * FROM monitoring.fnc_refresh_cache_jackpot_settings();
*******************************************************************************
*/
DECLARE

BEGIN
    log_time := clock_timestamp(); log_msg := 'INFO: Refresh cache_jackpot_settings job started'; RETURN NEXT;

    /* Check if MDB */
    IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
    END IF;

    -- Temporary table for JP settings
    CREATE TEMPORARY TABLE tmp_cache_jackpot_settings (
	game_id     INTEGER NULL,
	entity_id   INTEGER NULL,
	tree_pos    INTEGER NULL,
	brand_path  VARCHAR NULL
    ) ON COMMIT DROP;

    -- Prepare data in temp table
    WITH
    cte_jp_games AS (
    SELECT g.id AS game_id, jsonb_array_elements_text(g.features -> 'jackpotTypes') AS jp_type_name
    FROM   swmanagement.games g
    WHERE  g.features -> 'jackpotTypes' IS NOT NULL
        AND  jsonb_typeof(g.features -> 'jackpotTypes') = 'array'
        AND  g.status = 'available'
    ),
    cte_direct_settings AS (
    SELECT eg.game_id, eg.entity_id, Coalesce(StrPos(e.path, e.name), 20000) AS tree_pos, e.path AS brand_path
    FROM   swmanagement.entity_games eg
            INNER JOIN cte_jp_games jg ON eg.game_id = jg.game_id
                                        AND eg.settings -> 'jackpotId' ? jg.jp_type_name
            INNER JOIN swmanagement.entities e ON e.id = eg.entity_id
    WHERE eg.status = 'normal'
        AND EXISTS (
                    SELECT NULL
                    FROM  swjackpot.jp_instance jpi
                    WHERE jpi.region_id IS NOT NULL
                    AND jpi.deleted_at IS NULL
                    AND jpi.pid = jsonb_extract_path_text(eg.settings, 'jackpotId', jg.jp_type_name)
                    )
    )
    INSERT INTO tmp_cache_jackpot_settings
    SELECT * FROM cte_direct_settings;

    -- Insert data to real table
    TRUNCATE TABLE monitoring.cache_jackpot_settings;
    INSERT INTO monitoring.cache_jackpot_settings
    SELECT * FROM tmp_cache_jackpot_settings;


    log_time := clock_timestamp(); log_msg := 'INFO: Refresh cache_jackpot_settings job finished'; RETURN NEXT;
    RETURN;
END;
$function$
;

SELECT cron.schedule('* * * * *', 'SET SESSION search_path TO monitoring; SELECT * FROM monitoring.fnc_refresh_cache_jackpot_settings();');

RESET search_path;
--rollback SET search_path = monitoring;
--rollback DROP TABLE IF EXISTS cache_jackpot_settings;
--rollback DROP FUNCTION IF EXISTS fnc_refresh_cache_jackpot_settings();
--rollback RESET search_path;


--changeset aleksey.ignatenko:2020-08-18_DEVOPS-6575_change_owner_1 endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Set admin (the user swsystem) as owner for all DB objects in the schema swadaptergos
SET search_path TO swbackup;
    
CREATE TABLE pg_ownership 
(
    obj_type    char(1),
    obj_schema  varchar(30),
    obj_name    varchar(90),
    obj_owner   varchar(30),
    created_at  timestamp       NOT NULL    DEFAULT now(),
    PRIMARY KEY (obj_schema, obj_name)
);

COMMENT ON TABLE pg_ownership   IS 'Ownership of database objects for rollback';
COMMENT ON COLUMN pg_ownership.obj_type     IS 'Object type';
COMMENT ON COLUMN pg_ownership.obj_schema   IS 'Schema name';
COMMENT ON COLUMN pg_ownership.obj_name     IS 'Object name';
COMMENT ON COLUMN pg_ownership.obj_owner    IS 'User who owns the object';
COMMENT ON COLUMN pg_ownership.created_at   IS 'Date-time of change';

RESET search_path;

DO $$
DECLARE
    v_sysuser   TEXT        := 'swsystem';
    a_schemas   TEXT[]      := '{swadaptergos,swadapterqs,swgameserver,swsrt}'; /* except swjackpot,swmanagement */
    v_sql       TEXT;
BEGIN
    -- Backup DB object ownership
    INSERT INTO swbackup.pg_ownership (obj_type, obj_schema, obj_name, obj_owner)
    SELECT 'D', s.nspname, s.nspname, u.usename 
    FROM pg_catalog.pg_namespace s
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = s.nspowner 
    WHERE s.nspname = ANY(a_schemas)
        AND u.usename <> v_sysuser
    UNION ALL 
    SELECT 'T', schemaname, tablename, tableowner 
    FROM pg_catalog.pg_tables t 
    WHERE schemaname = ANY(a_schemas)
        AND tableowner <> v_sysuser 
    UNION ALL
    SELECT 'V', schemaname, viewname, viewowner 
    FROM pg_catalog.pg_views v     
    WHERE schemaname = ANY(a_schemas)
        AND viewowner <> v_sysuser 
    UNION ALL    
    SELECT 'S', schemaname, sequencename, sequenceowner
    FROM pg_catalog.pg_sequences s 
    WHERE schemaname = ANY(a_schemas)
        AND sequenceowner <> v_sysuser 
    UNION ALL       
    SELECT 'P', s.nspname, p.proname, u.usename 
    FROM pg_catalog.pg_proc p 
    INNER JOIN pg_catalog.pg_namespace s ON s.oid = p.pronamespace
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = p.proowner
    WHERE s.nspname = ANY(a_schemas)
        AND u.usename <> v_sysuser
    UNION ALL
    SELECT 'L', s.nspname, t.typname, u.usename
    FROM pg_catalog.pg_type t 
    INNER JOIN pg_catalog.pg_namespace s ON s.oid = t.typnamespace
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = t.typowner
    WHERE s.nspname = ANY(a_schemas)
        AND u.usename <> v_sysuser
        AND t.typrelid = 0 AND t.typelem = 0 /* independent */
    ;    
    RAISE INFO E' [%] Stored all object ownership in the table swbackup.pg_ownership for backup', clock_timestamp();

    -- Change owner of schema and set default privileges in it
    SELECT string_agg(
                format('REASSIGN OWNED BY "%s" TO "%s";', s.nspname, v_sysuser) || E'\n' ||
                format('GRANT USAGE ON SCHEMA "%s" TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT ALL ON TABLES TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT ALL ON SEQUENCES TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT EXECUTE ON FUNCTIONS TO %s;', s.nspname, s.nspname) || E'\n' ||
                format('ALTER DEFAULT PRIVILEGES IN SCHEMA "%s" GRANT USAGE ON TYPES TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('GRANT ALL ON ALL TABLES IN SCHEMA "%s" TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('GRANT ALL ON ALL SEQUENCES IN SCHEMA "%s" TO "%s";', s.nspname, s.nspname) || E'\n' ||
                format('GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA "%s" TO "%s";', s.nspname, s.nspname)
                , E'\n' ORDER BY s.nspname)
        INTO v_sql
    FROM pg_catalog.pg_namespace s        
    WHERE s.nspname = ANY(a_schemas)
    ;
    IF (v_sql IS NULL) THEN
        RAISE INFO E' [%] Changing owner of schema and granting right: skipped', clock_timestamp();
    ELSE
        RAISE INFO E' [%] Changing owner of schema and granting rights: \n%', clock_timestamp(), v_sql;
        EXECUTE (v_sql);
    END IF;
    
    -- Change owner of data types 
    SELECT string_agg(CASE WHEN lower(t.typname) = t.typname 
                THEN format('GRANT USAGE ON TYPE %s.%s TO %s;', s.nspname, t.typname, s.nspname)
                ELSE format('GRANT USAGE ON TYPE %s."%s" TO %s;', s.nspname, t.typname, s.nspname)
            END, E'\n' ORDER BY s.nspname, t.typname)
        INTO v_sql
    FROM pg_catalog.pg_type t 
    INNER JOIN pg_catalog.pg_namespace s ON s.oid = t.typnamespace
    INNER JOIN pg_catalog.pg_user u ON u.usesysid = t.typowner
    WHERE s.nspname = ANY(a_schemas)
        AND t.typrelid = 0 AND t.typelem = 0 /* independent */
    ;
    IF (v_sql IS NULL) THEN
        RAISE INFO E' [%] Changing owner of data types: skipped', clock_timestamp();
    ELSE
        RAISE INFO E' [%] Changing owner of data types: \n%', clock_timestamp(), v_sql;
        EXECUTE (v_sql);
    END IF;
    
END $$;

--rollback DO $$
--rollback DECLARE
--rollback     v_sysuser   TEXT        := 'swsystem';
--rollback     a_schemas   TEXT[]      := '{swadaptergos,swadapterqs,swgameserver,swsrt}'; /* except swjackpot,swmanagement */
--rollback     v_sql       TEXT;
--rollback BEGIN
--rollback     
--rollback     -- Change owner of schema and set default privileges in it
--rollback     SELECT string_agg(
--rollback                 format('ALTER SCHEMA %s OWNER TO %s;', s.nspname, o.obj_owner)
--rollback                 , E'\n' ORDER BY s.nspname)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_namespace s 
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.nspname AND o.obj_name = s.nspname       
--rollback     WHERE s.nspname = ANY(a_schemas)
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of schema and granting default rights: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback 
--rollback     -- Change owner of tables and views
--rollback     WITH cte_tables AS 
--rollback     (
--rollback         SELECT schemaname, tablename, tableowner FROM pg_catalog.pg_tables t
--rollback         UNION ALL
--rollback         SELECT schemaname, viewname, viewowner FROM pg_catalog.pg_views v 
--rollback     )
--rollback     SELECT string_agg(CASE WHEN lower(t.tablename) = t.tablename 
--rollback                THEN format('ALTER TABLE %s.%s OWNER TO %s;', t.schemaname, t.tablename, o.obj_owner)
--rollback                ELSE format('ALTER TABLE %s."%s" OWNER TO %s;', t.schemaname, t.tablename, o.obj_owner)
--rollback            END, E'\n' ORDER BY t.schemaname, t.tablename)
--rollback         INTO v_sql
--rollback     FROM cte_tables t
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = t.schemaname AND o.obj_name = t.tablename        
--rollback     WHERE t.schemaname = ANY(a_schemas)
--rollback         AND t.tableowner = v_sysuser
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of tables/views: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback 
--rollback     -- Change owner of sequences 
--rollback     SELECT string_agg(CASE WHEN lower(s.sequencename) = s.sequencename 
--rollback                THEN format('ALTER SEQUENCE %s.%s OWNER TO %s;', s.schemaname, s.sequencename, o.obj_owner)
--rollback                ELSE format('ALTER SEQUENCE %s."%s" OWNER TO %s;', s.schemaname, s.sequencename, o.obj_owner)
--rollback            END, E'\n' ORDER BY s.schemaname, s.sequencename)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_sequences s
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.schemaname AND o.obj_name = s.sequencename
--rollback     WHERE s.schemaname = ANY(a_schemas)
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of sequences: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback     
--rollback     -- Change owner of functions 
--rollback     SELECT string_agg(CASE WHEN lower(p.proname) = p.proname 
--rollback                THEN format('ALTER FUNCTION %s.%s OWNER TO %s;', s.nspname, p.proname, o.obj_owner)
--rollback                ELSE format('ALTER FUNCTION %s."%s" OWNER TO %s;', s.nspname, p.proname, o.obj_owner)
--rollback            END, E'\n' ORDER BY s.nspname, p.proname)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_proc p 
--rollback     INNER JOIN pg_catalog.pg_namespace s ON s.oid = p.pronamespace
--rollback     INNER JOIN pg_catalog.pg_user u ON u.usesysid = p.proowner
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.nspname AND o.obj_name = p.proname
--rollback     WHERE s.nspname = ANY(a_schemas)
--rollback         AND u.usename = v_sysuser
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of functions: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback 
--rollback     -- Change owner of data types 
--rollback     SELECT string_agg(CASE WHEN lower(t.typname) = t.typname 
--rollback                THEN format('ALTER TYPE %s.%s OWNER TO %s;', s.nspname, t.typname, o.obj_owner)
--rollback                ELSE format('ALTER TYPE %s."%s" OWNER TO %s;', s.nspname, t.typname, o.obj_owner)
--rollback            END, E'\n' ORDER BY s.nspname, t.typname)
--rollback         INTO v_sql
--rollback     FROM pg_catalog.pg_type t 
--rollback     INNER JOIN pg_catalog.pg_namespace s ON s.oid = t.typnamespace
--rollback     INNER JOIN pg_catalog.pg_user u ON u.usesysid = t.typowner
--rollback     INNER JOIN swbackup.pg_ownership o ON o.obj_schema = s.nspname AND o.obj_name = t.typname
--rollback     WHERE s.nspname = ANY(a_schemas)
--rollback         AND u.usename = v_sysuser
--rollback         AND t.typrelid = 0 AND t.typelem = 0 
--rollback     ;        
--rollback     RAISE INFO E' [%] Changing owner of data types: \n%', clock_timestamp(), v_sql;
--rollback     EXECUTE (COALESCE(v_sql, ''));
--rollback END $$;
--rollback 
--rollback SET search_path TO swbackup;
--rollback DROP TABLE pg_ownership;
--rollback RESET search_path;

--changeset aleh.rudzko:2020-09-11-SWS-21426-Create-permissions-for-GVC-Daily-Report
--comment add new permissions for gvc daily report
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'bi:report:gvc-daily' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:gvc-daily' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:bi:report:gvc-daily", "bi:report:gvc-daily"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:gvc-daily' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:gvc-daily' WHERE id = 1;
--rollback RESET search_path;
