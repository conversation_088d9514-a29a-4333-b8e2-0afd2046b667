--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset mikhail.ivanov:2022-07-15-SWS-35463-children-see-inherited-from-entity-title-of-parents
--comment add permissions to see inheritedFromEntityId and inheritedFromEntityTitle parameters into permissions table
SET search_path = swmanagement;
DELETE FROM permissions WHERE code IN ('gamertp:detailed-view', 'keyentity:gamertp:detailed-view');
INSERT INTO permissions(code, description, created_at, updated_at) VALUES
('gamertp:detailed-view', 'Allow to get inheritedFromEntityId and inheritedFromEntityTitle', NOW(), NOW()),
('keyentity:gamertp:detailed-view', 'Allow to get inheritedFromEntityId and inheritedFromEntityTitle on a keyentity level', NOW(), NOW());
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM permissions WHERE code IN ('gamertp:detailed-view', 'keyentity:gamertp:detailed-view');
--rollback RESET search_path;
