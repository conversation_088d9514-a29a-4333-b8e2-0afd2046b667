--liquibase formatted sql

--changeset artur.stepovyi:2020-10-07-SWS-21906-remove-permissions
--comment remove permission from roles
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'promotion:virtualmoney';
UPDATE roles SET permissions = permissions - 'promotion:rebate';
UPDATE roles SET permissions = permissions - 'promotion:virtualmoney:view';
UPDATE roles SET permissions = permissions - 'promotion:rebate:view';
UPDATE roles SET permissions = permissions - 'promotion:virtualmoney:create';
UPDATE roles SET permissions = permissions - 'promotion:rebate:create';
UPDATE roles SET permissions = permissions - 'promotion:virtualmoney:edit';
UPDATE roles SET permissions = permissions - 'promotion:rebate:edit';
UPDATE roles SET permissions = permissions - 'promotion:virtualmoney:delete';
UPDATE roles SET permissions = permissions - 'promotion:rebate:delete';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:virtualmoney';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:rebate';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:virtualmoney:view';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:rebate:view';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:virtualmoney:create';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:rebate:create';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:virtualmoney:edit';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:rebate:edit';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:virtualmoney:delete';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:rebate:delete';
RESET search_path;
--rollback SET search_path = swmanagement;
--UPDATE roles SET permissions = permissions || '["promotion:virtualmoney", "promotion:rebate", "promotion:virtualmoney:view", "promotion:rebate:view", "promotion:virtualmoney:create", "promotion:rebate:create", "promotion:virtualmoney:edit", "promotion:rebate:edit", "promotion:virtualmoney:delete", "promotion:rebate:delete", "keyentity:promotion:virtualmoney", "keyentity:promotion:rebate", "keyentity:promotion:virtualmoney:view", "keyentity:promotion:rebate:view", "keyentity:promotion:virtualmoney:create", "keyentity:promotion:rebate:create", "keyentity:promotion:virtualmoney:edit", "keyentity:promotion:rebate:edit", "keyentity:promotion:virtualmoney:delete", "keyentity:promotion:rebate:delete"]' WHERE id = 1;
--rollback RESET search_path;

--changeset nataliia.pliashko:2020-09-30-SWS-21909-create-table-merchant-player-nick_name
--comment create table 'merchant_player_nick_name'
SET search_path = swmanagement;

   CREATE SEQUENCE IF NOT EXISTS merchant_player_nick_name_id_seq;

   CREATE TABLE IF NOT EXISTS merchant_player_nick_name (
       id bigint NOT NULL DEFAULT nextval('merchant_player_nick_name_id_seq'),
       brand_id INTEGER NOT NULL,
       player_code VARCHAR(255) NOT NULL,
       nickname VARCHAR(15) NOT NULL,
       created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now(),
       updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now(),
       PRIMARY KEY (id),
       FOREIGN KEY (brand_id) REFERENCES entities (id) ON DELETE CASCADE ON UPDATE CASCADE
    );

   CREATE INDEX IF NOT EXISTS idx_merchant_player_nick_name_brand_id ON merchant_player_nick_name (brand_id);
   CREATE INDEX IF NOT EXISTS idx_merchant_player_nick_name_player_code ON merchant_player_nick_name (player_code);
   CREATE UNIQUE INDEX IF NOT EXISTS idx_merchant_player_nick_name_brand_id_player_code ON merchant_player_nick_name (brand_id, player_code);
   CREATE UNIQUE INDEX IF NOT EXISTS idx_merchant_player_nick_name_brand_id_nickname ON merchant_player_nick_name (brand_id, nickname);


   COMMENT ON TABLE merchant_player_nick_name IS 'Table for adding player nicknames per merchant';
   COMMENT ON COLUMN merchant_player_nick_name.brand_id IS 'Reference to entity id';
   COMMENT ON COLUMN merchant_player_nick_name.player_code IS 'Player code';
   COMMENT ON COLUMN merchant_player_nick_name.nickname IS 'Player nickname';
   COMMENT ON COLUMN merchant_player_nick_name.created_at IS 'Addition time of event';
   COMMENT ON COLUMN merchant_player_nick_name.updated_at IS 'Modification time of event';


   ALTER TABLE players ADD COLUMN nickname VARCHAR(15);
   COMMENT ON COLUMN players.nickname IS 'Player nickname';

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback     ALTER TABLE players DROP COLUMN nickname;
--rollback     DROP TABLE IF EXISTS merchant_player_nick_name;
--rollback     DROP SEQUENCE IF EXISTS merchant_player_nick_name_id_seq;
--rollback RESET search_path = swmanagement;


--changeset nataliia.pliashko:2020-09-30-SWS-21909-create-players_nickname_idx runInTransaction:false
--comment create new index for players brand and nickname
SET search_path = swmanagement;
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_players_brand_id_nickname ON players (brand_id, nickname);
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP INDEX IF EXISTS idx_players_brand_id_nickname;
--rollback RESET search_path = swmanagement;


--changeset aleksey.ignatenko:2020-10-21_DEVOPS-10766_improve_game_rtp_hist_func endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Improved func fnc_list_game_rtp_history. Added params for Limit, Offset; Added 2 columns for titles of entity and game in result
SET search_path TO swmanagement;

ALTER FUNCTION fnc_list_game_rtp_history RENAME TO fnc_list_game_rtp_history_before_4_47;

CREATE OR REPLACE FUNCTION fnc_list_game_rtp_history
(
    p_entity_id     integer,
    p_game_codes    varchar[]   DEFAULT NULL,
    p_ts_from       timestamp   DEFAULT NULL,
    p_ts_till       timestamp   DEFAULT NULL,
    p_limit         integer     DEFAULT 100, 
    p_offset        integer     DEFAULT 0
)
 RETURNS TABLE(
    id              int4,
    entity_id       int4,
    game_id         int4,
    rtp_info        jsonb,      -- JSON with rtp info, stored in game features and entity game settings
    rtp_deduction   jsonb,    -- JSON with rtp deduction info, stored in entity game settings
    ts              timestamp,  -- Time of creation
    game_code       varchar(255),
    entity_title    varchar(255),
    game_title      varchar(255) ) --SETOF swmanagement.game_rtp_history 
 LANGUAGE plpgsql
AS  $function$
/********************************************************************************************************
    Object Name: fnc_list_game_rtp_history
    Purpose    : List from the table game_rtp_history for given entity_id, list of games and period,
                with merged rtp_deduction from the entity ancestors 
    Add records in entity_games base for given entity_id and its childs. Records based on parents entity_games record.
    History    :
        1.0.0
            Date    : Jul 09, 2020
            Authors : Ales
            Notes   : Release (DEVOPS-9481)
        1.0.1 - v.4.47
            Date    : Oct 21, 2020
            Authors : Ales
            Notes   : Added params for Limit, Offset, added 2 columns for titles of entity and game in result (DEVOPS-10766)
            
    Sample run:
        SELECT * FROM swmanagement.fnc_list_game_rtp_history ( 
                                        p_entity_id => 40493
                                       --, p_game_codes => '{sw_prli, sw_qow, sw_omq}'::VARCHAR[]
                                       --, p_ts_from => '200709 15:42'
                                       --, p_ts_till => '200709 15:42'
                                    );
                                    
********************************************************************************************************/
DECLARE
    v_code  VARCHAR;
BEGIN
    
    /* Check mandatory params */
    IF (p_entity_id IS NULL) THEN
        RAISE EXCEPTION 'Parameter "p_entity_id" must be defined!';
    END IF;

    IF coalesce(p_limit, -1) < 0 THEN
      RAISE EXCEPTION 'Parameter "p_limit" must be positive!';
    END IF;

    IF coalesce(p_offset, -1) < 0 THEN
      RAISE EXCEPTION 'Parameter "p_offset" must be positive!';
    END IF;
    
    RETURN QUERY 
        WITH RECURSIVE cte_entity_hier_up AS
    ( -- full hierarchy tree to root direction for given entity_id
        SELECT e.id, e.parent, e.name, e.type, 0 AS root_level, e.title, e.path, e.is_test
        FROM swmanagement.entities e
        WHERE e.id = p_entity_id
        UNION ALL
        SELECT e.id, e.parent, e.name, e.type, h.root_level + 1 AS root_level, e.title, e.path, e.is_test
        FROM cte_entity_hier_up h
        INNER JOIN swmanagement.entities e ON e.id = h.parent
    )
    , cte_game_rtp_hist AS
    ( -- history of the given entity, its ancients
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
            e.root_level, e.path         
        FROM swmanagement.game_rtp_history h
        INNER JOIN cte_entity_hier_up e ON e.id = h.entity_id
        WHERE (p_game_codes IS NULL OR h.game_code = ANY(p_game_codes))            
            AND (p_ts_till IS NULL OR h.ts <= p_ts_till)
    )
    , cte_game_rtp_hist_full AS
    ( -- add history of games which have links to the main entity
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code, 
            root_level
        FROM cte_game_rtp_hist h
        UNION ALL 
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
            999 AS root_level         
        FROM swmanagement.game_rtp_history h        
        WHERE h.entity_id IS NULL 
            AND h.game_id IN (SELECT i.game_id FROM cte_game_rtp_hist i WHERE i.entity_id = p_entity_id)
            AND (p_ts_till IS NULL OR h.ts <= p_ts_till)
    )
    , cte_rtp_deduction_values AS 
    ( -- all rtp_deduction key-value pairs */
        SELECT h.id, h.entity_id, h.game_id, h.ts,
            root_level,
            (jsonb_each(h.rtp_deduction))."key" AS rtp_key,
            (jsonb_each(h.rtp_deduction)).value AS rtp_val
        FROM cte_game_rtp_hist h
    )
    , cte_game_rtp_hist_ext AS
    (
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
                v.rtp_key,
                FIRST_VALUE(v.rtp_val) OVER (
                        PARTITION BY h.entity_id, h.game_id, h.ts, v.rtp_key
                        ORDER BY v.root_level, v.ts DESC
                ) AS rtp_val
        FROM cte_game_rtp_hist_full h
        LEFT JOIN cte_rtp_deduction_values v 
            ON v.id = h.id OR (v.game_id = h.game_id AND v.ts <= h.ts AND v.root_level < h.root_level)        
    )
    , cte_game_rtp_hist_processed AS 
    (
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, 
            CASE WHEN max(rtp_key) IS NULL
                THEN '{}'::jsonb
                ELSE jsonb_object_agg(COALESCE (rtp_key, '-'), rtp_val )
            END AS rtp_deduction,
            h.ts, h.game_code
        FROM cte_game_rtp_hist_ext h
        WHERE (p_ts_from IS NULL OR h.ts >= p_ts_from)
        GROUP BY h.id, h.entity_id, h.game_id, h.rtp_info, h.ts, h.game_code            
    )
    SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code, 
            e.title AS entity_title, g.title AS game_title
    FROM cte_game_rtp_hist_processed h
    LEFT JOIN swmanagement.entities e   ON e.id = h.entity_id
    LEFT JOIN swmanagement.games g      ON g.id = h.game_id
    ORDER BY h.game_id, h.ts, h.entity_id
    LIMIT p_limit OFFSET p_offset;
    
END $function$;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP FUNCTION fnc_list_game_rtp_history;
--rollback ALTER FUNCTION fnc_list_game_rtp_history_before_4_47 RENAME TO fnc_list_game_rtp_history;
--rollback RESET search_path;


--changeset aleksey.stepanov:2020-10-29-SWS-22456-add-dashboards-for-table-management
--comment create Dashboards in Live Studio Hub
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'dashboard' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:dashboard' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'dashboard:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:dashboard:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'dashboard:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:dashboard:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'dashboard:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:dashboard:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'dashboard:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:dashboard:delete' WHERE id = 1;

UPDATE roles SET permissions = permissions ||
'["dashboard", "keyentity:dashboard", "dashboard:view", "keyentity:dashboard:view", "dashboard:edit", "keyentity:dashboard:edit", "dashboard:create","keyentity:dashboard:create","dashboard:delete","keyentity:dashboard:delete"]'::jsonb WHERE id = 1;

CREATE TABLE IF NOT EXISTS pht_dashboards (
    id SERIAL PRIMARY KEY,
    entity_id int4 NOT NULL,
    title varchar(255) NOT NULL,
    status varchar(255) NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW(),
    signals jsonb,
    CONSTRAINT pht_dashboards_entity_id_fkey FOREIGN KEY (entity_id)
        REFERENCES entities (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE
);
CREATE INDEX idx_pht_dashboards_entity_id ON pht_dashboards (entity_id);
COMMENT ON TABLE pht_dashboards IS 'Dashboards';
COMMENT ON COLUMN pht_dashboards.id IS 'Id of dashboard';
COMMENT ON COLUMN pht_dashboards.entity_id IS 'Entity ID';
COMMENT ON COLUMN pht_dashboards.title IS 'Dashboard title';
COMMENT ON COLUMN pht_dashboards.status IS 'Status (active/inactive)';
COMMENT ON COLUMN pht_dashboards.created_at IS 'Created At';
COMMENT ON COLUMN pht_dashboards.updated_at IS 'Updated At';
COMMENT ON COLUMN pht_dashboards.signals IS 'Signals/Events for Dealer';

CREATE TABLE IF NOT EXISTS pht_dashboard_physical_tables (
    physical_table_id int4 NOT NULL,
    dashboard_id int4 NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    PRIMARY KEY (physical_table_id, dashboard_id),
    CONSTRAINT pht_dashboard_physical_tables_physical_table_id_fkey FOREIGN KEY (physical_table_id)
        REFERENCES pht_physical_tables (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE,
    CONSTRAINT pht_dashboard_physical_tables_dashboard_id_fkey FOREIGN KEY (dashboard_id)
        REFERENCES pht_dashboards (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE
);
CREATE INDEX idx_pht_dashboard_physical_tables_dashboard_id ON pht_dashboard_physical_tables (dashboard_id);
COMMENT ON TABLE pht_dashboard_physical_tables IS 'Dashboards Physical tables';
COMMENT ON COLUMN pht_dashboard_physical_tables.physical_table_id IS 'Physical Table ID';
COMMENT ON COLUMN pht_dashboard_physical_tables.dashboard_id IS 'Dashboard ID';
COMMENT ON COLUMN pht_dashboard_physical_tables.created_at IS 'Created At';

CREATE TABLE IF NOT EXISTS pht_dashboard_users (
    user_id int4 NOT NULL,
    dashboard_id int4 NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    PRIMARY KEY (user_id, dashboard_id),
    CONSTRAINT pht_dashboard_users_user_id_fkey FOREIGN KEY (user_id)
        REFERENCES users (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE,
    CONSTRAINT pht_dashboard_physical_tables_dashboard_id_fkey FOREIGN KEY (dashboard_id)
        REFERENCES pht_dashboards (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE
);
CREATE INDEX idx_pht_dashboard_users_dashboard_id ON pht_dashboard_users (dashboard_id);
COMMENT ON TABLE pht_dashboard_users IS 'Dashboards Users';
COMMENT ON COLUMN pht_dashboard_users.user_id IS 'User ID';
COMMENT ON COLUMN pht_dashboard_users.dashboard_id IS 'Dashboard ID';
COMMENT ON COLUMN pht_dashboard_users.created_at IS 'Created At';

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'dashboard' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:dashboard' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'dashboard:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:dashboard:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'dashboard:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:dashboard:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'dashboard:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:dashboard:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'dashboard:delete' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:dashboard:delete' WHERE id = 1;

--rollback DROP TABLE IF EXISTS pht_dashboard_users;
--rollback DROP TABLE IF EXISTS pht_dashboard_physical_tables;
--rollback DROP TABLE IF EXISTS pht_dashboards;
--rollback RESET search_path;

--changeset sergey.malkov:2020-10-30-SWB365-345-Set-is-free-bet-supported-flag-for-b365-games
--comment set value for 'isFreebetSupported' field for games with jackpot that will be deployed to B365
SET search_path = swmanagement;
UPDATE games SET features=jsonb_set(features, '{isFreebetSupported}', 'false') WHERE code IN ('sw_azreeu', 'sw_bloo', 'sw_desheurb', 'sw_dosc7s', 'sw_doab', 'sw_frreeu', 'sw_gemerenigael', 'sw_ges_eu', 'sw_glreeu', 'sw_gs_eu', 'sw_hosheu', 'sw_ijp', 'sw_noreeu', 'sw_poreeu', 'sw_ps_eu', 'sw_reki', 'sw_rireeu', 'sw_roriyang', 'sw_suli', 'sw_thlaki', 'sw_thmase', 'sw_tisheu', 'sw_vfv', 'sw_wrl', 'sw_suli', 'sw_omqjp', 'sw_mdls', 'sw_fufish-jp', 'sw_jqw_ab_jp');
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE games SET features=jsonb_strip_nulls(jsonb_set(features, '{isFreebetSupported}', 'null')) WHERE code IN ('sw_azreeu', 'sw_bloo', 'sw_desheurb', 'sw_dosc7s', 'sw_doab', 'sw_frreeu', 'sw_gemerenigael', 'sw_ges_eu', 'sw_glreeu', 'sw_gs_eu', 'sw_hosheu', 'sw_ijp', 'sw_noreeu', 'sw_poreeu', 'sw_ps_eu', 'sw_reki', 'sw_rireeu', 'sw_roriyang', 'sw_suli', 'sw_thlaki', 'sw_thmase', 'sw_tisheu', 'sw_vfv', 'sw_wrl', 'sw_suli', 'sw_omqjp', 'sw_mdls', 'sw_fufish-jp', 'sw_jqw_ab_jp');
--rollback RESET search_path;


--changeset artur.stepovyi:2020-10-07-SWS-21906-remove-permissions-2
--comment remove permissions from permissions table
SET search_path = swmanagement;
DELETE FROM permissions WHERE code='promotion:virtualmoney';
DELETE FROM permissions WHERE code='promotion:rebate';
DELETE FROM permissions WHERE code='promotion:virtualmoney:view';
DELETE FROM permissions WHERE code='promotion:rebate:view';
DELETE FROM permissions WHERE code='promotion:virtualmoney:create';
DELETE FROM permissions WHERE code='promotion:rebate:create';
DELETE FROM permissions WHERE code='promotion:virtualmoney:edit';
DELETE FROM permissions WHERE code='promotion:rebate:edit';
DELETE FROM permissions WHERE code='promotion:virtualmoney:delete';
DELETE FROM permissions WHERE code='promotion:rebate:delete';
DELETE FROM permissions WHERE code='keyentity:promotion:virtualmoney';
DELETE FROM permissions WHERE code='keyentity:promotion:rebate';
DELETE FROM permissions WHERE code='keyentity:promotion:virtualmoney:view';
DELETE FROM permissions WHERE code='keyentity:promotion:rebate:view';
DELETE FROM permissions WHERE code='keyentity:promotion:virtualmoney:create';
DELETE FROM permissions WHERE code='keyentity:promotion:rebate:create';
DELETE FROM permissions WHERE code='keyentity:promotion:virtualmoney:edit';
DELETE FROM permissions WHERE code='keyentity:promotion:rebate:edit';
DELETE FROM permissions WHERE code='keyentity:promotion:virtualmoney:delete';
DELETE FROM permissions WHERE code='keyentity:promotion:rebate:delete';
UPDATE roles SET permissions = permissions - 'promotion:virtualmoney';
UPDATE roles SET permissions = permissions - 'promotion:rebate';
UPDATE roles SET permissions = permissions - 'promotion:virtualmoney:view';
UPDATE roles SET permissions = permissions - 'promotion:rebate:view';
UPDATE roles SET permissions = permissions - 'promotion:virtualmoney:create';
UPDATE roles SET permissions = permissions - 'promotion:rebate:create';
UPDATE roles SET permissions = permissions - 'promotion:virtualmoney:edit';
UPDATE roles SET permissions = permissions - 'promotion:rebate:edit';
UPDATE roles SET permissions = permissions - 'promotion:virtualmoney:delete';
UPDATE roles SET permissions = permissions - 'promotion:rebate:delete';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:virtualmoney';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:rebate';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:virtualmoney:view';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:rebate:view';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:virtualmoney:create';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:rebate:create';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:virtualmoney:edit';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:rebate:edit';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:virtualmoney:delete';
UPDATE roles SET permissions = permissions - 'keyentity:promotion:rebate:delete';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions || '["promotion:virtualmoney", "promotion:rebate", "promotion:virtualmoney:view", "promotion:rebate:view", "promotion:virtualmoney:create", "promotion:rebate:create", "promotion:virtualmoney:edit", "promotion:rebate:edit", "promotion:virtualmoney:delete", "promotion:rebate:delete", "keyentity:promotion:virtualmoney", "keyentity:promotion:rebate", "keyentity:promotion:virtualmoney:view", "keyentity:promotion:rebate:view", "keyentity:promotion:virtualmoney:create", "keyentity:promotion:rebate:create", "keyentity:promotion:virtualmoney:edit", "keyentity:promotion:rebate:edit", "keyentity:promotion:virtualmoney:delete", "keyentity:promotion:rebate:delete"]' WHERE id = 1;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2020-10-30_SWS-22621_rename_col_in_foreign_server_options endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Set new column name audits_summary_id instead of audit_type in column list for foreign server in func fnc_bo_audits remote query
DO $$
DECLARE
   r record;
BEGIN
   FOR r IN
            WITH get_fdw_tables AS (
               SELECT ftoptions_json ->> 'schema_name' AS remote_schema_name
                     ,ftoptions_json ->> 'table_name'  AS remote_table_name
                     ,local_table_name
                     ,local_schema_name
               FROM  (
                        SELECT f.ftrelid, jsonb_object(string_to_array(Replace(array_to_string(f.ftoptions,','), '=', ','), ',')) AS ftoptions_json
                              ,c.relname AS local_table_name, ns.nspname AS local_schema_name
                        FROM   pg_foreign_table f
                               INNER JOIN
                               pg_class c ON f.ftrelid = c.oid
                               INNER JOIN
                               pg_namespace ns ON c.relnamespace = ns.oid
                     ) ft
            )
            SELECT remote_schema_name
                  ,remote_table_name
                  ,local_table_name
                  ,local_schema_name
            FROM   get_fdw_tables
            WHERE  local_schema_name ~* 'swmanagement_archive.*'
              AND  local_table_name ~* 'audit.+'
            ORDER BY 1, 2
   LOOP
      SET client_min_messages TO INFO;
      RAISE INFO '[%] Do %.%', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'), r.local_schema_name, r.local_table_name;
      RESET client_min_messages;

      EXECUTE Format ($SQL1$ ALTER TABLE %s.%s ALTER audits_summary_id OPTIONS ( SET column_name 'audits_summary_id') $SQL1$, r.local_schema_name, r.local_table_name);
   END LOOP;
END $$;

--rollback select now();

--changeset evgeniy.gandzyuck:2020-11-12-SWS-21106-add-serverUrl-to-sisal-merchant-schema
--comment add serverUrl parameter to sisal merchant schema
SET search_path = swmanagement;
CREATE TABLE swbackup.merchant_types_201112_sws21106 AS SELECT * FROM merchant_types WHERE type = 'sisal';
INSERT INTO merchant_types as mt (type, url, schema, created_at, updated_at)
VALUES('sisal', null, '{
  "serverUrl": {
    "type": "text",
    "title": "MERCHANT.PARAMETERS.serverUrl",
    "defaultValue": ""
  }
}'::JSONB, now(), now())
ON CONFLICT (type) DO
UPDATE SET schema = mt.schema || '{
    "serverUrl": {
        "type": "text",
        "title": "MERCHANT.PARAMETERS.serverUrl",
        "defaultValue": ""
        }
}'::JSONB, updated_at = now();
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DELETE FROM merchant_types WHERE type = 'sisal';
--rollback INSERT INTO merchant_types(type, url, schema, created_at, updated_at)
--rollback SELECT type, url, schema, created_at, updated_at FROM swbackup.merchant_types_201112_sws21106;
--rollback DROP TABLE IF EXISTS swbackup.merchant_types_201112_sws21106;
--rollback RESET search_path;


--changeset maksim.puzikov:2020-11-06-SWBETV2-94-add-multibets-info
--comment updates games info: add flag isMultibet for sw_ge1xeu-te and sw_jolu
SET search_path = swmanagement;
UPDATE games
SET features = features || '{"isMultibet":true}'::jsonb
WHERE code in ('sw_ge1xeu-te', 'sw_jolu', 'sw_lohy', 'sw_gljoit', 'sw_sudelaoc', 'sw_ge1xas-te');
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE games
--rollback SET features = features - 'isMultibet'
--rollback WHERE code in ('sw_ge1xeu-te', 'sw_jolu', 'sw_lohy', 'sw_gljoit', 'sw_sudelaoc', 'sw_ge1xas-te');
--rollback RESET search_path;
