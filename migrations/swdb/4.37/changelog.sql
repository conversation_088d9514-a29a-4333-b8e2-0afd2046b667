--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset timur.luchkin:2020-05-08-SWS-XXXX-start-release-4.37.0
--comment label for 4.37.0
select now();
--rollback select now();


--changeset timur.luchkin:2020-05-08-SWS-18477-add-new-way-to-access-historical-cluster endDelimiter:# stripComments:false
--comment Add possibility to access historical cluster parent table directly without touching live MED partitions.
DO
$$
DECLARE
   -- TLU: Create new schema to access historical cluster in other way (read-only!)
   fs_name                 TEXT;
   arr_access_roles        TEXT[] := '{swmanagement,role_usr_dba,role_usr_default,redis_game_offloader,kafka_offloader}';
   rec_roles               RECORD;
   target_schema_swman     TEXT := 'swmanagement_archive_ro';
   target_schema_swjpt     TEXT := 'swjackpot_archive_ro';

   arr_swman_tables        TEXT[] := '{audits,rounds_finished,rounds_history,sessions_history,spins_history,wallet_entity_payment_log,wallet_operation_log,wallet_win_bet}';
   arr_swjpt_tables        TEXT[] := '{jp_contribution_log,jp_wallet_operation_log}';
   loop_table              TEXT;
BEGIN
   SET client_min_messages TO INFO;

   -- To prevent double run of this code
   IF EXISTS (SELECT null FROM pg_namespace WHERE nspname = target_schema_swman OR nspname = target_schema_swjpt) THEN
      RAISE INFO '[%] New schema already exists. Skip creation ...', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS');
      RETURN;
   END IF;

   -- Check for the foreign server
   BEGIN
      SELECT srvname
      FROM   pg_foreign_server WHERE srvname ~* '^sw_hcl.+ro$'
      INTO   STRICT fs_name;
   EXCEPTION
      WHEN no_data_found THEN
         RAISE INFO '[%] Foreign server not found. Will do emulation', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS');
      WHEN too_many_rows THEN
         RAISE EXCEPTION '[%] Too many foreign servers. Can''t choose one!', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS');
   END;

   -- swmanagement
   EXECUTE FORMAT('CREATE SCHEMA IF NOT EXISTS %s', target_schema_swman);
   EXECUTE FORMAT('ALTER SCHEMA %s OWNER TO swsystem', target_schema_swman);
   -- swjackpot
   EXECUTE FORMAT('CREATE SCHEMA IF NOT EXISTS %s', target_schema_swjpt);
   EXECUTE FORMAT('ALTER SCHEMA %s OWNER TO swsystem', target_schema_swjpt);


   FOR rec_roles IN SELECT * FROM pg_roles WHERE rolname = ANY(arr_access_roles)
   LOOP
         EXECUTE FORMAT('GRANT USAGE ON SCHEMA %s TO %s', target_schema_swman, rec_roles.rolname);
         EXECUTE FORMAT('ALTER DEFAULT PRIVILEGES IN SCHEMA %s FOR ROLE swsystem GRANT SELECT ON TABLES TO %s', target_schema_swman, rec_roles.rolname);

         EXECUTE FORMAT('GRANT USAGE ON SCHEMA %s TO %s', target_schema_swjpt, rec_roles.rolname);
         EXECUTE FORMAT('ALTER DEFAULT PRIVILEGES IN SCHEMA %s FOR ROLE swsystem GRANT SELECT ON TABLES TO %s', target_schema_swjpt, rec_roles.rolname);
   END LOOP;

   IF fs_name IS NOT NULL THEN
   -- Real foreign server (Prod/Stage)

      -- swmanagement
      FOREACH loop_table IN ARRAY arr_swman_tables
      LOOP
         EXECUTE FORMAT('IMPORT FOREIGN SCHEMA swmanagement LIMIT TO (swmanagement.%s) FROM SERVER %s INTO %s', loop_table, fs_name, target_schema_swman);
      END LOOP;

      -- swjackpot
      FOREACH loop_table IN ARRAY arr_swjpt_tables
      LOOP
         EXECUTE FORMAT('IMPORT FOREIGN SCHEMA swjackpot LIMIT TO (swjackpot.%s) FROM SERVER %s INTO %s', loop_table, fs_name, target_schema_swjpt);
      END LOOP;


    ELSE
   -- Emulation (QA/Dev)

      -- swmanagement
      FOREACH loop_table IN ARRAY arr_swman_tables
      LOOP
         EXECUTE FORMAT('CREATE TABLE %s.%s (LIKE swmanagement_archive.%s INCLUDING ALL EXCLUDING DEFAULTS)', target_schema_swman, loop_table, loop_table);
      END LOOP;

      -- swjackpot
      FOREACH loop_table IN ARRAY arr_swjpt_tables
      LOOP
         EXECUTE FORMAT('CREATE TABLE %s.%s (LIKE swjackpot_archive.%s INCLUDING ALL EXCLUDING DEFAULTS)', target_schema_swjpt, loop_table, loop_table);
      END LOOP;
   END IF;


   RESET client_min_messages;
END;
$$;
--rollback select now();

--changeset alexander.guzanov:2020-05-11-SWS-18340-default-merchant-version
--comment Merchant default version
SET search_path TO swmanagement;

ALTER TABLE merchants ALTER COLUMN version SET DEFAULT 0;
UPDATE merchants SET version=0;
ALTER TABLE merchants ALTER COLUMN version SET NOT NULL;

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE merchants ALTER COLUMN version DROP NOT NULL;
--rollback ALTER TABLE merchants ALTER COLUMN version DROP DEFAULT;
--rollback RESET search_path;

--changeset valdis.akmens:2020-05-15-SWDB-138-optimize-fnc_bo_rounds_history-function-for-archive-cluster-usage endDelimiter:# stripComments:false
--comment Based on date interval choose different archive schema
SET search_path TO swmanagement;

ALTER FUNCTION fnc_bo_rounds_history (VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN) RENAME TO fnc_bo_rounds_history_before_4_37_0;
ALTER FUNCTION fnc_bo_rounds_history_inner(VARCHAR[], VARCHAR, INT, INT, VARCHAR, BOOLEAN, VARCHAR[], BOOLEAN, VARCHAR) RENAME TO fnc_bo_rounds_history_inner_before_4_37_0;

CREATE OR REPLACE FUNCTION fnc_bo_rounds_history(p_where_filters character varying[], p_sort_by character varying[] DEFAULT NULL::character varying[], p_limit integer DEFAULT 20, p_offset integer DEFAULT 0, p_incl_sub_brands boolean DEFAULT false, p_incl_test boolean DEFAULT true)
 RETURNS SETOF rounds_finished
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************

    Object Name:   fnc_bo_rounds_history 
    Purpose    : Provide read access to rounds_history table and rounds_unfinished and rounds_finished
                If not partition key in WHERE, than only to new partitions. If is partition key, than check pathman_config, and pathman_partition_list to see if we need:
                new table
                old table
                or both tables union
    History    :
        1.0.0(?)
            Date    : Oct 16, 2018
            Authors : Valdis Akmens
            Notes   : Release (SWS-6904)
        1.0.1
            Date    : Jan 30, 2019
            Authors : Valdis Akmens
            Notes   : Change SELECT list for tables to dynamic generated to automatically include new columns to function result (SWDB-82)
        1.0.2
            Date    : May 02, 2019
            Authors : Valdis Akmens
            Notes   : Add parameters for including sub-entities in result (SWS-9902)
                    Work with sub-entities moved to special function - fnc_bo_rounds_history_inner - to optimize performance.
        1.0.3
            Date    : May 30, 2019
            Authors : Valdis Akmens
            Notes   : Add support for the historical data in Rounds history related calls.
                    Added call to "swmanagement_archive.rounds_history" if is definied "started_at" in p_where_filters and it points to "swmanagement_archive.rounds_history"
                    time interval (SWS-10600)
        1.0.4
            Date    : Oct 14, 2019
            Authors : Valdis Akmens
            Notes   : Add support for "swmanagement_archive.rounds_finished" and remove unfinished rounds view "swmanagement.v_rounds_unfinished"
                    (SWDB-117)
        1.0.5
            Date    : Mar 25, 2020
            Authors : Valdis Akmens
            Notes   : Remove rounds_history table from "fnc_bo_rounds_history" and check on started_at parition key 
                    (SWS-16933)         
        1.0.6
            Date    : May 15, 2020
            Authors : Valdis Akmens
            Notes   : Based on date interval choose different archive schema
                    (SWDB-138)                    
    Sample run:
    SELECT brand_id,started_at,finished_at FROM fnc_bo_rounds_history(
                                        p_where_filters => '{"brand_id = 51", "started_at >=''2017-12-08 00:00:00''","started_at < ''2017-12-09 00:00:00''" }',
                                        p_sort_by => '{"finished_at DESC"}',
                                        p_limit => 20,
                                        p_offset => 0
                                        );
    SELECT brand_id,started_at,finished_at FROM fnc_bo_rounds_history(
                                        p_where_filters => '{"brand_id = 50", "started_at >=''2017-12-08 00:00:00''","started_at < ''2017-12-09 00:00:00''" }',
                                        p_sort_by => '{"finished_at DESC"}',
                                        p_limit => 20,
                                        p_offset => 0,
                                        p_incl_sub_brands => TRUE,
                                        p_incl_test => FALSE
                                        );
    SELECT * FROM fnc_bo_rounds_history(
                                            p_where_filters => '{"brand_id = 51", "started_at >=''2017-12-08 00:00:00''","started_at < ''2017-12-09 00:00:00''", "finished_at is not null" }'
                                            );
********************************************************************************************************/
DECLARE
    v_filter                VARCHAR;
    v_select                VARCHAR;
    v_select_unf            VARCHAR;
    v_where                 VARCHAR:='WHERE ';
    v_sort_by               VARCHAR:='ORDER BY ';
    v_limit                 VARCHAR:=COALESCE('LIMIT '||p_limit::TEXT,'');
    v_offset                VARCHAR:=COALESCE('OFFSET '||p_offset::TEXT,'');
    v_exec_sql              VARCHAR:='';
    v_line                  VARCHAR;
    v_arch_schema           VARCHAR:='swmanagement_archive';
    v_arch_schema_1         VARCHAR:='swmanagement_archive';
    v_arch_schema_2         VARCHAR:='swmanagement_archive_ro';
    v_arch_schema_int       INTERVAL:= '7 DAYS';
    v_part_key_int          INTERVAL;
    v_rounds_table          VARCHAR:='swmanagement.rounds_finished';
    v_rounds_archive_table  VARCHAR:='rounds_finished';
    v_unfinished_table      VARCHAR:='swmanagement.rounds_unfinished';
    v_old_archive_table     VARCHAR:='rounds_history';
    v_partiton_key          VARCHAR:='finished_at';
    v_partiton_key_filters  VARCHAR[];
    v_is_in_archive         BOOLEAN:=FALSE;
    v_sub_brands            VARCHAR[];
    v_brand_id              VARCHAR:='';
    v_range_min             TIMESTAMP;
    v_range_max             TIMESTAMP;
BEGIN

     /* Check mandatory params */
    IF p_where_filters IS NULL THEN
        RAISE EXCEPTION 'Filter for WHERE must be defined!';
    END IF;

    -- Get column list
    SELECT 'SELECT '||string_agg(attname, ', ' ORDER BY attnum)||' FROM '
    INTO v_select
    FROM pg_catalog.pg_attribute
    WHERE  attrelid = v_rounds_table::regclass
    AND    attnum > 0
    AND    NOT attisdropped;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No column list for %.', v_rounds_table;
    END IF;

    -- Make select list for swmanagement.rounds_unfinished
    v_select_unf:=REPLACE(v_select,'finished_at','NULL::TIMESTAMP AS finished_at');

    --Build ORDER BY
    IF p_sort_by IS NULL OR p_sort_by = '{}'::VARCHAR[] THEN
        v_sort_by:= v_sort_by||v_partiton_key||' DESC';
    ELSE
        FOREACH v_filter IN ARRAY p_sort_by LOOP
            v_sort_by:=v_sort_by||CASE WHEN array_position(p_sort_by, v_filter) <> 1 THEN ' ,' ELSE '' END ||chr(10)||v_filter;
        END LOOP;
    END IF;

    --Build WHERE filter
    FOREACH v_filter IN ARRAY p_where_filters LOOP
        v_where:=v_where||CASE WHEN array_position(p_where_filters, v_filter) <> 1 THEN ' AND' ELSE '' END ||chr(10)||v_filter;
        -- Get datetime values from partition key filters, to match them with pathman configuration tables
        IF  (v_filter ILIKE v_partiton_key||'%' AND v_filter NOT ILIKE '%true%' AND v_filter NOT ILIKE '%false%') THEN
            v_partiton_key_filters:= array_append(v_partiton_key_filters, (SELECT substring(v_filter from '\''.*?\''' ))::VARCHAR);
        END IF;
    END LOOP;

    -- Check if exists partition key filters
    IF v_partiton_key_filters IS NOT NULL THEN

        v_range_max:= (SELECT                     
                            GREATEST(
                                (SELECT MAX(range_max::timestamp)
                                FROM public.pathman_partition_list
                                WHERE parent IN (v_old_archive_table::regclass)
                                ), 
                                (SELECT MAX(range_max::timestamp)
                                FROM public.pathman_partition_list
                                WHERE parent IN (v_rounds_archive_table::regclass)
                                )                         
                        ));
        -- Check if partition filters points to archive table
        IF ( EXISTS(SELECT * FROM unnest( v_partiton_key_filters ) AS part_filter WHERE part_filter.part_filter::timestamp < v_range_max) ) THEN 
            v_is_in_archive:= TRUE;
            v_part_key_int := (SELECT (SELECT MAX(part_filter.part_filter::TIMESTAMP) FROM unnest( v_partiton_key_filters ) AS part_filter) - (SELECT MIN(part_filter.part_filter::TIMESTAMP) FROM unnest( v_partiton_key_filters ) AS part_filter));
            IF v_part_key_int > '00:00:00'::INTERVAL AND  v_part_key_int < v_arch_schema_int THEN 
                v_arch_schema := v_arch_schema_1;
            ELSE 
                v_arch_schema := v_arch_schema_2;
            END IF;
        END IF;
        v_is_in_archive:=COALESCE(v_is_in_archive,FALSE);
    END IF;

    v_rounds_archive_table:= v_arch_schema||'.'||v_rounds_archive_table;
    v_old_archive_table   := v_arch_schema||'.'||v_old_archive_table;
    IF p_incl_sub_brands = FALSE THEN
            -- Build EXEC string based on which tables need to use
            CASE
                -- Add swmanagement_archive.rounds_history table to union
                WHEN v_is_in_archive THEN
                    v_exec_sql:=v_select||' (('||chr(10)||
                    v_select||v_rounds_table||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||') 
                        UNION ALL ( SELECT * FROM ( '||
                    v_select_unf||v_unfinished_table||') AS q '||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                        UNION ALL ('||
                    v_select||v_old_archive_table||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                        UNION ALL ('||
                    v_select||v_rounds_archive_table||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                    ) AS q '||v_sort_by||chr(10)||v_limit||chr(10)||v_offset||';';
                ------------------------------------------------------------------------------------------------------------------------
                ELSE
                    v_exec_sql:=v_select||' (('||chr(10)||
                    v_select||v_rounds_table||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                        UNION ALL ( SELECT * FROM ( '||
                    v_select_unf||v_unfinished_table||') AS q '||chr(10)||v_where||chr(10)||v_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                    ) AS q '||v_sort_by||chr(10)||v_limit||chr(10)||v_offset||';';
            END CASE;
    ELSE
        -- If needed sub-brands get all brand_id in ARRAY
        FOREACH v_filter IN ARRAY p_where_filters LOOP
            IF v_filter ILIKE '%brand_id%' THEN
                    --RAISE INFO '[%]: v_filter : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_filter;
                    FOR v_brand_id IN  EXECUTE 'WITH RECURSIVE hierarchy AS
                                                (
                                                    SELECT brand_id, parent, name, type, 1 AS deep_level, title, key, path, is_test
                                                    FROM   (SELECT id as brand_id, parent, name, type, 1 AS deep_level, title, key, path, is_test FROM entities) AS x
                                                    WHERE  '||v_filter||'
                                                    UNION ALL
                                                    SELECT en.id AS brand_id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.key, en.path, en.is_test
                                                    FROM   entities en
                                                    INNER JOIN hierarchy h ON en.parent = h.brand_id
                                                )
                                                SELECT ''brand_id = ''||brand_id::VARCHAR AS sub_brand_id
                                                FROM   hierarchy
                                                WHERE  is_test = COALESCE(CASE WHEN '||p_incl_test||' <> FALSE THEN NULL ELSE '||p_incl_test||' END, is_test);' LOOP
                        --RAISE INFO '[%]: v_brand_id : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_brand_id;
                        v_sub_brands:= array_append(v_sub_brands, v_brand_id);
                    END LOOP;
            END IF;
        END LOOP;
        -- Call sub-function for brand_id array
        v_exec_sql:=v_select||'( '||chr(10)||
                'SELECT * FROM fnc_bo_rounds_history_inner( '||quote_literal(p_where_filters::TEXT)||', '''||v_sort_by::TEXT||''', '||p_limit::TEXT||', '||p_offset::TEXT||', '''||v_select||''' ,'||v_is_in_archive::TEXT||', '''||v_sub_brands::TEXT||''', TRUE, NULL, '''
                ||v_arch_schema||''')'||chr(10)||
                ') AS x '||v_sort_by||chr(10)||v_limit||chr(10)||v_offset||';';
    END IF;

    --RAISE INFO '[%]: v_exec_sql: %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),chr(10)||v_exec_sql;

    -- Check execution plan
    /*FOR v_line IN EXECUTE 'EXPLAIN ANALYZE '||v_exec_sql LOOP
        RAISE INFO '% ' , v_line;
    END LOOP;*/
    RETURN QUERY
    EXECUTE v_exec_sql;

END;
$function$
;


CREATE OR REPLACE FUNCTION fnc_bo_rounds_history_inner(p_where_filters character varying[], p_sort_by character varying, p_limit integer, p_offset integer, p_select character varying, p_is_in_archive boolean, p_sub_brands character varying[], p_inital_call boolean, p_brand_id character varying, p_arch_schema character varying)
 RETURNS SETOF rounds_finished
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************

    Object Name:   fnc_bo_rounds_history_inner
    Purpose    : Provide read access to rounds_history table and rounds_unfinished and rounds_finished
                If not partition key in WHERE, than only to new partitions. If is partition key, than check pathman_config, and pathman_partition_list to see if we need:
                new table
                old table
                or both tables union
                This function is called from fnc_bo_rounds_history in case when is needed return records for sub-brands
    History    :
        1.0.0(?)
            Date    : Jun 03, 2019
            Authors : Valdis Akmens
            Notes   : Release (SWS-9902)
        1.0.4
            Date    : Oct 14, 2019
            Authors : Valdis Akmens
            Notes   : Add support for "swmanagement_archive.rounds_finished" and remove unfinished rounds view "swmanagement.v_rounds_unfinished"
                    (SWDB-117)
        1.0.5
            Date    : Mar 25, 2020
            Authors : Valdis Akmens
            Notes   : Remove rounds_history table from "fnc_bo_rounds_history"
                    (SWS-16933)   
        1.0.6
            Date    : May 15, 2020
            Authors : Valdis Akmens
            Notes   : Based on date interval choose different archive schema
                    (SWDB-138)     
    Sample run:
    SELECT * FROM fnc_bo_rounds_history_inner(
                                            '{"finished_at >=''2018-05-08 10:00:00''","finished_at < ''2018-05-08 13:00:00''", "brand_id = 51"}','ORDER BY finished_at DESC', 20, 10,'SELECT * FROM ' , FALSE, '{"brand_id = 51", "brand_id = 211"}', TRUE, NULL
                                            );
********************************************************************************************************/
DECLARE
    v_filter                VARCHAR;
    v_select                VARCHAR;
    v_select_unf            VARCHAR;
    v_where                 VARCHAR:='WHERE 1=1 ';
    v_sort_by               VARCHAR:='ORDER BY ';
    v_limit                 VARCHAR:=COALESCE('LIMIT '||p_limit::TEXT,'');
    v_offset                VARCHAR:=COALESCE('OFFSET '||p_offset::TEXT,'');
    v_exec_sql              VARCHAR:='';
    v_line                  VARCHAR;
    v_arch_schema           VARCHAR:='swmanagement_archive';
    v_arch_schema_1         VARCHAR:='swmanagement_archive';
    v_arch_schema_2         VARCHAR:='swmanagement_archive_ro';
    v_arch_schema_int       INTERVAL:= '7 DAYS';
    v_rounds_table          VARCHAR:='swmanagement.rounds_finished';
    v_rounds_archive_table  VARCHAR:='rounds_finished';
    v_unfinished_table      VARCHAR:='swmanagement.rounds_unfinished';
    v_old_archive_table     VARCHAR:='rounds_history';
    v_partiton_key          VARCHAR:='finished_at';
    v_partiton_key_filters  VARCHAR[];
    v_is_in_archive         BOOLEAN:=FALSE;
    v_sub_brand             VARCHAR;
    v_brand_id              VARCHAR:='';
BEGIN

    v_select_unf:=REPLACE(p_select,'finished_at', 'NULL::timestamp AS finished_at');

    IF p_inital_call = TRUE THEN

        FOREACH v_sub_brand IN ARRAY p_sub_brands LOOP
            RETURN QUERY SELECT * FROM fnc_bo_rounds_history_inner(p_where_filters, p_sort_by, p_limit, p_offset, p_select, p_is_in_archive, NULL, FALSE, v_sub_brand, p_arch_schema);
        END LOOP;

        RETURN;

    ELSE
        --Build WHERE filter
        FOREACH v_filter IN ARRAY p_where_filters LOOP
            IF v_filter ILIKE '%brand_id%' THEN
                v_filter:= p_brand_id;
            END IF;
            v_where:=v_where||' AND'||chr(10)||v_filter;
        END LOOP;

        v_rounds_archive_table:= p_arch_schema||'.'||v_rounds_archive_table;
        v_old_archive_table   := p_arch_schema||'.'||v_old_archive_table;
        -- Build EXEC string based on which tables need to use
        CASE
            -- Add swmanagement_archive.rounds_history table to union
            WHEN p_is_in_archive THEN
                v_exec_sql:=p_select||' (('||chr(10)||
                p_select||v_rounds_table||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                ||') 
                    UNION ALL ( SELECT * FROM ( '||
                v_select_unf||v_unfinished_table||') AS q '||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                ||')
                    UNION ALL ('||
                p_select||v_old_archive_table||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                ||')
                    UNION ALL ('||
                p_select||v_rounds_archive_table||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                    ||')
                ) AS q '||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)||';';
            ------------------------------------------------------------------------------------------------------------------------
            ELSE
                v_exec_sql:=p_select||' (('||chr(10)||
                p_select||v_rounds_table||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                ||') 
                    UNION ALL ( SELECT * FROM ( '||
                v_select_unf||v_unfinished_table||') AS q '||chr(10)||v_where||chr(10)||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)
                ||')
                ) AS q '||p_sort_by||chr(10)||' LIMIT '||(p_limit+p_offset)::TEXT||chr(10)||';';
        END CASE;

        --RAISE INFO '[%]: v_exec_sql: %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),chr(10)||v_exec_sql;

        -- Check execution plan
        /*FOR v_line IN EXECUTE 'EXPLAIN ANALYZE '||v_exec_sql LOOP
            RAISE INFO '% ' , v_line;
        END LOOP;*/

        RETURN QUERY
            EXECUTE v_exec_sql;
    END IF;

END;
$function$
;

RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_rounds_history(VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN);
--rollback DROP FUNCTION IF EXISTS fnc_bo_rounds_history_inner(VARCHAR[], VARCHAR, INT, INT, VARCHAR, BOOLEAN, VARCHAR[], BOOLEAN, VARCHAR, VARCHAR);
--rollback ALTER FUNCTION fnc_bo_rounds_history_before_4_37_0 (VARCHAR[], VARCHAR[], INT, INT, BOOLEAN, BOOLEAN) RENAME TO fnc_bo_rounds_history;
--rollback ALTER FUNCTION fnc_bo_rounds_history_inner_before_4_37_0 (VARCHAR[], VARCHAR, INT, INT, VARCHAR, BOOLEAN, VARCHAR[], BOOLEAN, VARCHAR) RENAME TO fnc_bo_rounds_history_inner;
--rollback RESET search_path;

--changeset alexander.guzanov:2020-05-22-SWS-SWS-18706
--comment Keep jackpot transaction duplicates in a separate table
SET search_path = swjackpot;

CREATE TABLE IF NOT EXISTS jp_wallet_operation_log_duplicates(
    pk BIGSERIAL PRIMARY KEY,
    id BIGINT NOT NULL,
    operation_id INTEGER NOT NULL,
    operation_name VARCHAR(255),
    public_id CHAR(28) NOT NULL,
    external_trx_id VARCHAR(255),
    is_external BOOLEAN NOT NULL,
    game_id VARCHAR(255),
    ts TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    version INTEGER NOT NULL,
    data JSONB NOT NULL,
    params JSONB,
    inserted_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now(),
    committed_at TIMESTAMP WITHOUT TIME ZONE
);
COMMENT ON TABLE jp_wallet_operation_log_duplicates IS 'The table contains duplicates, that occurred during unloading process';
ALTER TABLE jp_wallet_operation_log_duplicates OWNER TO swjackpot;

RESET search_path;
--rollback SET search_path = swjackpot;
--rollback DROP TABLE jp_wallet_operation_log_duplicates;
--rollback RESET search_path;
