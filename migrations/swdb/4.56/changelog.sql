--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset sergey.malkov:2020-03-18_SWS-24306_fix_for_theoretical_rtp_history endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Bug fix: Add ability to see history of theoretical RTP changes for entities without records in game_rtp_history
SET search_path TO swmanagement;

ALTER FUNCTION fnc_list_game_rtp_history RENAME TO fnc_list_game_rtp_history_before_4_56;

CREATE OR REPLACE FUNCTION fnc_list_game_rtp_history
(
   p_entity_id     INTEGER
  ,p_game_codes    VARCHAR[]   DEFAULT NULL
  ,p_ts_from       TIMESTAMP   DEFAULT NULL
  ,p_ts_till       TIMESTAMP   DEFAULT NULL
  ,p_limit         INTEGER     DEFAULT 100
  ,p_offset        INTEGER     DEFAULT 0
)
RETURNS TABLE(
   id              INTEGER,
   entity_id       INTEGER,
   game_id         INTEGER,
   rtp_info        JSONB,        -- JSON with rtp info, stored in game features and entity game settings
   rtp_deduction   JSONB,        -- JSON with rtp deduction info, stored in entity game settings
   ts              TIMESTAMP,    -- Time of creation
   game_code       VARCHAR(255),
   entity_title    VARCHAR(255),
   game_title      VARCHAR(255),
   total_rows      BIGINT
)
LANGUAGE plpgsql
AS  $function$
/********************************************************************************************************
   Object Name: fnc_list_game_rtp_history
   Purpose    : List from the table game_rtp_history for given entity_id, list of games and period,
               with merged rtp_deduction from the entity ancestors
   Add records in entity_games base for given entity_id and its childs. Records based on parents entity_games record.
   History    :
      1.0.0
         Date    : Jul 09, 2020
         Authors : Ales
         Notes   : Release (DEVOPS-9481)
      1.0.1 - v.4.47
         Date    : Oct 21, 2020
         Authors : Ales
         Notes   : Added params for Limit, Offset, added 2 columns for titles of entity and game in result (DEVOPS-10766)
      1.0.2 - v.4.50
         Date    : Dec 03, 2020
         Authors : Timur Luchkin
         Notes   : SWS-23026: Change final records order
                   SWS-22952: Return total rows count
      1.0.3 - v.4.56
         Date    : Mar 18, 2021
         Authors : Sergey Malkov
         Notes   : SWS-24306: Ability to see history of theoretical RTP changes for entities without records in game_rtp_history


   Sample run:
      SELECT * FROM swmanagement.fnc_list_game_rtp_history (
                                      p_entity_id => 4837
                                     ,p_game_codes => '{qs_dragonshrine, sw_gs, sw_ksm, sw_jodobi}'::VARCHAR[]
                                     ,p_ts_from => '2020-11-01 15:42'
                                     ,p_ts_till => '2020-12-15 15:42'
                                     ,p_limit => 20
                                     ,p_offset => 0
                                  );
********************************************************************************************************/
BEGIN

    /* Check mandatory params */
    IF (p_entity_id IS NULL) THEN
        RAISE EXCEPTION 'Parameter "p_entity_id" must be defined!';
    END IF;

    IF coalesce(p_limit, -1) < 0 THEN
      RAISE EXCEPTION 'Parameter "p_limit" must be positive!';
    END IF;

    IF coalesce(p_offset, -1) < 0 THEN
      RAISE EXCEPTION 'Parameter "p_offset" must be positive!';
    END IF;

    RETURN QUERY
        WITH RECURSIVE cte_entity_hier_up AS
    ( -- full hierarchy tree to root direction for given entity_id
        SELECT e.id, e.parent, e.name, e.type, 0 AS root_level, e.title, e.path, e.is_test
        FROM swmanagement.entities e
        WHERE e.id = p_entity_id
        UNION ALL
        SELECT e.id, e.parent, e.name, e.type, h.root_level + 1 AS root_level, e.title, e.path, e.is_test
        FROM cte_entity_hier_up h
        INNER JOIN swmanagement.entities e ON e.id = h.parent
    )
    , cte_game_rtp_hist AS
    ( -- history of the given entity, its ancients
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
            e.root_level, e.path
        FROM swmanagement.game_rtp_history h
        INNER JOIN cte_entity_hier_up e ON e.id = h.entity_id
        WHERE (p_game_codes IS NULL OR h.game_code = ANY(p_game_codes))
            AND (p_ts_till IS NULL OR h.ts <= p_ts_till)
    )
    , p_entity_games AS
    ( -- select all entity games for p_entity_id with p_game_codes filter
        SELECT eg.game_id FROM swmanagement.entity_games eg
        INNER JOIN swmanagement.games g ON g.id = eg.game_id
        WHERE eg.entity_id = p_entity_id AND (p_game_codes IS NULL OR g.code = ANY(p_game_codes))
    )
    , cte_game_rtp_hist_full AS
    ( -- add history of games which have links to the main entity
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
            root_level
        FROM cte_game_rtp_hist h
        UNION ALL
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
            999 AS root_level
        FROM swmanagement.game_rtp_history h
        WHERE h.entity_id IS NULL
            AND h.game_id IN (SELECT peg.game_id FROM p_entity_games peg)
            AND (p_ts_till IS NULL OR h.ts <= p_ts_till)
    )
    , cte_rtp_deduction_values AS
    ( -- all rtp_deduction key-value pairs */
        SELECT h.id, h.entity_id, h.game_id, h.ts,
            root_level,
            (jsonb_each(h.rtp_deduction))."key" AS rtp_key,
            (jsonb_each(h.rtp_deduction)).value AS rtp_val
        FROM cte_game_rtp_hist h
    )
    , cte_game_rtp_hist_ext AS
    (
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
                v.rtp_key,
                FIRST_VALUE(v.rtp_val) OVER (
                        PARTITION BY h.entity_id, h.game_id, h.ts, v.rtp_key
                        ORDER BY v.root_level, v.ts DESC
                ) AS rtp_val
        FROM cte_game_rtp_hist_full h
        LEFT JOIN cte_rtp_deduction_values v
            ON v.id = h.id OR (v.game_id = h.game_id AND v.ts <= h.ts AND v.root_level < h.root_level)
    )
    , cte_game_rtp_hist_processed AS
    (
        SELECT h.id, h.entity_id, h.game_id, h.rtp_info,
            CASE WHEN max(rtp_key) IS NULL
                THEN '{}'::jsonb
                ELSE jsonb_object_agg(COALESCE (rtp_key, '-'), rtp_val )
            END AS rtp_deduction,
            h.ts, h.game_code
        FROM cte_game_rtp_hist_ext h
        WHERE (p_ts_from IS NULL OR h.ts >= p_ts_from)
        GROUP BY h.id, h.entity_id, h.game_id, h.rtp_info, h.ts, h.game_code
    )
    SELECT h.id, h.entity_id, h.game_id, h.rtp_info, h.rtp_deduction, h.ts, h.game_code,
           e.title AS entity_title, g.title AS game_title,
           Cast(Sum(1) OVER () AS BIGINT) as total_rows
    FROM   cte_game_rtp_hist_processed h
           LEFT JOIN swmanagement.entities e   ON e.id = h.entity_id
           LEFT JOIN swmanagement.games g      ON g.id = h.game_id
    ORDER BY h.ts DESC
    LIMIT p_limit OFFSET p_offset;

END $function$;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback     DROP FUNCTION fnc_list_game_rtp_history
--rollback     (
--rollback        p_entity_id     INTEGER
--rollback       ,p_game_codes    VARCHAR[]
--rollback       ,p_ts_from       TIMESTAMP
--rollback       ,p_ts_till       TIMESTAMP
--rollback       ,p_limit         INTEGER
--rollback       ,p_offset        INTEGER
--rollback     );
--rollback     ALTER FUNCTION fnc_list_game_rtp_history_before_4_56 RENAME TO fnc_list_game_rtp_history;
--rollback RESET search_path;

--changeset sergey.malkov:2020-03-25_SWS-24306_remove_useless_data
--comment Remove previously saved useless data without rtp information
CREATE TABLE IF NOT EXISTS swbackup.game_rtp_history_archive (
    id INTEGER NOT NULL PRIMARY KEY,
    entity_id INTEGER,
    game_id INTEGER,
    rtp_info JSONB NOT NULL,
    rtp_deduction JSONB,
    ts TIMESTAMP NOT NULL,
    game_code VARCHAR(255) NOT NULL);
INSERT INTO swbackup.game_rtp_history_archive(id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code) SELECT id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code FROM swmanagement.game_rtp_history;
TRUNCATE TABLE swmanagement.game_rtp_history;
WITH game_hist_sub AS (DELETE FROM swbackup.game_rtp_history_archive WHERE entity_id IS null AND rtp_info <> '{}' RETURNING id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code)
    INSERT INTO swmanagement.game_rtp_history(id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code) SELECT  id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code FROM game_hist_sub;
WITH entity_game_hist_sub AS (DELETE FROM swbackup.game_rtp_history_archive WHERE entity_id IS NOT null AND rtp_deduction IS NOT null RETURNING id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code)
    INSERT INTO swmanagement.game_rtp_history(id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code) SELECT  id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code FROM  entity_game_hist_sub;
--rollback INSERT INTO swmanagement.game_rtp_history(id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code) SELECT id, entity_id, game_id, rtp_info, rtp_deduction, ts, game_code FROM swbackup.game_rtp_history_archive;
--rollback DROP TABLE swbackup.game_rtp_history_archive;


--changeset nikita.senko:2021-04-06-SWS-25942-add-new-permissions
--comment Create new permission for the Prize Drops BI report
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'bi:report:prize-drops' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:prize-drops' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:bi:report:prize-drops", "bi:report:prize-drops"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:prize-drops' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:prize-drops' WHERE id = 1;
--rollback RESET search_path;


