--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset andrei.stefan:2023-03-14-SWS-39718-slotmasters-tournament-history
--comment Add new column 'recovery_type' to  'ext_bet_win_history' table
SET search_path = swadaptergos;

ALTER TABLE ext_bet_win_history ADD COLUMN recovery_type swmanagement.enum_recovery_type;
COMMENT ON COLUMN ext_bet_win_history.recovery_type IS 'The recovery type if the round was recovered by game provider';

RESET search_path;

--rollback SET search_path = swadaptergos;
--rollback ALTER TABLE ext_bet_win_history DROP COLUMN recovery_type;
--rollback RESET search_path;

--changeset andrei.stefan:2023-03-27-SWS-39718-slotmasters-tournament-history
--comment Move 'recovery_type' enum to swadaptergos schema
SET search_path = swadaptergos;

DROP TYPE IF EXISTS enum_ext_bet_win_history_recovery_type;

CREATE TYPE enum_ext_bet_win_history_recovery_type AS ENUM (
    'force-finish',
    'revert',
    'finalize'
);

ALTER TABLE ext_bet_win_history DROP COLUMN IF EXISTS recovery_type;
ALTER TABLE ext_bet_win_history ADD COLUMN recovery_type enum_ext_bet_win_history_recovery_type;
COMMENT ON COLUMN ext_bet_win_history.recovery_type IS 'The recovery type if the round was recovered by game provider';

RESET search_path;

--rollback SET search_path = swadaptergos;
--rollback ALTER TABLE ext_bet_win_history DROP COLUMN recovery_type;
--rollback DROP TYPE enum_ext_bet_win_history_recovery_type;
--rollback RESET search_path;
