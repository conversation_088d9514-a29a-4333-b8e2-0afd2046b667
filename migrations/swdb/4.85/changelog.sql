--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset mikhail.ivanov:2022-05-12_SWS-34438-Implement-fetching-parent-jackpotIds-for-GET-jackpots endDelimiter:# stripComments:false
--comment Simplify function for getting all jackpots and jackpot's data for single entity
SET search_path = swmanagement;

CREATE OR REPLACE FUNCTION fnc_entity_jackpots_simplified(p_entity_id INTEGER)
 RETURNS TABLE(game_code CHARACTER VARYING, jp_type CHARACTER VARYING, jp_id CHARACTER VARYING)
 LANGUAGE plpgsql SECURITY DEFINER SET search_path = swmanagement
AS $function$

/********************************************************************************************************
    Object Name: fnc_entity_jackpots_simplified
    Purpose    : List of jackpot codes, types and codes of games for given entity_id
                 That's copy of fnc_entity_jackpots function written by Sergey Malkov within SWS-33437,
                 but simplified by removing two heavy selects (all_others_games_with_jackpot and others_jackpots)
                 and some unnecessary fields for SWS-34357 purposes (Greece JPN API).
    History    :
        1.0.0
            Date    : May 12, 2022
            Authors : Mikhail Ivanov
            Notes   : Release (SWS-34438)
    Sample run:
        SELECT * FROM fnc_entity_jackpots_simplified(81);

********************************************************************************************************/
DECLARE
BEGIN

     /* Check mandatory params */
    IF p_entity_id IS NULL THEN
        RAISE EXCEPTION 'Parameters must be defined!';
    END IF;

    RETURN QUERY WITH RECURSIVE

        all_brands_hier AS (
                SELECT ent.id, ent.parent, 0 AS deep_level FROM entities ent
                    WHERE ent.id = p_entity_id
                UNION SELECT ent.id, ent.parent, hier.deep_level + 1 as deep_level FROM entities ent
                    JOIN all_brands_hier hier ON hier.parent = ent.id
        ),

        all_hierarchy_games AS (SELECT all_brands_hier.id AS entity_id, deep_level, game_id, eg.settings
                FROM entity_games eg JOIN all_brands_hier ON eg.entity_id = all_brands_hier.id),

        -- all brand's games with jackpot
        all_jp_eg AS (SELECT entity_id, eg.game_id, settings->'jackpotId' as jackpots, deep_level
            FROM all_hierarchy_games eg
            WHERE settings IS NOT NULL AND settings <> '{}'
                AND settings-> 'jackpotId' <> '{}'
                AND game_id IN (SELECT game_id FROM all_hierarchy_games WHERE deep_level = 0)),

        -- unnested brand's games with jackpots
        brand_jp_eg_plain AS (SELECT entity_id, game_id, key::CHARACTER VARYING AS jp_type,
            value::CHARACTER VARYING AS jp_id, deep_level FROM all_jp_eg, JSONB_EACH_TEXT(all_jp_eg.jackpots)),

        -- game uses jackpot from nearest parent or from brand configuration
        nearest_parent_entity AS (SELECT game_id, brand_jp_eg_plain.jp_type, MIN(deep_level) AS min_deep_level
            FROM brand_jp_eg_plain GROUP by game_id, brand_jp_eg_plain.jp_type),

        -- get effective game configuration with min deep level
        applied_configuration AS (SELECT brand_jp_eg_plain.entity_id AS configured_on_entity_id,
            brand_jp_eg_plain.game_id, brand_jp_eg_plain.jp_type, brand_jp_eg_plain.jp_id
        FROM nearest_parent_entity JOIN brand_jp_eg_plain
            ON brand_jp_eg_plain.deep_level = nearest_parent_entity.min_deep_level
                AND brand_jp_eg_plain.jp_type = nearest_parent_entity.jp_type
                AND brand_jp_eg_plain.game_id = nearest_parent_entity.game_id)

        SELECT code, applied_configuration.jp_type, applied_configuration.jp_id
        FROM applied_configuration
            JOIN games ON applied_configuration.game_id = games.id
        ORDER BY code, jp_id;
END $function$
;

RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_entity_jackpots_simplified(INTEGER);
--rollback RESET search_path;

--changeset mikhail.ivanov:2022-05-25-SWS-34357-implement-jpn-api-for-greece
--comment add new permission for view own jackpots
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:report:jackpot:instances' where id = 1;
UPDATE roles SET permissions = permissions - 'report:jackpot:instances' where id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:report:jackpot:instances", "report:jackpot:instances"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:report:jackpot:instances' where id = 1;
--rollback UPDATE roles SET permissions = permissions - 'report:jackpot:instances' where id = 1;
--rollback RESET search_path;
