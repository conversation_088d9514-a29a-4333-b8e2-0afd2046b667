--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset dmitriy.palaznik:2024-06-12-SWS-44881-pariplay-round-history-is-incorrect
--comment Add a new column 'state' for bet_win_history table
SET search_path TO swmanagement;

ALTER TABLE bet_win_history ADD COLUMN state CHARACTER VARYING(32);
COMMENT ON COLUMN bet_win_history.state IS 'State of bet win history';

RESET search_path;

--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE bet_win_history DROP COLUMN state;
--rollback RESET search_path;