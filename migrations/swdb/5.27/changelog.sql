--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset emanuel-alin.raileanu:2024-03-21-SWS-38272 endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Support ignoring jackpot type validation
SET search_path = swmanagement;

ALTER FUNCTION fnc_validate_entity_game_jp_settings RENAME TO fnc_validate_entity_game_jp_settings_before_5_27;
ALTER FUNCTION fnc_validate_entity_game_jp_settings_before_5_27 SET SCHEMA swbackup;

CREATE OR REPLACE FUNCTION fnc_validate_entity_game_jp_settings(p_entity_id INTEGER, p_game_code CHARACTER varying, OUT validation_result BOOLEAN) returns BOOLEAN
    LANGUAGE plpgsql
AS
$$
/********************************************************************************************************
    Object Name: fnc_validate_entity_game_jp_settings
    Purpose    : Validate jackpot settings of parent entity_games. All jackpot types in game.features should be defined at least in one of parent entity_games
    History    :
    1.0.0
        Date         : Dec 16, 2019
        Last Updated : Mar 21, 2024
        Authors      : Sergey Malkov, Emanuel-Alin Raileanu
        Notes        : Release (DEVOPS-7322)
    Example ('sw_ps')
            game.features = {"jackpotTypes":["sw-grand-pot-shot","sw-super-shot"]}
            valid parent  entity_game.settings = {"jackpotId":{"sw-super-shot":"SW-SUPER-SHOT_AUTO","sw-grand-pot-shot":"SW-GRAND-POT-SHOT_AUTO"}}
            invalid parent  entity_game.settings = null or entity_game.settings = {"jackpotId":{"sw-super-shot":"SW-SUPER-SHOT_AUTO"}}

    Sample run(CD2):
    SELECT * FROM fnc_validate_entity_game_jp_settings( p_entity_id => 98302,
                                        p_game_code => 'sw_ps'
                                        );
********************************************************************************************************/
DECLARE
BEGIN
     /* Check mandatory params */
    IF p_entity_id IS NULL OR p_game_code IS NULL OR p_game_code ='' THEN
        RAISE EXCEPTION 'Parameters must be defined!';
    END IF;

    WITH RECURSIVE
    game_sub AS (SELECT games.id, features FROM games JOIN game_providers AS gapr ON provider_id = gapr.id WHERE games.code = p_game_code AND features IS NOT NULL AND games.status = 'available' AND gapr.status = 'normal'),
    ent_game_id AS (SELECT enga.id AS parent_eg_id FROM entities AS ent JOIN entity_games AS enga ON enga.entity_id = ent.parent  JOIN game_sub  AS game ON game.id = enga.game_id WHERE ent.id = p_entity_id),
    enga_hier_up
        AS
        (
            SELECT id, parent_entity_game_id, settings, 0 AS deep_level
            FROM entity_games
            WHERE id = (SELECT parent_eg_id FROM ent_game_id)
            UNION ALL
            SELECT enga.id, enga.parent_entity_game_id, enga.settings,  h.deep_level + 1 AS deep_level
            FROM enga_hier_up AS h
            INNER JOIN entity_games AS enga ON enga.id = h.parent_entity_game_id
    ),
    hier_settings AS
    (
        SELECT (settings->> 'jackpotId')::jsonb AS jp_settings from enga_hier_up WHERE settings->> 'jackpotId' IS NOT null
    ),
    jackpot_types_from_features AS (SELECT jsonb_array_elements_text((features->>'jackpotTypes')::jsonb) AS jp_types_from_features FROM game_sub),
    existing_jp_types_from_settings AS (SELECT DISTINCT(jp_types_from_features) FROM  jackpot_types_from_features JOIN hier_settings AS s ON jp_settings->jp_types_from_features IS NOT NULL),
    ignore_jackpot_types_validation AS (SELECT features->>'ignoreJackpotTypesValidation' = TRUE FROM game_sub)
    SELECT ignore_jackpot_types_validation = TRUE OR COUNT(*) = (SELECT COUNT(*) FROM existing_jp_types_from_settings) INTO validation_result FROM jackpot_types_from_features;

    RETURN;
END;
$$;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_validate_entity_game_jp_settings;
--rollback ALTER FUNCTION swbackup.fnc_validate_entity_game_jp_settings_before_5_27 SET SCHEMA swmanagement;
--rollback ALTER FUNCTION fnc_validate_entity_game_jp_settings_before_5_27 RENAME TO fnc_validate_entity_game_jp_settings;
--rollback RESET search_path;

--changeset emanuel-alin.raileanu:2024-04-03-SWS-38272 endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Fix function behaviour when ignoreJackpotTypesValidation is not defined
SET search_path = swmanagement;

ALTER FUNCTION fnc_validate_entity_game_jp_settings RENAME TO fnc_validate_entity_game_jp_settings_before_5_27_error;
ALTER FUNCTION fnc_validate_entity_game_jp_settings_before_5_27_error SET SCHEMA swbackup;

CREATE OR REPLACE FUNCTION fnc_validate_entity_game_jp_settings(p_entity_id INTEGER, p_game_code CHARACTER varying, OUT validation_result BOOLEAN) returns BOOLEAN
    LANGUAGE plpgsql
AS
$$
/********************************************************************************************************
    Object Name: fnc_validate_entity_game_jp_settings
    Purpose    : Validate jackpot settings of parent entity_games. All jackpot types in game.features should be defined at least in one of parent entity_games
    History    :
    1.0.0
        Date         : Dec 16, 2019
        Last Updated : Apr 03, 2024
        Authors      : Sergey Malkov, Emanuel-Alin Raileanu
        Notes        : Release (DEVOPS-7322)
    Example ('sw_ps')
            game.features = {"jackpotTypes":["sw-grand-pot-shot","sw-super-shot"]}
            valid parent  entity_game.settings = {"jackpotId":{"sw-super-shot":"SW-SUPER-SHOT_AUTO","sw-grand-pot-shot":"SW-GRAND-POT-SHOT_AUTO"}}
            invalid parent  entity_game.settings = null or entity_game.settings = {"jackpotId":{"sw-super-shot":"SW-SUPER-SHOT_AUTO"}}

    Sample run(CD2):
    SELECT * FROM fnc_validate_entity_game_jp_settings( p_entity_id => 98302,
                                        p_game_code => 'sw_ps'
                                        );
********************************************************************************************************/
DECLARE
BEGIN
     /* Check mandatory params */
    IF p_entity_id IS NULL OR p_game_code IS NULL OR p_game_code ='' THEN
        RAISE EXCEPTION 'Parameters must be defined!';
    END IF;

    WITH RECURSIVE
    game_sub AS (SELECT games.id, features FROM games JOIN game_providers AS gapr ON provider_id = gapr.id WHERE games.code = p_game_code AND features IS NOT NULL AND games.status = 'available' AND gapr.status = 'normal'),
    ent_game_id AS (SELECT enga.id AS parent_eg_id FROM entities AS ent JOIN entity_games AS enga ON enga.entity_id = ent.parent  JOIN game_sub  AS game ON game.id = enga.game_id WHERE ent.id = p_entity_id),
    enga_hier_up
        AS
        (
            SELECT id, parent_entity_game_id, settings, 0 AS deep_level
            FROM entity_games
            WHERE id = (SELECT parent_eg_id FROM ent_game_id)
            UNION ALL
            SELECT enga.id, enga.parent_entity_game_id, enga.settings,  h.deep_level + 1 AS deep_level
            FROM enga_hier_up AS h
            INNER JOIN entity_games AS enga ON enga.id = h.parent_entity_game_id
    ),
    hier_settings AS
    (
        SELECT (settings->> 'jackpotId')::jsonb AS jp_settings from enga_hier_up WHERE settings->> 'jackpotId' IS NOT null
    ),
    jackpot_types_from_features AS (SELECT jsonb_array_elements_text((features->>'jackpotTypes')::jsonb) AS jp_types_from_features FROM game_sub),
    existing_jp_types_from_settings AS (SELECT DISTINCT(jp_types_from_features) FROM  jackpot_types_from_features JOIN hier_settings AS s ON jp_settings->jp_types_from_features IS NOT NULL),
    ignore_jackpot_types_validation AS (SELECT (features->>'ignoreJackpotTypesValidation')::boolean = TRUE FROM game_sub)
    SELECT ignore_jackpot_types_validation = TRUE OR COUNT(*) = (SELECT COUNT(*) FROM existing_jp_types_from_settings) INTO validation_result FROM jackpot_types_from_features;

    RETURN;
END;
$$;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_validate_entity_game_jp_settings;
--rollback ALTER FUNCTION swbackup.fnc_validate_entity_game_jp_settings_before_5_27_error SET SCHEMA swmanagement;
--rollback ALTER FUNCTION fnc_validate_entity_game_jp_settings_before_5_27_error RENAME TO fnc_validate_entity_game_jp_settings;
--rollback RESET search_path;

--changeset emanuel-alin.raileanu:2024-04-03-SWS-38272-2 endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Fix missing column error
SET search_path = swmanagement;

ALTER FUNCTION fnc_validate_entity_game_jp_settings RENAME TO fnc_validate_entity_game_jp_settings_before_5_27_error_2;
ALTER FUNCTION fnc_validate_entity_game_jp_settings_before_5_27_error_2 SET SCHEMA swbackup;

CREATE OR REPLACE FUNCTION fnc_validate_entity_game_jp_settings(p_entity_id INTEGER, p_game_code CHARACTER varying, OUT validation_result BOOLEAN) returns BOOLEAN
    LANGUAGE plpgsql
AS
$$
/********************************************************************************************************
    Object Name: fnc_validate_entity_game_jp_settings
    Purpose    : Validate jackpot settings of parent entity_games. All jackpot types in game.features should be defined at least in one of parent entity_games
    History    :
        1.0.0
            Date         : Dec 16, 2019
            Authors      : Sergey Malkov
            Notes        : Release (DEVOPS-7322)

        1.0.1
            Date         : Mar 21, 2024
            Authors      : Emanuel-Alin Raileanu
            Notes        : Ignore jackpot types validation (SWS-38272)

        1.0.2
            Date         : Apr 03, 2024
            Authors      : Emanuel-Alin Raileanu
            Notes        : Fix query issue related to skipping jackpot types validation (SWS-38272)

    Example ('sw_ps')
            game.features = {"jackpotTypes":["sw-grand-pot-shot","sw-super-shot"]}
            valid parent  entity_game.settings = {"jackpotId":{"sw-super-shot":"SW-SUPER-SHOT_AUTO","sw-grand-pot-shot":"SW-GRAND-POT-SHOT_AUTO"}}
            invalid parent  entity_game.settings = null or entity_game.settings = {"jackpotId":{"sw-super-shot":"SW-SUPER-SHOT_AUTO"}}

    Sample run(CD2):
    SELECT * FROM fnc_validate_entity_game_jp_settings( p_entity_id => 98302,
                                        p_game_code => 'sw_ps'
                                        );
********************************************************************************************************/
DECLARE
BEGIN
     /* Check mandatory params */
    IF p_entity_id IS NULL OR p_game_code IS NULL OR p_game_code ='' THEN
        RAISE EXCEPTION 'Parameters must be defined!';
    END IF;

    WITH RECURSIVE
    game_sub AS (SELECT games.id, features FROM games JOIN game_providers AS gapr ON provider_id = gapr.id WHERE games.code = p_game_code AND features IS NOT NULL AND games.status = 'available' AND gapr.status = 'normal'),
    ent_game_id AS (SELECT enga.id AS parent_eg_id FROM entities AS ent JOIN entity_games AS enga ON enga.entity_id = ent.parent  JOIN game_sub  AS game ON game.id = enga.game_id WHERE ent.id = p_entity_id),
    enga_hier_up
        AS
        (
            SELECT id, parent_entity_game_id, settings, 0 AS deep_level
            FROM entity_games
            WHERE id = (SELECT parent_eg_id FROM ent_game_id)
            UNION ALL
            SELECT enga.id, enga.parent_entity_game_id, enga.settings,  h.deep_level + 1 AS deep_level
            FROM enga_hier_up AS h
            INNER JOIN entity_games AS enga ON enga.id = h.parent_entity_game_id
    ),
    hier_settings AS
    (
        SELECT (settings->> 'jackpotId')::jsonb AS jp_settings from enga_hier_up WHERE settings->> 'jackpotId' IS NOT null
    ),
    jackpot_types_from_features AS (SELECT jsonb_array_elements_text((features->>'jackpotTypes')::jsonb) AS jp_types_from_features FROM game_sub),
    existing_jp_types_from_settings AS (SELECT DISTINCT(jp_types_from_features) FROM  jackpot_types_from_features JOIN hier_settings AS s ON jp_settings->jp_types_from_features IS NOT NULL)
    SELECT (SELECT (features->>'ignoreJackpotTypesValidation')::boolean IS TRUE FROM game_sub) = TRUE OR COUNT(*) = (SELECT COUNT(*) FROM existing_jp_types_from_settings) INTO validation_result FROM jackpot_types_from_features;

    RETURN;
END;
$$;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_validate_entity_game_jp_settings;
--rollback ALTER FUNCTION swbackup.fnc_validate_entity_game_jp_settings_before_5_27_error_2 SET SCHEMA swmanagement;
--rollback ALTER FUNCTION fnc_validate_entity_game_jp_settings_before_5_27_error_2 RENAME TO fnc_validate_entity_game_jp_settings;
--rollback RESET search_path;
