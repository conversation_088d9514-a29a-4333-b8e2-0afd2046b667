--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset nikita.senko:2020-07-14-SWS-XXXX-start-release-4.42.0
--comment label for 4.42.0
select now();
--rollback select now();

--changeset nikita.senko:2020-07-14-SWS-18422-remove-royalties
--comment remove royalties column from entity_games table
SET search_path = swmanagement;
ALTER TABLE entity_games DROP royalties;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entity_games ADD royalties numeric;
--rollback RESET search_path;

--changeset stepanov.alekey:2020-07-20-SWS-18429
--comment Create API for unblocking player
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:player-extra:login-unlock' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'player-extra:login-unlock' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:player-extra:login-unlock", "player-extra:login-unlock"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'player-extra:login-unlock' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:player-extra:login-unlock' WHERE id = 1;
--rollback RESET search_path;

--changeset nikita.senko:2020-07-21-SWS-15488-add-type-to-deployment-groups
--comment add type column in deployment groups
SET search_path = swmanagement;
CREATE TYPE enum_deployment_groups_type AS ENUM ('game', 'entity');
ALTER TABLE deployment_groups ADD group_type enum_deployment_groups_type;
UPDATE deployment_groups u SET group_type = 'game';
UPDATE deployment_groups u SET group_type = 'entity' WHERE EXISTS (SELECT NULL FROM entities e WHERE e.deployment_group_id = u.id );
ALTER TABLE deployment_groups ALTER group_type SET NOT NULL;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE deployment_groups DROP group_type;
--rollback DROP TYPE enum_deployment_groups_type;
--rollback RESET search_path;

--changeset senko.nikita:2020-07-28-SWS-18422
--comment del permissions from admin role and del royalities permissions from table
SET search_path = swmanagement;
DELETE FROM permissions WHERE code='royalties';
DELETE FROM permissions WHERE code='royalties:create';
DELETE FROM permissions WHERE code='royalties:delete';
DELETE FROM permissions WHERE code='royalties:edit';
DELETE FROM permissions WHERE code='royalties:view';
DELETE FROM permissions WHERE code='keyentity:royalties';
DELETE FROM permissions WHERE code='keyentity:royalties:create';
DELETE FROM permissions WHERE code='keyentity:royalties:delete';
DELETE FROM permissions WHERE code='keyentity:royalties:edit';
DELETE FROM permissions WHERE code='keyentity:royalties:view';
UPDATE roles SET permissions = permissions - 'royalties';
UPDATE roles SET permissions = permissions - 'royalties:create';
UPDATE roles SET permissions = permissions - 'royalties:delete';
UPDATE roles SET permissions = permissions - 'royalties:edit';
UPDATE roles SET permissions = permissions - 'royalties:view';
UPDATE roles SET permissions = permissions - 'keyentity:royalties';
UPDATE roles SET permissions = permissions - 'keyentity:royalties:create';
UPDATE roles SET permissions = permissions - 'keyentity:royalties:delete';
UPDATE roles SET permissions = permissions - 'keyentity:royalties:edit';
UPDATE roles SET permissions = permissions - 'keyentity:royalties:view';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('royalties', 'Manage entity royalties', NOW(), NOW());
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('royalties:create', 'Add entity royalties', NOW(), NOW());
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('royalties:delete', 'Remove entity royalties', NOW(), NOW());
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('royalties:edit', 'Edit entity royalties', NOW(), NOW());
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('royalties:view', 'View entity royalties', NOW(), NOW());
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('keyentity:royalties', 'Manage keyEntity royalties', NOW(), NOW());
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('keyentity:royalties:create', 'Add keyEntity royalties', NOW(), NOW());
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('keyentity:royalties:delete', 'Remove keyEntity royalties', NOW(), NOW());
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('keyentity:royalties:edit', 'Edit keyEntity royalties', NOW(), NOW());
--rollback INSERT INTO permissions (code, description, created_at, updated_at) VALUES ('keyentity:royalties:view', 'View keyEntity royalties', NOW(), NOW());
--rollback UPDATE roles SET permissions = permissions || '["royalties", "royalties:create", "royalties:delete", "royalties:edit", "royalties:view", "keyentity:royalties", "keyentity:royalties:create", "keyentity:royalties:delete", "keyentity:royalties:edit", "keyentity:royalties:view"]'::jsonb WHERE id = 1;
--rollback RESET search_path;


--changeset senko.nikita:2020-07-30-SWS-20609
--comment Create new permissions
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:bo:report:special:esl:campaign' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'bo:report:special:esl:campaign' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:bo:report:special:esl:campaign", "bo:report:special:esl:campaign"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bo:report:special:esl:campaign' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'bo:report:special:esl:campaign' WHERE id = 1;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2020-07-31_SWS-20673_remove_deleted_col_from_func endDelimiter:# stripComments:false
--comment SWS-20673 Removed mentions of deleted column "royalties" of the table "entity_games" in the function
SET search_path TO swmanagement;

ALTER FUNCTION fnc_add_entity_games(int4, varchar[]) RENAME TO fnc_add_entity_games_before_4_42_0;

CREATE OR REPLACE FUNCTION fnc_add_entity_games(p_entity_id integer, p_game_codes character varying[], OUT po_games_added integer)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name: fnc_add_entity_games
    Purpose    : Add records in entity_games base for given entity_id and its childs. Records based on parents entity_games record.
    History    :
        1.0.0
            Date    : Dec 16, 2019
            Authors : Valdis Akmens
            Notes   : Release (DEVOPS-7322)
        1.0.2
            Date    : Jul 31, 2020
            Authors : Ales
            Notes   : Removed column "royalties" (SWS-20673)
    Sample run:
    SELECT * FROM fnc_add_entity_games( p_entity_id => 256,
                                        p_game_codes => '{sw_ps, sw_mr, sw_gs}'::VARCHAR[]
                                        );
********************************************************************************************************/
DECLARE
    v_code  VARCHAR;
BEGIN

     /* Check mandatory params */
    IF p_entity_id IS NULL OR p_game_codes IS NULL OR p_game_codes ='{}' THEN
        RAISE EXCEPTION 'Parameters must be defined!';
    END IF;

    FOREACH v_code IN ARRAY p_game_codes LOOP
            IF fnc_validate_entity_game_jp_settings(p_entity_id, v_code) = FALSE THEN
                RAISE EXCEPTION 'Jackpot settings is not valid for game %', v_code;
            END IF;
    END LOOP;

    -- Get full hierarchy tree for entity UP and DOWN direction for given entity_id
    WITH RECURSIVE cte_hier_down
    AS
    (
        SELECT id, parent, name, type,  0 AS deep_level, title,  path, is_test
        FROM entities
        WHERE id = p_entity_id
        UNION ALL
        SELECT en.id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.path, en.is_test
        FROM entities AS en
        INNER JOIN cte_hier_down AS h ON en.parent = h.id
    ),
    -- Get parent of p_entity_id entity_game record which will be used for new records
    cte_entity_game AS (
        SELECT enga.*
        FROM entity_games      AS enga
        JOIN games             AS game ON enga.game_id = game.id
        JOIN game_providers    AS gapr ON game.provider_id = gapr.id
        JOIN cte_hier_down     AS cthi ON enga.entity_id = cthi.parent
        WHERE
            game.status = 'available'
        AND gapr.status = 'normal'
        AND game.code = ANY(p_game_codes)
        AND cthi.deep_level = 0
    ), cte_ins AS (
        INSERT INTO entity_games (entity_id, game_id, created_at, updated_at, parent_entity_game_id, settings, status, limit_filters, url_params)
        SELECT cthi.id AS entity_id, cega.game_id, NOW(), NOW(), cega.id AS parent_entity_game_id, NULL AS settings, cega.status, cega.limit_filters, NULL AS url_params
        FROM cte_hier_down AS cthi
        CROSS JOIN cte_entity_game AS cega
        WHERE cthi.deep_level >=0
        AND NOT EXISTS(SELECT id FROM entity_games AS x WHERE cthi.id = x.entity_id AND cega.game_id = x.game_id)
        RETURNING *
    )
    SELECT COUNT(*)
    FROM cte_ins
    INTO po_games_added
    ;

    RETURN;
END;
$function$
;

-- Permissions
ALTER FUNCTION fnc_add_entity_games(int4, varchar[]) OWNER TO swmanagement;

RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_add_entity_games(int4, varchar[]);
--rollback ALTER FUNCTION fnc_add_entity_games_before_4_42_0 (int4, varchar[]) RENAME TO fnc_add_entity_games;
--rollback RESET search_path;

--changeset senko.nikita:2020-08-03-SWS-18594-new-table-fpr-integration-tests
--comment create new table for integration tests reports
SET search_path = swmanagement;
CREATE TYPE enum_integration_test_reports_status AS ENUM ('success', 'failure', 'in progress');
CREATE TABLE IF NOT EXISTS integration_test_reports (
  id int NOT NULL PRIMARY KEY,
  game_code varchar(255),
  merch_type varchar(255),
  merch_code varchar(255),
  report jsonb,
  report_status enum_integration_test_reports_status,
  updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
  created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL
);
COMMENT ON TABLE integration_test_reports IS 'Table for storing integration tests report';
COMMENT ON COLUMN integration_test_reports.id IS 'id';
COMMENT ON COLUMN integration_test_reports.game_code IS 'game code';
COMMENT ON COLUMN integration_test_reports.merch_code IS 'merch code';
COMMENT ON COLUMN integration_test_reports.report IS 'report in json format';
COMMENT ON COLUMN integration_test_reports.report_status IS 'report status';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP TABLE integration_test_reports;
--rollback DROP TYPE enum_integration_test_reports_status;
--rollback RESET search_path;

--changeset nikita.senko:2020-08-04-SWS-18422-rollback
--comment add column
SET search_path = swmanagement;
ALTER TABLE entity_games ADD royalties numeric;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entity_games DROP royalties;
--rollback RESET search_path;

--changeset senko.nikita:2020-08-03-SWS-18594-new-table-fpr-integration-tests_fix
--comment create new table for integration tests reports
SET search_path = swmanagement;
DROP TABLE IF EXISTS integration_test_reports;
CREATE TABLE IF NOT EXISTS integration_test_reports (
  id BIGSERIAL NOT NULL PRIMARY KEY,
  game_code varchar(255),
  merch_type varchar(255),
  merch_code varchar(255),
  report jsonb,
  report_status enum_integration_test_reports_status,
  updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now()
);
COMMENT ON TABLE integration_test_reports IS 'Table for storing integration tests report';
COMMENT ON COLUMN integration_test_reports.id IS 'id';
COMMENT ON COLUMN integration_test_reports.game_code IS 'game code';
COMMENT ON COLUMN integration_test_reports.merch_code IS 'merch code';
COMMENT ON COLUMN integration_test_reports.report IS 'report in json format';
COMMENT ON COLUMN integration_test_reports.report_status IS 'report status';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP TABLE IF EXISTS integration_test_reports;
--rollback RESET search_path;

--changeset aleksey.ignatenko::2020-08-13-SWS-18594-new-table-fpr-integration-tests_rights
--comment Changed owner of new table for integration tests reports
SET search_path = swmanagement;
ALTER TABLE integration_test_reports OWNER TO swmanagement;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE integration_test_reports OWNER TO swsystem;
--rollback RESET search_path;

--changeset egor.morozov:2020-08-14-SWEHS-899-unable-to-disable-jackpot
--comment Set phantom jackpot to be able to disable
SET search_path = swjackpot;
UPDATE jp_type SET can_be_disabled = true WHERE jp_game_id IN ('ph-must-win-multi-type');
RESET search_path;
--rollback SET search_path = swjackpot;
--rollback UPDATE jp_type SET can_be_disabled = false WHERE jp_game_id IN ('ph-must-win-multi-type');
--rollback RESET search_path;