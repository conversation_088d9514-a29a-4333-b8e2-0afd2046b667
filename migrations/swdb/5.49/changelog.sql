--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2025-03-11-SWS-48639-sw-seamless-add-merchant-params-to-merchant-types
--comment Add merchant params to merchant types
SET search_path = swmanagement;
UPDATE merchant_types SET schema = schema || '{ "shortCurrencyEnabled": { "type": "boolean", "title": "MERCHANT.PARAMETERS.shortCurrencyEnabled" }, "currencies": { "type": "textarea", "title": "MERCHANT.PARAMETERS.currencies" }, "playMoneyCurrencies": { "type": "textarea", "title": "MERCHANT.PARAMETERS.playMoneyCurrencies" } }'::jsonb WHERE merchant_types.type = 'seamless';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE merchant_types SET schema = schema - 'shortCurrencyEnabled' - 'currencies' - 'playMoneyCurrencies' WHERE merchant_types.type = 'seamless';
--rollback RESET search_path;


--changeset vladimir.minakov:2025-03-21-SWS-48857-sw-seamless-rollback-add-merchant-params-to-merchant-types
--comment Rollback Add merchant params to merchant types
SET search_path = swmanagement;
UPDATE merchant_types SET schema = schema - 'shortCurrencyEnabled' - 'currencies' - 'playMoneyCurrencies' WHERE merchant_types.type = 'seamless';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback SELECT now();
--rollback RESET search_path;
