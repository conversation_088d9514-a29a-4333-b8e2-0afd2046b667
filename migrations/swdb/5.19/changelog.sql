--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset andrei.stefan:2023-11-22-SWS-43376
--comment add new column 'wallet_transaction_id' in ext_bet_win_history_duplicates table
SET search_path = swadaptergos;

ALTER TABLE ext_bet_win_history_duplicates ADD COLUMN wallet_transaction_id VARCHAR(255);
COMMENT ON COLUMN ext_bet_win_history_duplicates.wallet_transaction_id IS 'Internal transaction id';

RESET search_path;

--rollback SET search_path = swadaptergos;
--rollback ALTER TABLE ext_bet_win_history_duplicates DROP COLUMN wallet_transaction_id;
--rollback RESET search_path;
