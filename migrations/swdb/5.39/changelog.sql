--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset stepanov.aleksey:2024-09-11-SWS-45863
--comment Add permissions for changing nickname APIs
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:player:change-nickname' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'player:change-nickname' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:player:change-nickname", "player:change-nickname"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:player:change-nickname' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'player:change-nickname' WHERE id = 1;
--rollback RESET search_path;
