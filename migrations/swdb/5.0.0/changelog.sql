--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset mihai.constantinescu:2023-02-03-SWS-39510-new_report_permission
--comment Add 'Golden Matrix by country' as a new report permission
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'bi:report:golden-matrix-by-country' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:golden-matrix-by-country' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:bi:report:golden-matrix-by-country", "bi:report:golden-matrix-by-country"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:golden-matrix-by-country' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:golden-matrix-by-country' WHERE id = 1;
--rollback RESET search_path;
