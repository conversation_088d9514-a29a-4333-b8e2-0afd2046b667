--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset emanuel-alin.raileanu:2024-11-04-SWS-46465
--comment Add static domain pool tables, new column and permissions

SET search_path = swmanagement;

CREATE TABLE static_domain_pools (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    created_at timestamp without time zone DEFAULT NOW(),
    updated_at timestamp without time zone DEFAULT NOW()
);
COMMENT ON TABLE static_domain_pools IS 'Static domain pools entities';
COMMENT ON COLUMN static_domain_pools.name IS 'Static domain pool name';
COMMENT ON COLUMN static_domain_pools.created_at IS 'Creation timestamp';
COMMENT ON COLUMN static_domain_pools.updated_at IS 'Last update timestamp';

CREATE TABLE static_domain_pools_static_domains (
    static_domain_pool_id INT NOT NULL REFERENCES static_domain_pools(id) ON DELETE CASCADE,
    static_domain_id INT NOT NULL REFERENCES static_domains(id) ON DELETE CASCADE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    PRIMARY KEY (static_domain_pool_id, static_domain_id)
);
COMMENT ON TABLE static_domain_pools_static_domains IS 'Connection table between static domain pools and static domains';
COMMENT ON COLUMN static_domain_pools_static_domains.static_domain_pool_id IS 'Static domain pool ID';
COMMENT ON COLUMN static_domain_pools_static_domains.static_domain_id IS 'Static domain ID';
COMMENT ON COLUMN static_domain_pools_static_domains.is_active IS 'Flag that indicates whether the static domain is active and can be used';

CREATE INDEX idx_static_domain_pools_static_domains ON static_domain_pools_static_domains USING btree (static_domain_id);

ALTER TABLE entities ADD COLUMN static_domain_pool_id INTEGER REFERENCES static_domain_pools(id) ON DELETE CASCADE;
COMMENT ON COLUMN entities.static_domain_pool_id IS 'Static domain pool id';

UPDATE roles SET permissions = permissions - 'domain-pool' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'domain-pool:static' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'domain-pool:static:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'domain-pool:static:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'domain-pool:static:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'domain-pool:static:remove' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["domain-pool", "domain-pool:static", "domain-pool:static:view", "domain-pool:static:create", "domain-pool:static:edit", "domain-pool:static:remove"]'::jsonb WHERE id = 1;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool:static:remove' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool:static:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool:static:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool:static:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool:static' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool' WHERE id = 1;
--rollback ALTER TABLE entities DROP COLUMN static_domain_pool_id;
--rollback DROP TABLE static_domain_pools_static_domains;
--rollback DROP TABLE static_domain_pools;
--rollback RESET search_path;
