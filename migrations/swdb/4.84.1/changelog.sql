--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset pavel.shamshurov:2022-04-27-SWS-34180-modify-physical-table
--comment Remove physical table fkey from games, add index for physical table id in games
SET search_path = swmanagement;
ALTER TABLE games DROP CONSTRAINT games_physical_table_id_fkey;
CREATE INDEX IF NOT EXISTS idx_games_physical_table_id ON games(physical_table_id);
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP INDEX IF EXISTS idx_games_physical_table_id;
--rollback ALTER TABLE ONLY games ADD CONSTRAINT games_physical_table_id_fkey FOREIGN KEY (physical_table_id) REFERENCES pht_physical_tables(id) ON UPDATE CASCADE ON DELETE SET NULL;
--rollback RESET search_path;


--changeset timur.luchkin:2022-05-11-SWDB-238-hashid_support_part1
--comment Add support for hashID extension (1)
SET search_path = swsystem;

   CREATE TABLE IF NOT EXISTS swsystem.sw_hashid_secret (
    hash_project  VARCHAR(20) NOT NULL PRIMARY KEY
   ,hash_salt     TEXT NOT NULL
   ,hash_length   INTEGER NOT NULL
   );
   COMMENT ON TABLE swsystem.sw_hashid_secret IS 'System table for internal purposes';

   REVOKE ALL ON swsystem.sw_hashid_secret FROM public;
   INSERT INTO swsystem.sw_hashid_secret VALUES ('sw-falcon','fake_key_here.replace_me', 8) ON CONFLICT DO NOTHING;

RESET search_path;
--rollback SELECT now();


--changeset timur.luchkin:2022-05-11-SWDB-238-hashid_support_part2 endDelimiter:# stripComments:false
--comment Add support for hashID extension (2)
SET search_path = swsystem;

   CREATE OR REPLACE FUNCTION swsystem.get_sw_hashid (IN p_hash_project VARCHAR, OUT po_hash_salt TEXT, OUT po_hash_length INTEGER)
   AS
   $BODY$
   BEGIN
      SELECT hash_salt, hash_length FROM swsystem.sw_hashid_secret WHERE hash_project = p_hash_project INTO po_hash_salt, po_hash_length;
   END;
   $BODY$
   LANGUAGE plpgsql STABLE PARALLEL RESTRICTED SECURITY DEFINER;

   REVOKE ALL ON
   FUNCTION swsystem.get_sw_hashid (IN p_hash_project VARCHAR, OUT po_hash_salt TEXT, OUT po_hash_length INTEGER)
   FROM public;

   CREATE OR REPLACE FUNCTION public.sw_get_public_id (IN internal_id BIGINT, OUT public_id TEXT)
   AS
   $BODY$
   BEGIN
      SELECT public.id_encode(internal_id, po_hash_salt, po_hash_length)
      FROM   swsystem.get_sw_hashid ('sw-falcon') INTO public_id;
   END;
   $BODY$
   LANGUAGE plpgsql STABLE PARALLEL RESTRICTED SECURITY DEFINER RETURNS NULL ON NULL INPUT;

   GRANT EXECUTE ON
   FUNCTION public.sw_get_public_id (IN internal_id BIGINT, OUT public_id TEXT)
   TO public;

   CREATE OR REPLACE FUNCTION public.sw_get_internal_id (IN public_id TEXT, OUT internal_id BIGINT)
   AS
   $BODY$
   BEGIN
      SELECT (public.id_decode(public_id, po_hash_salt, po_hash_length))[1]
      FROM   swsystem.get_sw_hashid ('sw-falcon') INTO internal_id;
   END;
   $BODY$
   LANGUAGE plpgsql STABLE PARALLEL RESTRICTED SECURITY DEFINER RETURNS NULL ON NULL INPUT;

   GRANT EXECUTE ON
   FUNCTION public.sw_get_internal_id (IN public_id TEXT, OUT internal_id BIGINT)
   TO public;

RESET search_path;
--rollback SELECT now();


--changeset pavel.shamshurov:2022-04-28-SWS-34117-update-live-games-with-physical-table-id endDelimiter:# stripComments:false
--comment Update live games with physical table id by concrete game codes
SET search_path = swmanagement;

ALTER TABLE games ALTER COLUMN physical_table_id TYPE bigint;
CREATE TABLE IF NOT EXISTS swbackup.games_220428_sws34117 AS SELECT * FROM games WHERE code = ANY(
                                  '{sw_ro_a01bac,sw_ro_a01bac_nc,sw_ro_a02bac,sw_ro_a02bac_nc,sw_ro_a03bac,sw_ro_a03bac_nc,'
                                  'sw_ro_a04bac,sw_ro_a04bac_nc,sw_ro_a05bac,sw_ro_a05bac_nc,sw_ro_a06bac,sw_ro_a06bac_nc,'
                                  'sw_ro_a07bac,sw_ro_a07bac_nc,sw_ro_a01ro,sw_ro_a02ro,sw_ro_a03ro,sw_live_erol_atom,'
                                  'sw_ro_a01bj,sw_ro_a02bj,sw_ro_a03bj,sw_ro_a04bj,sw_ro_a05bj,sw_ro_esl_htbac01,'
                                  'sw_ro_esl_htbac01_nc,sw_ro_esl_htro01}');
UPDATE games SET physical_table_id = public.sw_get_internal_id(features::json -> 'live' ->> 'tableId') WHERE code IN (SELECT code FROM swbackup.games_220428_sws34117);

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE games g SET physical_table_id = bg.physical_table_id FROM (SELECT physical_table_id, id FROM swbackup.games_220428_sws34117) AS bg WHERE bg.id = g.id;
--rollback DROP TABLE IF EXISTS swbackup.games_220428_sws34117;
--rollback RESET search_path;
