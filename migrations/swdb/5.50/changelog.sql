--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset cosmin.cosman:2025-02-27-SWS-47889-email-field-optional
--comment Make email field be optional
SET search_path = swmanagement;
ALTER TABLE users ALTER COLUMN email DROP NOT NULL;
DROP INDEX IF EXISTS idx_users_entity_id_email_key_and_deleted_at_is_null;
CREATE UNIQUE INDEX idx_users_entity_id_email_key_and_deleted_at_is_null ON swmanagement.users USING btree (entity_id, email) WHERE (deleted_at IS NULL AND email IS NOT NULL);
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE users ALTER COLUMN email SET NOT NULL;
--rollback DROP INDEX IF EXISTS idx_users_entity_id_email_key_and_deleted_at_is_null;
--rollback CREATE UNIQUE INDEX idx_users_entity_id_email_key_and_deleted_at_is_null ON swmanagement.users USING btree (entity_id, email) WHERE (deleted_at IS NULL);
--rollback RESET search_path;

--changeset emanuel-alin.raileanu:2025-04-01-SWS-47711-store-b365-free-spin-tokens
--comment Add the free_spin_token column to the b365 rounds table
SET search_path = swadapterb365;
ALTER TABLE rounds ADD COLUMN free_spin_token VARCHAR(100);
RESET search_path;

--rollback SET search_path = swadapterb365;
--rollback ALTER TABLE rounds DROP COLUMN free_spin_token;
--rollback RESET search_path;

--changeset valdis.akmens:2025-04-21-optimize-fnc_upd_promotion_players endDelimiter:# stripComments:false
--comment Optimize fnc_upd_promotion_players for large player count
SET search_path TO swmanagement;

CREATE INDEX IF NOT EXISTS idx_promotion_players_update_inserted_at ON promotion_players_update(inserted_at);

ALTER FUNCTION fnc_upd_promotion_players() RENAME TO fnc_upd_promotion_players_1_0_0;

CREATE OR REPLACE FUNCTION fnc_upd_promotion_players()
RETURNS TABLE(log_time timestamp without time zone, log_msg text)
LANGUAGE plpgsql
AS $function$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

    Object Name:   fnc_upd_promotion_players
    Purpose    :   Update promotion_players from promotion_players_update table.
                    1. select record from promotion_players_update
                    2. select original record from promotion_players matching by (player_code, promotion_id)
                    3. if promotion_players_update.inserted_at > promotion_players.updated_at
                    3.1 if promotion_players_update.status not null, update promotion_players.status with this value
                    3.2 if promotion_players_update.finish_status not null, update promotion_players.finish_status with this value
                    3.3 if promotion_players_update.played_at not null, update promotion_players.played_at with this value
                    3.4 if promotion_players_update.finished_at not null, update promotion_players.finished_at with this value
                    4. remove record from promotion_players_update
    History    :
        1.0.0
            Date    : Mar 02, 2020
            Authors : Valdis Akmens
            Notes   : Initial release (DEVOPS-8226)
        1.1.0
            Date    : Apr 21, 2025
            Authors : Valdis Akmens
            Notes   : Optimize function

    Sample run:
        SELECT * FROM fnc_upd_promotion_players();
~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
    v_rec           RECORD;
    v_counter       INT:=0;
BEGIN
    log_time := clock_timestamp(); log_msg := 'INFO: Start update promotion_players '; RETURN NEXT;

    /* Check if MDB */
    IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
    END IF;

    WITH cte AS (
    SELECT *
    FROM promotion_players_update
    ORDER BY inserted_at ASC LIMIT 1000000
    ), cte_upd AS (
        UPDATE promotion_players AS pp SET
            status          = COALESCE(c.status,pp.status),
            finish_status   = COALESCE(c.finish_status,pp.finish_status),
            played_at       = COALESCE(c.played_at, pp.played_at),
            finished_at     = COALESCE(c.finished_at, pp.finished_at)
        FROM cte AS c
        WHERE     pp.player_code = c.player_code
                AND pp.promotion_id = c.promotion_id
                AND c.inserted_at > pp.updated_at
    ), cte_del AS (
    DELETE FROM promotion_players_update AS ppu
    WHERE ppu.id IN (SELECT id FROM cte)
    )
    SELECT COUNT(*)
    INTO v_counter
    FROM cte
    ;

    log_time := clock_timestamp(); log_msg := 'INFO: Finish update promotion_players. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

    RETURN;
END;
$function$;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP INDEX IF EXISTS idx_promotion_players_update_inserted_at;
--rollback DROP FUNCTION IF EXISTS fnc_upd_promotion_players();
--rollback ALTER FUNCTION fnc_upd_promotion_players_1_0_0() RENAME TO fnc_upd_promotion_players;
--rollback RESET search_path;
