--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement, public;
--example: CREATE TABLE tmp(id int);
--example: --rollback DROP TABLE tmp;


--changeset andrey.shmigiro:2019-11-21-SWS-XXXX-start-release-4.28.0
--comment label for 4.28.0
select now();
--rollback select now();


--changeset dmitriy.palaznik:2019-12-12-SWS-13588-stars-automated-checksum-service
--comment Renamed table to use it as a general storage of all stars jobs
ALTER TABLE swadapterstars.jp_feeder_job RENAME TO jobs;
ALTER TABLE swadapterstars.jobs RENAME CONSTRAINT jp_feeder_job_pkey TO jobs_pkey;
INSERT INTO swadapterstars.jobs (id) VALUES (2);
--rollback DELETE FROM swadapterstars.jobs WHERE id = 2;
--rollback ALTER TABLE swadapterstars.jobs RENAME TO jp_feeder_job;
--rollback ALTER TABLE swadapterstars.jp_feeder_job RENAME CONSTRAINT jobs_pkey TO jp_feeder_job_pkey;


--changeset pavel.shamshurov:2019-12-04-SWS-14703-add-constraint-on-delete
--comment Add constraint 'on delete cascade' to game_group_limits
ALTER TABLE swmanagement.game_group_limits
DROP CONSTRAINT game_group_limits_entity_game_id_fkey,
ADD CONSTRAINT game_group_limits_entity_game_id_fkey
  FOREIGN KEY (entity_game_id)
  REFERENCES swmanagement.entity_games(id)
  ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE swmanagement.segments
DROP CONSTRAINT segments_entity_game_id_fkey,
ADD CONSTRAINT segments_entity_game_id_fkey
  FOREIGN KEY (entity_game_id)
  REFERENCES swmanagement.entity_games(id)
  ON UPDATE CASCADE ON DELETE CASCADE;
--rollback ALTER TABLE swmanagement.game_group_limits DROP CONSTRAINT game_group_limits_entity_game_id_fkey, ADD CONSTRAINT game_group_limits_entity_game_id_fkey FOREIGN KEY (entity_game_id) REFERENCES swmanagement.entity_games(id) ON UPDATE CASCADE;
--rollback ALTER TABLE swmanagement.segments DROP CONSTRAINT segments_entity_game_id_fkey, ADD CONSTRAINT segments_entity_game_id_fkey FOREIGN KEY (entity_game_id) REFERENCES swmanagement.entity_games(id) ON UPDATE CASCADE;


--changeset vera.kruhliakova:2019-12-06-SWS-13992-payment-decline-reason
--comment Add reason of declined payment
CREATE TYPE swmanagement.enum_payments_decline_reason AS ENUM ('transactionNotFound', 'insufficientEntityBalance', 'insufficientBalance', 'badTransactionId', 'internalError');
ALTER TABLE swmanagement.payments ADD COLUMN decline_reason swmanagement.enum_payments_decline_reason NULL;
COMMENT ON COLUMN swmanagement.payments.decline_reason IS 'The reason why payment was declined';
--rollback ALTER TABLE swmanagement.payments DROP COLUMN decline_reason; DROP TYPE IF EXISTS swmanagement.enum_payments_decline_reason;


--changeset stepan.shklyanko:2019-12-04-SWS-12618-gamecategories-per-lobby
--comment Add new table for lobby menu items
CREATE SEQUENCE IF NOT EXISTS swmanagement.lobby_menu_items_id_seq;
ALTER TABLE swmanagement.lobby_menu_items_id_seq OWNER TO swmanagement;
CREATE TABLE IF NOT EXISTS swmanagement.lobby_menu_items
(
    id integer DEFAULT nextval('swmanagement.lobby_menu_items_id_seq') PRIMARY KEY,
    title varchar(100),
    lobby_id integer NOT NULL,
    game_category_id integer NOT NULL,
    CONSTRAINT lobby_menu_items_lobby_id_fkey FOREIGN KEY (lobby_id)
        REFERENCES swmanagement.lobbies (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE,

    CONSTRAINT lobby_menu_items_game_category_id_fkey FOREIGN KEY (game_category_id)
        REFERENCES swmanagement.game_categories (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE
);
ALTER TABLE swmanagement.lobby_menu_items OWNER TO swmanagement;

CREATE INDEX IF NOT EXISTS idx_lobby_menu_items_lobby_id ON swmanagement.lobby_menu_items USING btree (lobby_id);
CREATE INDEX IF NOT EXISTS idx_lobby_menu_items_game_category_id ON swmanagement.lobby_menu_items USING btree (game_category_id);
COMMENT ON TABLE swmanagement.lobby_menu_items IS 'Table for storing lobby menu items';
COMMENT ON COLUMN swmanagement.lobby_menu_items.lobby_id IS 'Lobby that this menu item belongs to';
COMMENT ON COLUMN swmanagement.lobby_menu_items.game_category_id IS 'Game category that this menu item belongs to';
--rollback DROP TABLE swmanagement.lobby_menu_items;
--rollback DROP SEQUENCE IF EXISTS swmanagement.lobby_menu_items_id_seq;


--changeset egor.morozov:2019-12-18 SWS-14863-Support-game-client-URLs-for-deployment-groups
--comment Add new table to link deployment group, game code and game client version
ALTER TABLE swmanagement.games ADD COLUMN default_client_version VARCHAR(255);
CREATE TABLE IF NOT EXISTS swmanagement.game_versions
(
  id SERIAL NOT NULL CONSTRAINT game_versions_pkey PRIMARY KEY,
  deployment_group_id integer NOT NULL CONSTRAINT game_versions_deployment_group_id_fkey REFERENCES swmanagement.deployment_groups ON UPDATE NO ACTION ON DELETE CASCADE,
  game_code VARCHAR(255) CONSTRAINT game_versions_game_code_fkey REFERENCES swmanagement.games (code) ON UPDATE NO ACTION on DELETE CASCADE,
  client_version VARCHAR(255) NOT NULL
);
ALTER TABLE swmanagement.game_versions OWNER TO swmanagement;

CREATE UNIQUE INDEX IF NOT EXISTS idx_game_versions_deployment_group_id_game_code ON swmanagement.game_versions USING btree (deployment_group_id, game_code);
CREATE INDEX IF NOT EXISTS idx_game_versions_game_code ON swmanagement.game_versions USING btree (game_code);

COMMENT ON TABLE swmanagement.game_versions IS 'Table for storing game client versions by deployment group + game code';
COMMENT ON COLUMN swmanagement.game_versions.id IS 'Autoincrement unique Id';
COMMENT ON COLUMN swmanagement.game_versions.deployment_group_id IS 'Id of deployment group from deployment_groups table';
COMMENT ON COLUMN swmanagement.game_versions.game_code IS 'game code from games table';
COMMENT ON COLUMN swmanagement.game_versions.client_version IS 'Overided game client version for game by deployment group';
--rollback ALTER TABLE swmanagement.games DROP COLUMN default_client_version;
--rollback DROP TABLE swmanagement.game_versions;
--rollback DROP INDEX IF EXISTS swmanagement.idx_game_versions_deployment_group_id_game_code;
--rollback DROP INDEX IF EXISTS swmanagement.idx_game_versions_game_code;


--changeset vera.kruhliakova:2019-11-26-SWS-13956-jp-can-be-disabled
--comment Mark jackpot if it can be disabled
ALTER TABLE swjackpot.jp_type ADD COLUMN can_be_disabled BOOLEAN NOT NULL DEFAULT false;
COMMENT ON COLUMN swjackpot.jp_type.can_be_disabled IS 'Indicates if jackpot instance of the type can be disabled/enabled';
UPDATE swjackpot.jp_type SET can_be_disabled = true WHERE jp_game_id IN ('sw-must-no-limit', 'sw-must-win-amount');
--rollback ALTER TABLE swjackpot.jp_type DROP COLUMN can_be_disabled;


--changeset vera.kruhliakova:2019-12-27-SWS-13956-must-win
--comment Mark must win jackpot can be disabled
UPDATE swjackpot.jp_type SET can_be_disabled = true WHERE jp_game_id IN ('sw-must-win-size');
--rollback UPDATE swjackpot.jp_type SET can_be_disabled = false WHERE jp_game_id IN ('sw-must-win-size');


--changeset vera.kruhliakova:2019-11-29-SWS-14991-mark-game-context
--comment Mark game context if it requires completion after migration
ALTER TABLE swgameserver.game_contexts ADD COLUMN is_require_completion BOOLEAN NULL;
COMMENT ON COLUMN swgameserver.game_contexts.is_require_completion IS 'Indicates if game context require completion after migration';
CREATE INDEX IF NOT EXISTS idx_game_contexts_is_require_completion ON swgameserver.game_contexts USING btree (is_require_completion) WHERE is_require_completion IS NOT NULL;
--rollback ALTER TABLE swgameserver.game_contexts DROP COLUMN is_require_completion;
--rollback DROP INDEX IF EXISTS swgameserver.idx_game_contexts_is_require_completion;
