--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset mikhail.ivanov:2022-04-06-SWS-33638-restricted-countries-solution-tab-in-ubo
--comment Add new permission to master
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'restricted-countries-solution' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["restricted-countries-solution"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'restricted-countries-solution' WHERE id = 1;
--rollback RESET search_path;

--changeset sergey.malkov:2022-04-07-SWS-33437-Create-new-report-entity-s-jackpots endDelimiter:# stripComments:false
--comment Get all jackpots and jackpot's data for single entity
SET search_path = swmanagement;

CREATE OR REPLACE FUNCTION fnc_entity_jackpots(p_entity_id INTEGER)
 RETURNS TABLE(game_title CHARACTER VARYING, game_code CHARACTER VARYING, jp_type CHARACTER VARYING,
    jp_id CHARACTER VARYING, jp_is_inherited BOOLEAN, configured_on CHARACTER VARYING, is_global BOOLEAN,
    is_owned BOOLEAN, is_local BOOLEAN, is_used_by_others BOOLEAN)
 LANGUAGE plpgsql SECURITY DEFINER SET search_path = swmanagement
AS $function$

/********************************************************************************************************
    Object Name: fnc_entity_jackpots
    Purpose    : List of jackpots for given entity_id, jackpot's flags and configuration details
    History    :
        1.0.0
            Date    : Arp 07, 2022
            Authors : Sergey Malkov
            Notes   : Release (SWS-32014, SWS-33437, DEVOPS-18728)
    Sample run:
        SELECT * FROM fnc_entity_jackpots(81);

********************************************************************************************************/
DECLARE
BEGIN

     /* Check mandatory params */
    IF p_entity_id IS NULL THEN
        RAISE EXCEPTION 'Parameters must be defined!';
    END IF;

    RETURN QUERY WITH RECURSIVE

        all_brands_hier AS (
                SELECT ent.id, ent.parent, 0 AS deep_level FROM entities ent
                    WHERE ent.id = p_entity_id
                UNION SELECT ent.id, ent.parent, hier.deep_level + 1 as deep_level FROM entities ent
                    JOIN all_brands_hier hier ON hier.parent = ent.id
        ),

        all_hierarchy_games AS (SELECT all_brands_hier.id AS entity_id, deep_level, eg.id AS eg_id, game_id, eg.settings
                FROM entity_games eg JOIN all_brands_hier ON eg.entity_id = all_brands_hier.id),

        -- all brand's games with jackpot
        all_jp_eg AS (SELECT eg_id, entity_id, eg.game_id, settings->'jackpotId' as jackpots, deep_level
            FROM all_hierarchy_games eg
            WHERE settings IS NOT NULL AND settings <> '{}'
                AND settings-> 'jackpotId' <> '{}'
                AND game_id IN (SELECT game_id FROM all_hierarchy_games WHERE deep_level = 0)),

        -- unnested brand's games with jackpots
        brand_jp_eg_plain AS (SELECT eg_id, entity_id, game_id, key::CHARACTER VARYING AS jp_type,
            value::CHARACTER VARYING AS jp_id, deep_level  FROM all_jp_eg, JSONB_EACH_TEXT(all_jp_eg.jackpots)),

        -- game uses jackpot from nearest parent or from brand configuration
        nearest_parent_entity AS (SELECT game_id, brand_jp_eg_plain.jp_type, MIN(deep_level) AS min_deep_level
            FROM brand_jp_eg_plain GROUP by game_id, brand_jp_eg_plain.jp_type),

        -- get effective game configuration with min deep level
        applied_configuration AS (SELECT brand_jp_eg_plain.entity_id AS configured_on_entity_id,
            brand_jp_eg_plain.game_id, brand_jp_eg_plain.jp_type, brand_jp_eg_plain.jp_id
                FROM nearest_parent_entity JOIN brand_jp_eg_plain
                    ON brand_jp_eg_plain.deep_level = nearest_parent_entity.min_deep_level
                        AND brand_jp_eg_plain.jp_type = nearest_parent_entity.jp_type
                        AND brand_jp_eg_plain.game_id = nearest_parent_entity.game_id),

        -- we need to get field is_used_by_others, fetch all entity games not related to brand
        all_others_games_with_jackpot AS (SELECT settings->'jackpotId' as jackpots FROM entity_games
            WHERE  entity_id <> p_entity_id
                AND settings IS NOT NULL AND settings <> '{}' AND settings-> 'jackpotId' <> '{}'),

        -- unique jackpot ids of other entities
        others_jackpots AS (SELECT DISTINCT value::CHARACTER VARYING AS jp_id FROM all_others_games_with_jackpot, JSONB_EACH_TEXT(jackpots))

        SELECT games.title, code, applied_configuration.jp_type, applied_configuration.jp_id,
            configured_on_entity_id <> p_entity_id AS jp_is_inherited, path AS configured_on, jp_instance.is_global,
            jp_instance.is_owned, jp_instance.is_local, applied_configuration.jp_id IN (SELECT j.jp_id FROM others_jackpots j)
        FROM applied_configuration
           JOIN games ON applied_configuration.game_id = games.id
           JOIN entities ON entities.id = configured_on_entity_id
           LEFT JOIN swjackpot.jp_instance ON pid = applied_configuration.jp_id
        ORDER BY configured_on DESC, code, jp_id;
END $function$
;

RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_entity_jackpots(INTEGER);
--rollback RESET search_path;

--changeset sergey.malkov:2022-04-11-SWS-33437-Create-new-report-entity-s-jackpots-permissions
--comment Add jackpot report permissions to superadmin role
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:jp-config-report' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'jp-config-report' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:jp-config-report", "jp-config-report"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:jp-config-report' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'jp-config-report' WHERE id = 1;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2022-04-08_SWS-33895_fix_rights_to_swjackpot_archive_ro_schema
--comment Grant missed access of swjackpot to the schema swjackpot_archive_ro 
GRANT USAGE ON SCHEMA swjackpot_archive_ro TO swjackpot;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA swjackpot_archive_ro TO swjackpot;
ALTER DEFAULT PRIVILEGES IN SCHEMA swjackpot_archive_ro GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO swjackpot;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA swjackpot_archive_ro TO swjackpot;
ALTER DEFAULT PRIVILEGES IN SCHEMA swjackpot_archive_ro GRANT USAGE, SELECT ON SEQUENCES TO swjackpot;

--rollback SELECT now();

