--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset timur.luchkin:2022-06-14-DEVOPS-19694 endDelimiter:# stripComments:false
--comment Fix SecondarySnapshot issue
SET search_path = swsystem;

   CREATE OR REPLACE FUNCTION swsystem.get_sw_hashid (IN p_hash_project VARCHAR, OUT po_hash_salt TEXT, OUT po_hash_length INTEGER)
   AS
   $BODY$
   BEGIN
      SELECT hash_salt, hash_length FROM swsystem.sw_hashid_secret WHERE hash_project = p_hash_project INTO po_hash_salt, po_hash_length;
   END;
   $BODY$
   LANGUAGE plpgsql STABLE PARALLEL UNSAFE SECURITY DEFINER;

   CREATE OR REPLACE FUNCTION public.sw_get_public_id (IN internal_id BIGINT, OUT public_id TEXT)
   AS
   $BODY$
   BEGIN
      SELECT public.id_encode(internal_id, po_hash_salt, po_hash_length)
      FROM   swsystem.get_sw_hashid ('sw-falcon') INTO public_id;
   END;
   $BODY$
   LANGUAGE plpgsql STABLE PARALLEL UNSAFE SECURITY DEFINER RETURNS NULL ON NULL INPUT;

   CREATE OR REPLACE FUNCTION public.sw_get_internal_id (IN public_id TEXT, OUT internal_id BIGINT)
   AS
   $BODY$
   BEGIN
      SELECT (public.id_decode(public_id, po_hash_salt, po_hash_length))[1]
      FROM   swsystem.get_sw_hashid ('sw-falcon') INTO internal_id;
   END;
   $BODY$
   LANGUAGE plpgsql STABLE PARALLEL UNSAFE SECURITY DEFINER RETURNS NULL ON NULL INPUT;

RESET search_path;
--rollback SELECT now();

--changeset emanuel-alin.railean:2022-06-24_SWS-35185-add-is_round_closed-to-ext_bet_win_history
--comment Add is_round_closed to ext_bet_win_history
SET search_path = swadaptergos;
ALTER TABLE ext_bet_win_history ADD IF NOT EXISTS is_round_closed BOOLEAN;
COMMENT ON COLUMN ext_bet_win_history.is_round_closed IS 'Determines whether the round is marked as closed by the game provider';
ALTER TABLE ext_bet_win_history ALTER COLUMN is_round_closed SET DEFAULT false;
RESET search_path;
--rollback SET search_path = swadaptergos;
--rollback ALTER TABLE ext_bet_win_history DROP IF EXISTS is_round_closed;
--rollback RESET search_path;

--changeset kirill.kaminskiy:2022-06-29_SWS-35189-implement-force-finish-for-external-provider
--comment Add new role to force finish for external provider
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:external-game-provider:gameclose:forcefinish' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:external-game-provider:gameclose:forcefinish"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE ROLES SET permissions = permissions - 'keyentity:external-game-provider:gameclose:forcefinish' WHERE id = 1;
--rollback RESET search_path;

--changeset  emanuel-alin.railean:2022-06-30_SWS-35269-add-event_id-to-ext_bet_win_history
--comment Add event_id to ext_bet_win_history
SET search_path = swadaptergos;
ALTER TABLE ext_bet_win_history ADD IF NOT EXISTS event_id INTEGER;
COMMENT ON COLUMN ext_bet_win_history.event_id IS 'Represent an internal rounds counter (starts from 0) for a number of occurred "spins".';
ALTER TABLE ext_bet_win_history ALTER COLUMN event_id SET DEFAULT 0;
RESET search_path;
--rollback SET search_path = swadaptergos;
--rollback ALTER TABLE ext_bet_win_history DROP IF EXISTS event_id;
--rollback RESET search_path;

--changeset kirill.kaminskiy:2022-06-30_SWS-35189-implement-force-finish-for-external-provider
--comment Add new role to force finish for external provider for entities
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'entity:external-game-provider:gameclose:forcefinish' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["entity:external-game-provider:gameclose:forcefinish"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE ROLES SET permissions = permissions - 'entity:external-game-provider:gameclose:forcefinish' WHERE id = 1;
--rollback RESET search_path;
