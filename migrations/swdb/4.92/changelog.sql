--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset kirill.kaminskiy:2022-08-11-SWS-36113-remove-redundant-permissions
--comment Remove permissions for superadmin role
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'hub:engagement:tournaments' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'hub:engagement:prize-drops' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'hub:engagement:must-win-jackpots' WHERE id = 1;
RESET search_path;
--rollback SELECT NOW();


--changeset stepan.shklianko:2022-08-19-SWS-36042-Request-to-create-a-permission
--comment Add new permissions to allow forced entitygame status change
SET search_path = swmanagement;
DELETE FROM permissions WHERE code IN ('entity:game:change-state:enforce');
INSERT INTO permissions(code, description, created_at, updated_at) VALUES
('entity:game:change-state:enforce', 'Allow to set entitygame status ignoring the parent entitygame status', NOW(), NOW());
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM permissions WHERE code IN ('entity:game:change-state:enforce');
--rollback RESET search_path;

--changeset stepanov.aleksey:2022-08-23-SWS-34569-add-permission
--comment Add permission 'reset-change-nickname-attempts'
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'player:reset-change-nickname-attempts' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["player:reset-change-nickname-attempts"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'player:reset-change-nickname-attempts' where id = 1;
--rollback RESET search_path;

--changeset kirill.kaminskiy:2022-08-24-SWS-36113-remove-redundant-permissions
--comment Remove redundant permissions from 'permissions' table
SET search_path = swmanagement;
DELETE FROM permissions WHERE code='hub:engagement:tournaments';
DELETE FROM permissions WHERE code='hub:engagement:prize-drops';
DELETE FROM permissions WHERE code='hub:engagement:must-win-jackpots';
RESET search_path;
--rollback SELECT NOW();
