--liquibase formatted sql

--changeset artur.stepovyi:2020-10-07-SWS-21906-remove-permissions
--comment remove permission from roles
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'critical-files';
UPDATE roles SET permissions = permissions || '["critical-files"]' WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'critical-files';
--rollback RESET search_path;


--changeset valdis.akmens:2020-11-03-SWS-21782-get-audit-with-audits_summary-nested-data endDelimiter:# stripComments:false
--comment Extend procedure fnc_bo_audits for get audit with audits_summary nested data
SET search_path TO swmanagement;

ALTER FUNCTION fnc_bo_audits (character varying[],character varying[], integer, integer, boolean , boolean) RENAME TO fnc_bo_audits_before_4_48_0;

CREATE OR REPLACE FUNCTION fnc_bo_audits(p_where_filters character varying[], p_sort_by character varying[] DEFAULT NULL::character varying[], p_limit integer DEFAULT 20, p_offset integer DEFAULT 0, p_incl_sub_brands boolean DEFAULT false, p_incl_test boolean DEFAULT true)
RETURNS TABLE
(
    --audits
    audit_id                INTEGER,
    entity_id               INTEGER,
    ts                      TIMESTAMP,
    audits_summary_id       SMALLINT,
    history                 JSONB,
    initiator_type          swmanagement.enum_audits_initiator_type,
    initiator_name          VARCHAR(255),
    ip                      INET,
    user_agent              VARCHAR(2048),
    initiator_service_name  VARCHAR(255),
    audits_session_id       UUID,
    -- audits_summary
    id                      INTEGER,
    event_name              VARCHAR(255),
    summary                 VARCHAR(255),
    path                    VARCHAR(255),
    method                  swmanagement.enum_action_methods,
    created_at              TIMESTAMP,
    updated_at              TIMESTAMP
)
LANGUAGE plpgsql
AS $function$
/********************************************************************************************************

    Object Name:   fnc_bo_audits
    Purpose    :   Provide read access to audits table and archived audits_archive
                If not partition key in WHERE, than only to new partitions. If is partition key, than check pathman_config, and pathman_partition_list to see if we need:
                new table
                old table
                or both tables union
    History    :
        1.0.0
            Date    : Oct 16, 2018
            Authors : Valdis Akmens
            Notes   : Release (SWS-6904)
        1.0.1
            Date    : Jan 30, 2019
            Authors : Valdis Akmens
            Notes   : Change SELECT list for tables to dynamic generated to automatically include new columns to function result (SWDB-82)
        1.0.2
            Date    : May 02, 2019
            Authors : Valdis Akmens
            Notes   : Add parameters for including sub-entities in result (SWS-9902)
        1.0.3
            Date    : Aug 02, 2019
            Authors : Valdis Akmens
            Notes   : Postgres optimizator chooses wrong index when WHERE filter is definied with entity_id IN (..,..,)
                        it should choose index (entity_id, ts), not (ts) to force use right index string:", entity_id" is added to ORDER BY (SWDB-104)
        1.0.4
            Date    : Aug 11, 2020
            Authors : Valdis Akmens
            Notes   : Correct criteria to search in historical and live clusters (SWS-18819)
        1.0.5
            Date    : Sep 24, 2020
            Authors : Valdis Akmens
            Notes   : Extend procedure for get audit with audits_summary nested data (SWS-21782)
    Sample run:
    SELECT audit_id, entity_id, ts,initiator_type FROM fnc_bo_audits(
                                        p_where_filters => '{"entity_id = 51", "ts >=''2017-07-11 00:00:00''","ts < ''2017-07-19 00:00:00''" }',
                                        p_sort_by => '{"entity_id DESC"}',
                                        p_limit => 5,
                                        p_offset => 0
                                        );
    SELECT audit_id, entity_id, ts,initiator_type FROM fnc_bo_audits(
                                        p_where_filters => '{"entity_id = 50", "ts >=''2017-07-11 00:00:00''","ts < ''2017-07-19 00:00:00''" }',
                                        p_sort_by => '{"entity_id DESC"}',
                                        p_limit => 5,
                                        p_offset => 0,
                                        p_incl_sub_brands => TRUE,
                                        p_incl_test => FALSE
                                        );
    SELECT audit_id, entity_id, ts,initiator_type,path,method,event_name FROM swmanagement.fnc_bo_audits (p_where_filters => '{"entity_id = 275", "ts >=''2020-08-16 23:59:00''", "ts <''2020-08-18 23:59:00''", "event_name = ''LOGIN''" }', p_sort_by => '{"ts ASC"}', p_limit => 5, p_offset => 0 );
********************************************************************************************************/
DECLARE
    v_filter                VARCHAR;
    v_select                VARCHAR;
    v_select_act            VARCHAR;
    v_select_sum            VARCHAR;
    v_where                 VARCHAR:='WHERE ';
    v_where_sum             VARCHAR:='WHERE 1=1';
    v_sum_ids               VARCHAR:='';
    v_sort_by               VARCHAR:='ORDER BY ';
    v_limit                 VARCHAR:=COALESCE('LIMIT '||p_limit::TEXT,'');
    v_offset                VARCHAR:=COALESCE('OFFSET '||p_offset::TEXT,'');
    v_exec_sql              VARCHAR:='';
    v_line                  VARCHAR;
    v_actual_table          VARCHAR:='swmanagement.audits';
    v_summary_table         VARCHAR:='swmanagement.audits_summary';
    v_archive_table         VARCHAR:='swmanagement_archive.audits';
    v_partiton_key          VARCHAR;
    v_partiton_key_filters  VARCHAR[];
    v_is_in_archive         BOOLEAN:=FALSE;
    v_sub_brands            VARCHAR:='';
    v_new_line              VARCHAR:=chr(10);
    v_range_max             TIMESTAMP;
BEGIN

     /* Check mandatory params */
    IF p_where_filters IS NULL THEN
        RAISE EXCEPTION 'Filter for WHERE must be defined!';
    END IF;

    -- Get partition key from pathman configuration table
    SELECT expr
    INTO v_partiton_key
    FROM public.pathman_config
    WHERE
        partrel  = v_actual_table::regclass
    AND parttype = 2;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Table % has no valid partitions.', v_actual_table;
    END IF;

    -- Get column list for audits
    SELECT ' '||string_agg(attname, ', ' ORDER BY attnum)||' '
    INTO v_select_act
    FROM pg_catalog.pg_attribute
    WHERE  attrelid = v_actual_table::regclass
    AND    attnum > 0
    AND    NOT attisdropped;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No column list for %.', v_actual_table;
    END IF;

    -- Get column list for audits_summary
    SELECT ' '||string_agg(attname, ', ' ORDER BY attnum)||' '
    INTO v_select_sum
    FROM pg_catalog.pg_attribute
    WHERE  attrelid = v_summary_table::regclass
    AND    attnum > 0
    AND    NOT attisdropped;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No column list for %.', v_summary_table;
    END IF;

    v_select:= 'SELECT '||v_select_act||','||v_select_sum||' FROM ';

    --Build WHERE filter
    FOREACH v_filter IN ARRAY p_where_filters LOOP
        -- If includes sub-brands then need to find all brands for brand_id from p_where_filters
        IF p_incl_sub_brands = TRUE AND v_filter ILIKE '%entity_id%' THEN
                    --RAISE INFO '[%]: v_filter : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_filter;
                    EXECUTE 'WITH RECURSIVE hierarchy AS
                                            (
                                                SELECT entity_id, parent, name, type, 1 AS deep_level, title, key, path, is_test
                                                FROM   (SELECT id as entity_id, parent, name, type, 1 AS deep_level, title, key, path, is_test FROM entities) AS x
                                                WHERE  '||v_filter||'
                                                UNION ALL
                                                SELECT en.id AS entity_id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.key, en.path, en.is_test
                                                FROM   entities en
                                                INNER JOIN hierarchy h ON en.parent = h.entity_id
                                            )
                                            SELECT ''entity_id IN (''|| string_agg(entity_id::varchar, '', '' ORDER BY entity_id) ||'')''
                                            FROM   hierarchy
                                            WHERE  is_test = COALESCE(CASE WHEN '||p_incl_test||' <> FALSE THEN NULL ELSE '||p_incl_test||' END, is_test);' INTO v_sub_brands;
                    --RAISE INFO '[%]: v_sub_brands : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_sub_brands;
                    v_filter:= v_sub_brands;
        END IF;
        v_where:=v_where||CASE WHEN array_position(p_where_filters, v_filter) <> 1 THEN ' AND' ELSE '' END ||v_new_line||v_filter;

        -- Get datetime values from partition key filters, to match them with pathman configuration tables
        IF v_filter ILIKE v_partiton_key||'%' THEN
            v_partiton_key_filters:= array_append(v_partiton_key_filters, (SELECT substring(v_filter from '\''.*?\''' ))::VARCHAR);
        END IF;
        -- Get filters for audits_summary table
        IF v_filter ILIKE 'event_name%' OR v_filter ILIKE 'summary%' OR v_filter ILIKE 'path%' OR v_filter ILIKE 'method%' THEN
            v_where_sum:= v_where_sum||' AND '||v_new_line||v_filter;
        END IF;

    END LOOP;

    -- Add ids to WHERE clause from audits_summary to optimize query to archive cluster
    IF v_where_sum <> 'WHERE 1=1' THEN
        EXECUTE '
                SELECT ''(audits_summary_id IN (''|| string_agg(id::varchar, '', '' ORDER BY id) ||''))''
                        FROM  '||v_summary_table||v_new_line||v_where_sum||';' INTO v_sum_ids;
        v_where:= v_where||' AND '||v_new_line||v_sum_ids;
    END IF;

    --Build ORDER BY
    IF p_sort_by IS NULL OR p_sort_by = '{}'::VARCHAR[] THEN
        v_sort_by:= v_sort_by||v_partiton_key||' DESC';
    ELSE
        FOREACH v_filter IN ARRAY p_sort_by LOOP
            v_sort_by:=v_sort_by||CASE WHEN array_position(p_sort_by, v_filter) <> 1 THEN ' ,' ELSE '' END ||v_new_line||v_filter;
        END LOOP;
    END IF;

    --2019-08-02 Special hack to make Postgres use index (entity_id, ts) when conditions is with IN like:  "WHERE entity_id IN ('2219', '2221', '2270', '2533')"
    IF v_where ILIKE '%entity_id in%' THEN
        v_sort_by:=v_sort_by||' ,'||v_new_line||' entity_id';
    END IF;

    -- Check if exists partition key filters
    IF v_partiton_key_filters IS NOT NULL THEN
        --RAISE INFO '[%]: Partition key exists in WHERE ', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS');

        -- Check if partition filters points to archive table
        v_range_max:= (SELECT MAX(range_max::timestamp)
                                FROM public.pathman_partition_list
                                WHERE parent IN (v_archive_table::regclass)
                        );
        -- Check if partition filters points to archive table
        IF ( EXISTS(SELECT * FROM unnest( v_partiton_key_filters ) AS part_filter WHERE part_filter.part_filter::timestamp < v_range_max) ) THEN
            v_is_in_archive:= TRUE;
        END IF;
        v_is_in_archive:=COALESCE(v_is_in_archive,FALSE);

        -- Build EXEC string based on which tables need to use
        IF v_is_in_archive = TRUE THEN
            v_exec_sql:=v_select||' (('||v_new_line||
            v_select||v_actual_table||v_new_line||' JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
            ||') UNION ALL ('||
            v_select||v_archive_table||v_new_line||'JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
            ||')) AS q '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
        ELSE
            v_exec_sql:=v_select||v_actual_table||v_new_line||' JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
        END IF;
    ELSE
        -- If partition key doesnt exist in WHERE use only actual table
        v_exec_sql:=v_select||v_actual_table||v_new_line||' JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
    END IF;

    --RAISE INFO '[%]: v_exec_sql: %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_new_line||v_exec_sql;

    RETURN QUERY
        EXECUTE v_exec_sql
        ;
END;
$function$
;

RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_audits(character varying[],character varying[], integer , integer , boolean , boolean);
--rollback ALTER FUNCTION fnc_bo_audits_before_4_48_0 (character varying[],character varying[],  integer , integer , boolean , boolean) RENAME TO fnc_bo_audits;
--rollback RESET search_path;


--changeset aleksey.stepanov:2020-11-04-SWS-22400-add-game-provider-codes
--comment create game_provider_codes CRUD
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:provider-game-codes' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:provider-game-codes:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:provider-game-codes:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:provider-game-codes:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:provider-game-codes:delete' WHERE id = 1;

UPDATE roles SET permissions = permissions ||
'["keyentity:provider-game-codes", "keyentity:provider-game-codes:view", "keyentity:provider-game-codes:edit","keyentity:provider-game-codes:create","keyentity:provider-game-codes:delete"]'::jsonb WHERE id = 1;

CREATE TYPE enum_pht_provider_game_codes_status AS ENUM ('active', 'inactive');
CREATE TYPE enum_pht_provider_game_codes_game_type AS ENUM ('baccarat', 'dragonTiger', 'roulette', 'blackjack');
CREATE TABLE IF NOT EXISTS pht_provider_game_codes (
    id SERIAL PRIMARY KEY,
    provider_id int4 NOT NULL,
    provider_game_code varchar(255) NOT NULL,
    game_type enum_pht_provider_game_codes_game_type,
    status enum_pht_provider_game_codes_status,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW(),
    CONSTRAINT pht_provider_game_codes_provider_id_fkey FOREIGN KEY (provider_id)
        REFERENCES game_providers (id)
        ON DELETE CASCADE
        ON UPDATE NO ACTION
);
CREATE INDEX idx_pht_provider_game_codes_provider_id ON pht_provider_game_codes (provider_id);
COMMENT ON TABLE pht_provider_game_codes IS 'Game Provider Codes';
COMMENT ON COLUMN pht_provider_game_codes.id IS 'Id of game provider code';
COMMENT ON COLUMN pht_provider_game_codes.provider_id IS 'Provider ID';
COMMENT ON COLUMN pht_provider_game_codes.provider_game_code IS 'Game Provider Code';
COMMENT ON COLUMN pht_provider_game_codes.game_type IS 'Game Type (baccarat/dragonTiger/roulette/blackjack)';
COMMENT ON COLUMN pht_provider_game_codes.status IS 'Status (active/inactive)';
COMMENT ON COLUMN pht_provider_game_codes.created_at IS 'Created At';
COMMENT ON COLUMN pht_provider_game_codes.updated_at IS 'Updated At';

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:provider-game-codes' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:provider-game-codes:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:provider-game-codes:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:provider-game-codes:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:provider-game-codes:delete' WHERE id = 1;

--rollback DROP TABLE IF EXISTS pht_provider_game_codes;
--rollback DROP TYPE enum_pht_provider_game_codes_status;
--rollback DROP TYPE enum_pht_provider_game_codes_game_type;
--rollback RESET search_path;


--changeset valdis.akmens:2020-11-16-SWS-22966-correct-when-not-found-audits-summary endDelimiter:# stripComments:false
--comment Correct error when not found filters in audits_summary
SET search_path TO swmanagement;

ALTER FUNCTION fnc_bo_audits (character varying[],character varying[], integer, integer, boolean , boolean) RENAME TO fnc_bo_audits_before_4_48_1;

CREATE OR REPLACE FUNCTION fnc_bo_audits(p_where_filters character varying[], p_sort_by character varying[] DEFAULT NULL::character varying[], p_limit integer DEFAULT 20, p_offset integer DEFAULT 0, p_incl_sub_brands boolean DEFAULT false, p_incl_test boolean DEFAULT true)
RETURNS TABLE
(
    --audits
    audit_id                INTEGER,
    entity_id               INTEGER,
    ts                      TIMESTAMP,
    audits_summary_id       SMALLINT,
    history                 JSONB,
    initiator_type          swmanagement.enum_audits_initiator_type,
    initiator_name          VARCHAR(255),
    ip                      INET,
    user_agent              VARCHAR(2048),
    initiator_service_name  VARCHAR(255),
    audits_session_id       UUID,
    -- audits_summary
    id                      INTEGER,
    event_name              VARCHAR(255),
    summary                 VARCHAR(255),
    path                    VARCHAR(255),
    method                  swmanagement.enum_action_methods,
    created_at              TIMESTAMP,
    updated_at              TIMESTAMP
)
LANGUAGE plpgsql
AS $function$
/********************************************************************************************************

    Object Name:   fnc_bo_audits
    Purpose    :   Provide read access to audits table and archived audits_archive
                If not partition key in WHERE, than only to new partitions. If is partition key, than check pathman_config, and pathman_partition_list to see if we need:
                new table
                old table
                or both tables union
    History    :
        1.0.0
            Date    : Oct 16, 2018
            Authors : Valdis Akmens
            Notes   : Release (SWS-6904)
        1.0.1
            Date    : Jan 30, 2019
            Authors : Valdis Akmens
            Notes   : Change SELECT list for tables to dynamic generated to automatically include new columns to function result (SWDB-82)
        1.0.2
            Date    : May 02, 2019
            Authors : Valdis Akmens
            Notes   : Add parameters for including sub-entities in result (SWS-9902)
        1.0.3
            Date    : Aug 02, 2019
            Authors : Valdis Akmens
            Notes   : Postgres optimizator chooses wrong index when WHERE filter is definied with entity_id IN (..,..,)
                        it should choose index (entity_id, ts), not (ts) to force use right index string:", entity_id" is added to ORDER BY (SWDB-104)
        1.0.4
            Date    : Aug 11, 2020
            Authors : Valdis Akmens
            Notes   : Correct criteria to search in historical and live clusters (SWS-18819)
        1.0.5
            Date    : Sep 24, 2020
            Authors : Valdis Akmens
            Notes   : Extend procedure for get audit with audits_summary nested data (SWS-21782)
        1.0.6
            Date    : Nov 16, 2020
            Authors : Valdis Akmens
            Notes   : Correct error when not found filters in audits_summary (SWS-22966)
    Sample run:
    SELECT audit_id, entity_id, ts,initiator_type FROM fnc_bo_audits(
                                        p_where_filters => '{"entity_id = 51", "ts >=''2017-07-11 00:00:00''","ts < ''2017-07-19 00:00:00''" }',
                                        p_sort_by => '{"entity_id DESC"}',
                                        p_limit => 5,
                                        p_offset => 0
                                        );
    SELECT audit_id, entity_id, ts,initiator_type FROM fnc_bo_audits(
                                        p_where_filters => '{"entity_id = 50", "ts >=''2017-07-11 00:00:00''","ts < ''2017-07-19 00:00:00''" }',
                                        p_sort_by => '{"entity_id DESC"}',
                                        p_limit => 5,
                                        p_offset => 0,
                                        p_incl_sub_brands => TRUE,
                                        p_incl_test => FALSE
                                        );
    SELECT audit_id, entity_id, ts,initiator_type,path,method,event_name FROM swmanagement.fnc_bo_audits (p_where_filters => '{"entity_id = 275", "ts >=''2020-08-16 23:59:00''", "ts <''2020-08-18 23:59:00''", "event_name = ''LOGIN''" }', p_sort_by => '{"ts ASC"}', p_limit => 5, p_offset => 0 );
********************************************************************************************************/
DECLARE
    v_filter                VARCHAR;
    v_select                VARCHAR;
    v_select_act            VARCHAR;
    v_select_sum            VARCHAR;
    v_where                 VARCHAR:='WHERE ';
    v_where_sum             VARCHAR:='WHERE 1=1';
    v_sum_ids               VARCHAR:='';
    v_sort_by               VARCHAR:='ORDER BY ';
    v_limit                 VARCHAR:=COALESCE('LIMIT '||p_limit::TEXT,'');
    v_offset                VARCHAR:=COALESCE('OFFSET '||p_offset::TEXT,'');
    v_exec_sql              VARCHAR:='';
    v_line                  VARCHAR;
    v_actual_table          VARCHAR:='swmanagement.audits';
    v_summary_table         VARCHAR:='swmanagement.audits_summary';
    v_archive_table         VARCHAR:='swmanagement_archive.audits';
    v_partiton_key          VARCHAR;
    v_partiton_key_filters  VARCHAR[];
    v_is_in_archive         BOOLEAN:=FALSE;
    v_sub_brands            VARCHAR:='';
    v_new_line              VARCHAR:=chr(10);
    v_range_max             TIMESTAMP;
BEGIN

     /* Check mandatory params */
    IF p_where_filters IS NULL THEN
        RAISE EXCEPTION 'Filter for WHERE must be defined!';
    END IF;

    -- Get partition key from pathman configuration table
    SELECT expr
    INTO v_partiton_key
    FROM public.pathman_config
    WHERE
        partrel  = v_actual_table::regclass
    AND parttype = 2;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Table % has no valid partitions.', v_actual_table;
    END IF;

    -- Get column list for audits
    SELECT ' '||string_agg(attname, ', ' ORDER BY attnum)||' '
    INTO v_select_act
    FROM pg_catalog.pg_attribute
    WHERE  attrelid = v_actual_table::regclass
    AND    attnum > 0
    AND    NOT attisdropped;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No column list for %.', v_actual_table;
    END IF;

    -- Get column list for audits_summary
    SELECT ' '||string_agg(attname, ', ' ORDER BY attnum)||' '
    INTO v_select_sum
    FROM pg_catalog.pg_attribute
    WHERE  attrelid = v_summary_table::regclass
    AND    attnum > 0
    AND    NOT attisdropped;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No column list for %.', v_summary_table;
    END IF;

    v_select:= 'SELECT '||v_select_act||','||v_select_sum||' FROM ';

    --Build WHERE filter
    FOREACH v_filter IN ARRAY p_where_filters LOOP
        -- If includes sub-brands then need to find all brands for brand_id from p_where_filters
        IF p_incl_sub_brands = TRUE AND v_filter ILIKE '%entity_id%' THEN
                    --RAISE INFO '[%]: v_filter : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_filter;
                    EXECUTE 'WITH RECURSIVE hierarchy AS
                                            (
                                                SELECT entity_id, parent, name, type, 1 AS deep_level, title, key, path, is_test
                                                FROM   (SELECT id as entity_id, parent, name, type, 1 AS deep_level, title, key, path, is_test FROM entities) AS x
                                                WHERE  '||v_filter||'
                                                UNION ALL
                                                SELECT en.id AS entity_id, en.parent, en.name, en.type, h.deep_level + 1 AS deep_level, en.title, en.key, en.path, en.is_test
                                                FROM   entities en
                                                INNER JOIN hierarchy h ON en.parent = h.entity_id
                                            )
                                            SELECT ''entity_id IN (''|| string_agg(entity_id::varchar, '', '' ORDER BY entity_id) ||'')''
                                            FROM   hierarchy
                                            WHERE  is_test = COALESCE(CASE WHEN '||p_incl_test||' <> FALSE THEN NULL ELSE '||p_incl_test||' END, is_test);' INTO v_sub_brands;
                    --RAISE INFO '[%]: v_sub_brands : %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_sub_brands;
                    v_filter:= v_sub_brands;
        END IF;
        v_where:=v_where||CASE WHEN array_position(p_where_filters, v_filter) <> 1 THEN ' AND' ELSE '' END ||v_new_line||v_filter;

        -- Get datetime values from partition key filters, to match them with pathman configuration tables
        IF v_filter ILIKE v_partiton_key||'%' THEN
            v_partiton_key_filters:= array_append(v_partiton_key_filters, (SELECT substring(v_filter from '\''.*?\''' ))::VARCHAR);
        END IF;
        -- Get filters for audits_summary table
        IF v_filter ILIKE 'event_name%' OR v_filter ILIKE 'summary%' OR v_filter ILIKE 'path%' OR v_filter ILIKE 'method%' THEN
            v_where_sum:= v_where_sum||' AND '||v_new_line||v_filter;
        END IF;

    END LOOP;

    -- Add ids to WHERE clause from audits_summary to optimize query to archive cluster
    IF v_where_sum <> 'WHERE 1=1' THEN
        EXECUTE '
                SELECT ''(audits_summary_id IN (''|| string_agg(id::varchar, '', '' ORDER BY id) ||''))''
                        FROM  '||v_summary_table||v_new_line||v_where_sum||';' INTO v_sum_ids;
        v_sum_ids:= COALESCE(v_sum_ids, '1 = 0');
        v_where:= v_where||' AND '||v_new_line||v_sum_ids;
    END IF;

    --Build ORDER BY
    IF p_sort_by IS NULL OR p_sort_by = '{}'::VARCHAR[] THEN
        v_sort_by:= v_sort_by||v_partiton_key||' DESC';
    ELSE
        FOREACH v_filter IN ARRAY p_sort_by LOOP
            v_sort_by:=v_sort_by||CASE WHEN array_position(p_sort_by, v_filter) <> 1 THEN ' ,' ELSE '' END ||v_new_line||v_filter;
        END LOOP;
    END IF;

    --2019-08-02 Special hack to make Postgres use index (entity_id, ts) when conditions is with IN like:  "WHERE entity_id IN ('2219', '2221', '2270', '2533')"
    IF v_where ILIKE '%entity_id in%' THEN
        v_sort_by:=v_sort_by||' ,'||v_new_line||' entity_id';
    END IF;

    -- Check if exists partition key filters
    IF v_partiton_key_filters IS NOT NULL THEN
        --RAISE INFO '[%]: Partition key exists in WHERE ', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS');

        -- Check if partition filters points to archive table
        v_range_max:= (SELECT MAX(range_max::timestamp)
                                FROM public.pathman_partition_list
                                WHERE parent IN (v_archive_table::regclass)
                        );
        -- Check if partition filters points to archive table
        IF ( EXISTS(SELECT * FROM unnest( v_partiton_key_filters ) AS part_filter WHERE part_filter.part_filter::timestamp < v_range_max) ) THEN
            v_is_in_archive:= TRUE;
        END IF;
        v_is_in_archive:=COALESCE(v_is_in_archive,FALSE);

        -- Build EXEC string based on which tables need to use
        IF v_is_in_archive = TRUE THEN
            v_exec_sql:=v_select||' (('||v_new_line||
            v_select||v_actual_table||v_new_line||' JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
            ||') UNION ALL ('||
            v_select||v_archive_table||v_new_line||'JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||' LIMIT '||(p_limit+p_offset)::TEXT||v_new_line
            ||')) AS q '||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
        ELSE
            v_exec_sql:=v_select||v_actual_table||v_new_line||' JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
        END IF;
    ELSE
        -- If partition key doesnt exist in WHERE use only actual table
        v_exec_sql:=v_select||v_actual_table||v_new_line||' JOIN '||v_summary_table||' ON audits_summary_id = id'||v_new_line||v_where||v_new_line||v_sort_by||v_new_line||v_limit||v_new_line||v_offset||';';
    END IF;

    --RAISE INFO '[%]: v_exec_sql: %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'),v_new_line||v_exec_sql;

    RETURN QUERY
        EXECUTE v_exec_sql
        ;
END;
$function$
;

RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_audits(character varying[],character varying[], integer , integer , boolean , boolean);
--rollback ALTER FUNCTION fnc_bo_audits_before_4_48_1 (character varying[],character varying[],  integer , integer , boolean , boolean) RENAME TO fnc_bo_audits;
--rollback RESET search_path;
