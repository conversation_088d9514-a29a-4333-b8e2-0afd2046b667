--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement, public;
--example: CREATE TABLE tmp(id int);
--example: --rollback DROP TABLE tmp;


--changeset andrey.shmigiro:2020-01-14-SWS-XXXX-start-release-4.30.0
--comment label for 4.30.0
select now();
--rollback select now();


--changeset stepanov.aleksey:2020-01-14-SWS-15489
--comment Add api to store Favorite games per Player
SET search_path TO swmanagement;
CREATE TABLE IF NOT EXISTS favorite_games_by_player (
    entity_id INTEGER NOT NULL,
    player_code VARCHAR(255) NOT NULL,
    game_code VARCHAR(255) NOT NULL,
    count INTEGER NOT NULL DEFAULT 1,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT favorite_games_by_player_entity_id_fkey FOREIGN KEY (entity_id)
        REFERENCES entities (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE,
    PRIMARY KEY (entity_id, player_code, game_code)
);
COMMENT ON TABLE favorite_games_by_player IS 'Table for storing favorite games by player';
COMMENT ON COLUMN favorite_games_by_player.entity_id IS 'Id of entity from entities table';
COMMENT ON COLUMN favorite_games_by_player.player_code IS 'player code';
COMMENT ON COLUMN favorite_games_by_player.game_code IS 'game code';
COMMENT ON COLUMN favorite_games_by_player.count IS 'How many times the game has been opened';

ALTER TABLE favorite_games_by_player OWNER TO swmanagement;
GRANT ALL PRIVILEGES ON TABLE favorite_games_by_player TO swmanagement;
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP TABLE IF EXISTS favorite_games_by_player;
--rollback RESET search_path;

--changeset stepanov.aleksey:2020-01-15-SWS-15491
--comment Add Comission/nocomission flag for baccarat games
SET search_path TO swmanagement;
UPDATE games SET features = jsonb_concat(features, '{"isComission": false}') where provider_game_code = 'sw_live_ncbac';
UPDATE games SET features = jsonb_concat(features, '{"isComission": true}') where provider_game_code = 'sw_live_cbac';
RESET search_path;
--rollback SET search_path TO swmanagement;
--UPDATE games SET features = json_strip_nulls(json(jsonb_set(features, '{isComission}', jsonb 'null'))) where provider_game_code in ('sw_live_cbac','sw_live_ncbac');
--rollback RESET search_path;

--changeset stepanov.aleksey:2020-01-16-SWS-15502
--comment Add 'icon' and 'translations' fields for gamecategories
SET search_path TO swmanagement;
ALTER TABLE game_categories ADD COLUMN translations JSONB;
ALTER TABLE game_categories ADD COLUMN icon TEXT;
COMMENT ON COLUMN game_categories.translations IS 'Translations for game category';
COMMENT ON COLUMN game_categories.icon IS 'Game category icon';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE game_categories DROP COLUMN translations;
--rollback ALTER TABLE game_categories DROP COLUMN icon;
--rollback RESET search_path;

--changeset pavel.shamshurov:2020-01-16-SWS-15671-modify-reports-table
--comment Add clientFeatures field for entity game
SET search_path TO swmanagement;
ALTER TABLE bi_reports ADD COLUMN ordering INTEGER DEFAULT 0;
COMMENT ON COLUMN bi_reports.ordering IS 'Bi report position in list';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE bi_reports DROP COLUMN ordering;
--rollback RESET search_path;

--changeset stepanov.aleksey:2020-01-23-SWS-15504
--comment Add api to store Recently played games per Player
--comment SWS-15489 - Add api to store Favorite games per Player
SET search_path TO swmanagement;
UPDATE roles SET permissions = permissions || '["keyentity:favoritegames", "keyentity:recentlygames"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:favoritegames' - 'keyentity:recentlygames' WHERE id = 1;
--rollback RESET search_path;


--changeset egor.morozov:2020-01-22-SWS-8459-Test-JP-for-GRC-Games
--comment Separate table for test challenges
SET search_path TO swsrt;
CREATE TABLE test_challenges(
  id SERIAL NOT NULL,
  name VARCHAR(255) NOT NULL CONSTRAINT test_challenges_name_key UNIQUE,
  start_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
  finish_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
  pot_amount NUMERIC NOT NULL,
  target_badges INTEGER NOT NULL,
  definition JSONB,
  start_transaction VARCHAR(28) NOT NULL,
  finish_transaction VARCHAR(28),
  spare_amount NUMERIC,
  state ENUM_CHALLENGES_STATE NOT NULL,
  version INTEGER  NOT NULL,
  created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT now(),
  currency CHAR(3) default 'EUR',
  CONSTRAINT test_challenges_pkey PRIMARY KEY (id)
);

CREATE INDEX idx_test_challenges_finish_at on test_challenges (finish_at);

COMMENT ON TABLE test_challenges IS 'Table for challenges for test players';
COMMENT ON COLUMN test_challenges.id IS 'Unique challenge Id';
COMMENT ON COLUMN test_challenges.name IS 'Unique challenge name';
COMMENT ON COLUMN test_challenges.start_at IS 'Start challenge time';
COMMENT ON COLUMN test_challenges.finish_at IS 'Finish challenge time';
COMMENT ON COLUMN test_challenges.pot_amount IS 'Challenge prize pool';
COMMENT ON COLUMN test_challenges.target_badges IS 'Required amount of badges to win challenge prize';
COMMENT ON COLUMN test_challenges.definition IS 'Challenge additional information';
COMMENT ON COLUMN test_challenges.start_transaction IS 'TransactionId for JPN for freeze challenge pool';
COMMENT ON COLUMN test_challenges.finish_transaction IS 'TransactionId for JPN for unfreeze challenge pool';
COMMENT ON COLUMN test_challenges.spare_amount IS 'All undistributed money will be moved here after challenge is finished';
COMMENT ON COLUMN test_challenges.state IS 'Indicate challenge state';
COMMENT ON COLUMN test_challenges.version IS 'Used for optimistic lock';

ALTER TABLE test_challenges OWNER TO swsrt;
GRANT ALL PRIVILEGES ON TABLE test_challenges TO swsrt;

RESET search_path;
--rollback SET search_path TO swsrt;
--rollback DROP TABLE IF EXISTS test_challenges;
--rollback RESET search_path;
