--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset pavel.shamshurov:2020-03-30-SWS-XXXX-start-release-4.34.0
--comment label for 4.33.0
select now();
--rollback select now();


--changeset pavel.shamshurov:2020-03-30-SWS-17171-add-extra-permissions-to-role
--comment Add extra permissions to superadmin role
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'bi:report:bets-distribution' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:bets-distribution' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'bi:report:summary-real-time-advanced' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:summary-real-time-advanced' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["bi:report:bets-distribution", "keyentity:bi:report:bets-distribution", "bi:report:summary-real-time-advanced", "keyentity:bi:report:summary-real-time-advanced"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:bets-distribution' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:bets-distribution' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:summary-real-time-advanced' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:summary-real-time-advanced' WHERE id = 1;
--rollback RESET search_path;


--changeset pavel.shamshurov:2020-03-31-SWS-17459-add-extra-permissions-to-role
--comment Add extra permissions to superadmin role
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'bi:report:jackpot-split-prize' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:jackpot-split-prize' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["bi:report:jackpot-split-prize", "keyentity:bi:report:jackpot-split-prize"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:jackpot-split-prize' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:jackpot-split-prize' WHERE id = 1;
--rollback RESET search_path;
