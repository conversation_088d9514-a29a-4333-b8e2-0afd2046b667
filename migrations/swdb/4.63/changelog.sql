--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset aleh.rudzko:2021-07-05_SWS-28297_add-new-flag-instead-of-custom-limits
--comment Update new flag instead of old one.
SET search_path = swmanagement;
UPDATE games SET features = features - 'isLimitFiltersSupported' || '{"isLimitFiltersSupported": true}' WHERE features ->> 'isCustomLimitsSupported' = 'true';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE games SET features = features - 'isLimitFiltersSupported' WHERE features ->> 'isLimitFiltersSupported' = 'true';
--rollback RESET search_path;

--changeset aleh.rudzko:2021-07-05_launch-game-inside-lobby
--comment Add is_default field to lobby
SET search_path = swmanagement;
ALTER TABLE lobbies ADD COLUMN is_default BOOLEAN DEFAULT FALSE;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE lobbies DROP COLUMN is_default;
--rollback RESET search_path;