--liquibase formatted sql

--changeset aleh.rudzko:2020-11-11-SWS-21630-ols-improvements
--comment add new flag to ignore invalid filtering
SET search_path = swmanagement;
ALTER TABLE game_group_filters ADD COLUMN IF NOT EXISTS ignore_invalid BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN game_group_filters.ignore_invalid IS 'Ignore invalid filtering, by default raise error';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE game_group_filters DROP COLUMN IF EXISTS ignore_invalid;
--rollback RESET search_path;

--changeset aleh.rudzko:2020-12-02-SWS-23394-update-IDR-coin-bets
SET search_path = swmanagement;
update stake_ranges set coin_bets = '[100,200,300,400,500,600,800,1000,2000,3000,4000,5000,6000,8000,10000,20000,30000,50000,80000,100000,200000,300000,500000,600000,800000,900000,1000000,1500000,2000000,2500000,3000000]' where currency = 'IDR';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback update stake_ranges SET coin_bets = '[10, 20, 30, 50, 80, 100, 200, 300, 500, 800, 1000, 2000, 3000, 5000, 8000, 10000, 20000, 30000, 50000, 80000, 100000, 200000, 300000, 500000, 600000, 800000, 900000, 1000000, 1500000, 2000000, 2500000, 3000000, 4000000, 5000000, 6000000]' where currency = 'IDR';
--rollback RESET search_path;
