--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset dmitriy.palaznik:2024-05-28-SWS-44417-pariplay-no-pending-credit-transactions-in-reconciliation-responses
--comment Add a new table with a right naming to save problematic bet/win for Pariplay and data further migration from old table
SET search_path TO swmanagement;

CREATE SEQUENCE bet_win_history_id_seq;

SELECT setval('bet_win_history_id_seq', COALESCE((SELECT MAX(id) + 10000 FROM bet_win_histories), 1));

CREATE TABLE bet_win_history (
   id bigint DEFAULT nextval('bet_win_history_id_seq'::regclass) PRIMARY KEY,
   brand_id integer NOT NULL,
   round_id bigint NOT NULL,
   player_code character varying(255) NOT NULL,
   game_code character varying(255),
   currency character(3) NOT NULL,
   trx_id character varying(255) NOT NULL,
   bet numeric,
   win numeric,
   balance_before numeric,
   balance_after numeric,
   is_test boolean,
   inserted_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_bet_win_history_round_brand ON bet_win_history USING btree (round_id, brand_id);

COMMENT ON TABLE bet_win_history IS 'Canceled bet/win records';

COMMENT ON COLUMN bet_win_history.brand_id IS 'Brand identifier';
COMMENT ON COLUMN bet_win_history.player_code IS 'Player code';
COMMENT ON COLUMN bet_win_history.player_code IS 'Player code';
COMMENT ON COLUMN bet_win_history.game_code IS 'Game code in management system';
COMMENT ON COLUMN bet_win_history.currency IS 'Currency code';
COMMENT ON COLUMN bet_win_history.trx_id IS 'Transaction id';
COMMENT ON COLUMN bet_win_history.win IS 'Win amount';
COMMENT ON COLUMN bet_win_history.bet IS 'Bet amount';
COMMENT ON COLUMN bet_win_history.balance_before IS 'Balance at the beginning of round';
COMMENT ON COLUMN bet_win_history.balance_after IS 'Balance at the end of round';
COMMENT ON COLUMN bet_win_history.is_test IS 'Is it test game';
COMMENT ON COLUMN bet_win_history.inserted_at IS 'Timestamp when row was inserted';

--rollback SET search_path TO swmanagement;
--rollback DROP TABLE bet_win_history;
--rollback DROP SEQUENCE IF EXISTS bet_win_history_id_seq;
--rollback RESET search_path;


--changeset dmitriy.palaznik:2024-05-31-SWS-44417-pariplay-no-pending-credit-transactions-in-reconciliation-responses
--comment Set owner of sequence to bet_win_history table.
SET search_path TO swmanagement;

ALTER SEQUENCE bet_win_history_id_seq OWNED BY bet_win_history.id;

RESET search_path;

--rollback SET search_path TO swmanagement;
--rollback ALTER SEQUENCE bet_win_history_id_seq OWNED BY NONE;
--rollback RESET search_path;
