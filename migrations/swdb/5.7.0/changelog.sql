--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset andrei.stefan:2023-05-22-SWS-40528-custom-static-domain
--comment Add new column 'provider_game_code' to the 'static_domains' table
--comment Add new column 'domain' to the 'entity_games' table
SET search_path = swmanagement;

ALTER TABLE static_domains ADD COLUMN provider_game_code VARCHAR(255);
COMMENT ON COLUMN static_domains.provider_game_code IS 'Game provider code';

ALTER TABLE static_domains ADD CONSTRAINT unq_provider_game_code UNIQUE (provider_game_code);

ALTER TABLE entity_games ADD COLUMN domain VARCHAR(255);
COMMENT ON COLUMN entity_games.domain IS 'Static domain';

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE static_domains DROP COLUMN provider_game_code;
--rollback ALTER TABLE entity_games DROP COLUMN domain;
--rollback RESET search_path;

--changeset andrei.stefan:2023-06-07-SWS-40528-only-entity-game
--comment Remove 'provider_game_code' from the 'static_domains' table
SET search_path = swmanagement;

ALTER TABLE static_domains DROP COLUMN IF EXISTS provider_game_code;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE static_domains ADD COLUMN provider_game_code VARCHAR(255);
--rollback COMMENT ON COLUMN static_domains.provider_game_code IS 'Game provider code';

--rollback ALTER TABLE static_domains ADD CONSTRAINT unq_provider_game_code UNIQUE (provider_game_code);
