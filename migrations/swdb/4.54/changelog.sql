--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset aleksey.stepanov:2021-02-18-SWS-XXXX-start-release-4.54.0
--comment label for 4.54.0
select now();
--rollback select now();

--changeset valdis.akmens:2021-02-19-NO-JIRA-adjust-fnc_refresh_cache_jackpot_settings endDelimiter:# stripComments:false
--comment Change TRUNCATE to DELETE to avoid locks on sync replica

SET search_path TO monitoring;

ALTER FUNCTION fnc_refresh_cache_jackpot_settings() RENAME TO fnc_refresh_cache_jackpot_settings_before_4_54_0;

CREATE OR REPLACE FUNCTION fnc_refresh_cache_jackpot_settings()
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
*******************************************************************************
    Object Name:   fnc_refresh_cache_jackpot_settings
    Purpose    :   Refresh cache_jackpot_settings table.
    History    :
        1.0.0
            Date    :  Jun 05, 2020
            Authors : Valdis Akmens
            Notes   : Release (DEVOPS-9068)
        1.0.1
            Date    :  Feb 19, 2021
            Authors : Valdis Akmens
            Notes   : Change TRUNCATE to DELETE to avoid locks on sync replica

    Sample run:
      SELECT * FROM monitoring.fnc_refresh_cache_jackpot_settings();
*******************************************************************************
*/
DECLARE

BEGIN
    log_time := clock_timestamp(); log_msg := 'INFO: Refresh cache_jackpot_settings job started'; RETURN NEXT;

    /* Check if MDB */
    IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
    END IF;

    -- Temporary table for JP settings
    CREATE TEMPORARY TABLE tmp_cache_jackpot_settings (
	game_id     INTEGER NULL,
	entity_id   INTEGER NULL,
	tree_pos    INTEGER NULL,
	brand_path  VARCHAR NULL
    ) ON COMMIT DROP;

    -- Prepare data in temp table
    WITH
    cte_jp_games AS (
    SELECT g.id AS game_id, jsonb_array_elements_text(g.features -> 'jackpotTypes') AS jp_type_name
    FROM   swmanagement.games g
    WHERE  g.features -> 'jackpotTypes' IS NOT NULL
        AND  jsonb_typeof(g.features -> 'jackpotTypes') = 'array'
        AND  g.status = 'available'
    ),
    cte_direct_settings AS (
    SELECT eg.game_id, eg.entity_id, Coalesce(StrPos(e.path, e.name), 20000) AS tree_pos, e.path AS brand_path
    FROM   swmanagement.entity_games eg
            INNER JOIN cte_jp_games jg ON eg.game_id = jg.game_id
                                        AND eg.settings -> 'jackpotId' ? jg.jp_type_name
            INNER JOIN swmanagement.entities e ON e.id = eg.entity_id
    WHERE eg.status = 'normal'
        AND EXISTS (
                    SELECT NULL
                    FROM  swjackpot.jp_instance jpi
                    WHERE jpi.region_id IS NOT NULL
                    AND jpi.deleted_at IS NULL
                    AND jpi.pid = jsonb_extract_path_text(eg.settings, 'jackpotId', jg.jp_type_name)
                    )
    )
    INSERT INTO tmp_cache_jackpot_settings
    SELECT * FROM cte_direct_settings;

    -- Insert data to real table
    DELETE FROM monitoring.cache_jackpot_settings;
    INSERT INTO monitoring.cache_jackpot_settings
    SELECT * FROM tmp_cache_jackpot_settings;


    log_time := clock_timestamp(); log_msg := 'INFO: Refresh cache_jackpot_settings job finished'; RETURN NEXT;
    RETURN;
END;
$function$
;

RESET search_path;

--rollback SET search_path TO monitoring;
--rollback DROP FUNCTION IF EXISTS fnc_refresh_cache_jackpot_settings();
--rollback ALTER FUNCTION fnc_refresh_cache_jackpot_settings_before_4_54_0 () RENAME TO fnc_refresh_cache_jackpot_settings;
--rollback RESET search_path;