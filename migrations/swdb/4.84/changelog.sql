--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset sergey.malkov:2022-04-26-SWS-33301-Add-api-to-find-game-context
--comment Find game-contexts permissions to superadmin role
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:view:game-contexts' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:view:game-contexts"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:view:game-contexts' WHERE id = 1;
--rollback RESET search_path;


--changeset maksim.puzikov:2022-04-29-SWS-33704-distribution-type
--comment Add distribution type for deferred payments
--comment The tournament shared between entities of the same operator should be 'bespoke'.
--comment In other word, shared between multiple operators should be 'network'.
SET search_path = swdeferredpmnt;

CREATE TYPE enum_deferred_payments_distribution_type AS ENUM ('network', 'bespoke');

ALTER TABLE deferred_payments ADD COLUMN distribution_type enum_deferred_payments_distribution_type NULL;
COMMENT ON COLUMN deferred_payments.distribution_type IS 'The tournament shared between entities of the same operator should be bespoke. In other word, shared between multiple operators should be network.';
RESET search_path;

--rollback SET search_path = swdeferredpmnt;
--rollback ALTER TABLE deferred_payments DROP column distribution_type;
--rollback DROP TYPE enum_deferred_payments_distribution_type;
--rollback RESET search_path;


--changeset stepan.shklyanko:2022-03-23-SWS-33384-Clarifications-on-Promo-drop-useless-tables
--comment Dropping the redundant promo tables - they are never used, this is a copy of the same changes in 4.82 as due to stability issues, previous change set was applied on EU only
SET search_path = swmanagement;
DROP TABLE IF EXISTS promotion_reward_rebates;
DROP TABLE IF EXISTS promotion_reward_virtual_money;
RESET search_path;
--rollback SELECT NOW();

--changeset stepanov.aleksey:2022-05-13-SWS-34481-update-game-type
--comment Update live game
SET search_path = swmanagement;
CREATE TABLE IF NOT EXISTS swbackup.games_220513_sws34481 AS SELECT * FROM games WHERE provider_game_code in ('sw_live_erol', 'sw_live_cbac', 'sw_live_ncbac', 'sw_live_dt', 'sw_live_erol_rush', 'sw_live_bj', 'sw_live_erol_atom', 'sw_live_bjmax', 'sw_live_jwheel', 'sw_live_andbah');
UPDATE games SET type='live' WHERE code in (SELECT code FROM swbackup.games_220513_sws34481);
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE games g SET type = bg.type FROM swbackup.games_220513_sws34481 AS bg WHERE bg.id = g.id;
--rollback DROP TABLE IF EXISTS swbackup.games_220513_sws34481;
--rollback RESET search_path;
