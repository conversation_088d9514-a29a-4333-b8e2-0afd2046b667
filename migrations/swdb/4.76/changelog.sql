--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset oleg.rudko:2021-12-17-SWS-31411-add-is-test-to-deferred-payments
--comment Add is_test flag to deferred payments table
SET search_path = swdeferredpmnt;
ALTER TABLE deferred_payments ADD COLUMN is_test bool DEFAULT FALSE;
COMMENT ON COLUMN deferred_payments.is_test IS 'Is test payment';
RESET search_path;
--rollback SET search_path = swdeferredpmnt;
--rollback ALTER TABLE deferred_payments DROP COLUMN is_test;
--rollback RESET search_path;