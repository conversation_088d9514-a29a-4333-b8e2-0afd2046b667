--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset dmitriy.palaznik:2023-01-05-SWS-38546-fix-sequences-for-jp-contribution-win-log tables
--comment add ownership of sequences
SET search_path = swjackpot;
ALTER SEQUENCE jp_contribution_id_seq owned BY jp_contribution_log.id;
ALTER SEQUENCE jp_win_id_seq owned BY jp_win_log.id;
ALTER SEQUENCE remote_jp_contribution_log_id_seq owned BY remote_jp_contribution_log.id;
ALTER SEQUENCE remote_jp_win_log_id_seq owned BY remote_jp_win_log.id;
RESET search_path;
--rollback SET search_path = swjackpot
--rollback ALTER SEQUENCE jp_contribution_id_seq owned BY NONE;
--rollback ALTER SEQUENCE jp_win_id_seq owned BY NONE;
--rollback ALTER SEQUENCE remote_jp_contribution_log_id_seq owned BY NONE;
--rollback ALTER SEQUENCE remote_jp_win_log_id_seq owned BY NONE;
--rollback RESET search_path

--changeset valdis.akmens:2022-12-27_SWDB-290_partition_bo_aggr_player_rounds_by_country
--comment Partition table bo_aggr_player_rounds_by_country
SET search_path = swmanagement;

ALTER TABLE IF EXISTS bo_aggr_rounds SET SCHEMA swbackup;

SELECT public.set_init_callback (
                        relation        => 'swmanagement.bo_aggr_player_rounds_by_country'::regclass,
                        callback        => 'public.pathman_callback(jsonb)' );
SELECT public.create_range_partitions (
                        parent_relid    => 'swmanagement.bo_aggr_player_rounds_by_country'::regclass,
                        expression      => 'date_hour',
                        start_value     => (SELECT date_trunc('week',COALESCE(min(date_hour),now()))::date FROM swmanagement.bo_aggr_player_rounds_by_country),
                        p_interval      => '7 days'::interval,
                        p_count         => (SELECT CASE WHEN NOT EXISTS (SELECT 1 FROM swmanagement.bo_aggr_player_rounds_by_country) THEN 1 ELSE NULL END),
                        partition_data  => FALSE );

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback    ALTER TABLE IF EXISTS swbackup.bo_aggr_rounds SET SCHEMA swmanagement;
--rollback RESET search_path;