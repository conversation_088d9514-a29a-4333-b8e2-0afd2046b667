--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset aleksey.ignate<PERSON>:2022-02-01_DEVOPS-16279_fix_move_old_data_to_arch endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Archive DB emulation on CDs(Dev/QA) environments. Set sorting order for table cycle to avoid deadlocks and embeded SQL-errors handler

CREATE OR REPLACE FUNCTION swsystem.fnc_move_data_to_arch 
(
    p_lifespan      text    DEFAULT NULL, 
    p_check_only    bool    DEFAULT FALSE 
)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name:   fnc_move_data_to_arch
    Purpose    : Copy rows older than certain date (1 month ago usually, from config for each table, or from <p_lifespan> - will be used the greatest interval) 
                from a set of big tables listed and configured in the table "swsystem.cleanups"
                to proper archive tables usually placed in the schemes "sw*_archive" and "sw*_archive_ro"
                and clear the original tables from these old rows
    History    :
        1.0.0
            Date    : June 09, 2020
            Authors : Ales
            Notes   : Release (DEVOPS-9291)
        1.0.1
            Date    : Sep 03, 2020
            Authors : Ales
            Notes   : Added copying old data also to the archive_ro schema (SWS-21281)
        1.0.2
            Date    : 2022-01-19
            Authors : Ales
            Notes   : Set sorting order for tables and embedded SQL-errors handler (DEVOPS-16279)
            
    Sample run:
        SELECT swsystem.fnc_move_data_to_arch(null, true);
        SELECT jsonb_pretty( swsystem.fnc_move_data_to_arch(null) );
********************************************************************************************************/
DECLARE
    i_lifespan  interval    := p_lifespan::interval;
    v_res       jsonb       := '{}'::jsonb;
    v_today     timestamp   := date_trunc('DAY', now() AT TIME ZONE 'UTC');
    v_arch_suff varchar(30) := '_archive';
    v_refl_suff varchar(30) := '_ro';
    c           record;  
    v_dead_line timestamp;
    v_num       int;
    v_sql       text;
    v_count     int         := 0;
    v_total     int         := 0;
    v_err_state   TEXT;
    v_err_msg     TEXT;
    v_err_detail  TEXT;
    v_err_hint    TEXT;
    v_err_context TEXT;
BEGIN
    
    FOR c IN (
                WITH arch_tables AS (
                    SELECT  split_part(tabrel::text, '.', 2) AS live_table,
                            split_part(tabrel::text, '.', 1) AS live_schema,
                            split_part(arch_tabrel::text, '.', 2) AS arch_table,
                            split_part(arch_tabrel::text, '.', 1) AS arch_schema,
                            tabrel, dt_col, lifespan                                                
                    FROM swsystem.partition_cleanup_config                
                    WHERE is_active 
                )              
                , cols AS (
                    SELECT table_schema, table_name, 
                            array_agg(column_name::varchar ORDER BY ordinal_position) AS arr
                    FROM information_schema.columns 
                    GROUP BY table_schema, table_name
                )
                SELECT  v.live_table AS table_name, v.tabrel, 
                        COALESCE(v.dt_col, 'inserted_at') AS dt_col, lifespan,
                        v.live_schema, l.table_name AS live_table, l.arr AS live_cols,
                        a.table_schema AS arch_schema, a.table_name AS arch_table, a.arr AS arch_cols, 
                        r.table_schema AS view_schema, r.table_name AS view_table, r.arr AS view_cols
                FROM arch_tables v                    
                LEFT JOIN cols l
                    ON l.table_schema = v.live_schema
                        AND l.table_name = v.live_table
                LEFT JOIN cols a 
                    ON a.table_schema = COALESCE(v.arch_schema, v.live_schema || v_arch_suff) 
                        AND a.table_name = COALESCE(v.arch_table, v.live_table)
                LEFT JOIN cols r
                    ON r.table_schema = (a.table_schema || v_refl_suff)
                        AND r.table_name = a.table_name
                ORDER BY v.tabrel::text
            ) LOOP
        
        /* init */
        v_dead_line := v_today - greatest( coalesce(i_lifespan, c.lifespan), c.lifespan );
    
        /* check */
        IF (c.arch_table IS NULL) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Not found the table %s in the arch schema %s', c.table_name, c.arch_schema) );
            CONTINUE;
        END IF;
        IF (c.live_table IS NULL) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Not found the table %s in the live schema %s', c.table_name, c.live_schema) );
            CONTINUE;
        END IF;
        IF NOT (c.arch_cols @> c.live_cols AND c.arch_cols <@ c.live_cols) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Mismatched structures of table %s in live and arch schemas (%s and %s)', c.table_name, c.live_schema, c.arch_schema) );
            CONTINUE;
        END IF;
        IF (array_position(c.arch_cols, c.dt_col) IS NULL) THEN 
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, 0, TRUE, 
                    format('Error. Not found column %s in the table %s in the live schema %s', c.dt_col, c.table_name, c.live_schema) );
            CONTINUE;
        END IF;
        
        /* dynamic sql */
        v_sql := CASE WHEN p_check_only 
                THEN format(
                    'SELECT count(*) FROM %s.%s WHERE %s < $1'
                    , c.live_schema, c.live_table, c.dt_col
                )
                ELSE format(
                    'WITH cleared AS (
                        DELETE FROM %s.%s WHERE %s < $1 RETURNING * 
                    ) 
                    , copied AS (
                        INSERT INTO %s.%s 
                        SELECT %s FROM cleared 
                        RETURNING %s
                    )
                    , copied_copied AS (
                        INSERT INTO %s.%s 
                        SELECT %s FROM copied
                        RETURNING %s
                    )
                    SELECT count(*) FROM copied_copied'
                    , c.live_schema, c.live_table, c.dt_col
                    , c.arch_schema, c.arch_table
                    , array_to_string(c.arch_cols, ','), array_to_string(c.arch_cols, ',')
                    , c.view_schema, c.view_table
                    , array_to_string(c.view_cols, ','), c.dt_col
                ) 
            END;
        RAISE INFO '%', v_sql;
        
        /* run */
        BEGIN            
            EXECUTE v_sql USING v_dead_line INTO v_num;
    
            /* log */
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, v_num, FALSE, 
                CASE v_num WHEN 0 
                    THEN format('Nothing to move from %s.%s to archive %s.%s / %s.%s'
                                , c.live_schema,c.live_table, c.arch_schema,c.arch_table, c.view_schema,c.view_table )
                    ELSE format('Moved %s rows from %s.%s to archive %s.%s / %s.%s'
                                , v_num, c.live_schema,c.live_table, c.arch_schema,c.arch_table, c.view_schema,c.view_table ) 
                END 
            );
        
            /* add to result */        
            v_res := v_res || jsonb_build_object (c.tabrel::text, v_num);
    
         EXCEPTION WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS
                v_err_state   = RETURNED_SQLSTATE,
                v_err_msg     = MESSAGE_TEXT,
                v_err_detail  = PG_EXCEPTION_DETAIL,
                v_err_hint    = PG_EXCEPTION_HINT,
                v_err_context = PG_EXCEPTION_CONTEXT;
            
            v_num := 0;
            /* log error */
            PERFORM swsystem.fnc_log_partition_cleanup( c.tabrel, v_dead_line, p_check_only, v_num, TRUE,
                    format('Error: %s (%s) Details: %s Hint: %s Context: %s', v_err_msg, v_err_state, v_err_detail, v_err_hint, v_err_context)
                );
            v_res := v_res || jsonb_build_object (c.tabrel::text, 'Error: '||SQLERRM );
        END;
        
        v_count := v_count + sign(v_num);
        v_total := v_total + 1;
    
    END LOOP;

    RETURN jsonb_build_object('status', CASE WHEN v_err_msg IS NULL THEN 'SUCCESS' ELSE 'FAIL' END) 
        || jsonb_build_object('cleaned', format('%s/%s', v_count, v_total)) 
        || jsonb_build_object('tables', v_res);

END $function$
;

CREATE OR REPLACE FUNCTION swsystem.fnc_remove_empty_partitions
(
    p_lifespan      text    DEFAULT NULL,
    p_check_only    bool    DEFAULT FALSE 
)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name:   fnc_remove_empty_partitions
    Purpose    : Check pathman partitions older than certain date (1 month ago usually, from config or <p_lifespan> - the oldest date) on row existance
                for a set of big tables listed and configured in the table "swsystem.cleanups"                
                and remove empty ones of them
    History    :
        1.0.0
            Date    : June 12, 2020
            Authors : Ales
            Notes   : Release (DEVOPS-9291)
        1.0.1
            Date    : 2022-01-19
            Authors : Ales
            Notes   : Set sorting order for old partitions and embedded SQL-errors handler (DEVOPS-16279)
             
    Sample run:
        SELECT swsystem.fnc_remove_empty_partitions(null, true);
        SELECT swsystem.fnc_remove_empty_partitions('3 MONTH', true);
        SELECT jsonb_pretty( swsystem.fnc_remove_empty_partitions() );
********************************************************************************************************/
DECLARE
    i_lifespan  interval    := p_lifespan::interval;
    v_res       jsonb       := '{}'::jsonb;   
    v_today     timestamp   := date_trunc('DAY', now() AT TIME ZONE 'UTC');
    c           record;  
    v_num       int;
    v_sql       text;
    v_part_name text;
    v_count     int         := 0;
    v_total     int         := 0;
    v_err_state   TEXT;
    v_err_msg     TEXT;
    v_err_detail  TEXT;
    v_err_hint    TEXT;
    v_err_context TEXT;
BEGIN
    
    FOR c IN (
                WITH tabs AS (
                    SELECT  tabrel, dt_col, lifespan,
                            (v_today - greatest(coalesce(i_lifespan, lifespan), lifespan)) AS dead_line 
                    FROM swsystem.partition_cleanup_config                
                    WHERE is_active 
                )              
                , parts AS (
                    SELECT parent AS tabrel, partition AS partrel, 
                        expr, range_min, range_max
                    FROM public.pathman_partition_list p                    
                    WHERE parttype = 2 
                )
                SELECT p.tabrel, p.partrel, v.dead_line,
                        p.expr AS dt_col, p.range_min, p.range_max
                FROM tabs v
                INNER JOIN parts p 
                    ON p.tabrel = v.tabrel 
                        AND p.range_max < (v.dead_line)::text
                        AND p.expr = v.dt_col
                ORDER BY v.tabrel::text
            ) LOOP
    BEGIN
        v_num := 0;
        v_part_name := c.partrel::text;
    
        /* dynamic sql */
        v_sql := format(
                    'SELECT count(*) FROM %s'
                    , c.partrel::text, c.dt_col
                );            
        --RAISE INFO '%', v_sql;
        
        /* run */ 
        EXECUTE v_sql INTO v_num;
        
        IF (v_num = 0 AND NOT p_check_only) THEN
            /* log */
            PERFORM swsystem.fnc_log_partition_cleanup( c.partrel, c.dead_line, p_check_only, v_num, FALSE, 
                    format('Partition %s of table %s is empty and being removed', c.partrel::text, c.tabrel::text) );                       
            
            /* remove partition */
            SELECT public.drop_range_partition(c.partrel, TRUE) --detach_range_partition(c.partrel);
                INTO v_part_name;
            
            /* save to history */
            INSERT INTO swsystem.partition_cleanup_history (partition_name, schema_name, table_name, dt_col_name, range_min, range_max)
            VALUES (v_part_name, split_part(c.tabrel::text, '.', 1), split_part(c.tabrel::text, '.', 2), c.dt_col, c.range_min, c.range_max);
        
            v_count := v_count + 1;
        ELSE /* not empty partition or just test*/
            /* log */
            v_err_msg := format('Partition %s of table %s has %s rows', c.partrel::text, c.tabrel::text, v_num);
            PERFORM swsystem.fnc_log_partition_cleanup( c.partrel, c.dead_line, p_check_only, v_num, TRUE, v_err_msg);
        END IF;
        
        /* add to result */        
        v_res := v_res || jsonb_build_object( v_part_name, CASE v_num WHEN 0 THEN 'removed' ELSE v_num::text||' rows' END );
    
     EXCEPTION WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS
            v_err_state   = RETURNED_SQLSTATE,
            v_err_msg     = MESSAGE_TEXT,
            v_err_detail  = PG_EXCEPTION_DETAIL,
            v_err_hint    = PG_EXCEPTION_HINT,
            v_err_context = PG_EXCEPTION_CONTEXT;
        /* log error */
        PERFORM swsystem.fnc_log_partition_cleanup( c.partrel, c.dead_line, p_check_only, v_num, TRUE,
                format('Error: %s (%s) Details: %s Hint: %s Context: %s', v_err_msg, v_err_state, v_err_detail, v_err_hint, v_err_context)
            );
        v_res := v_res || jsonb_build_object( v_part_name, 'Error: '||SQLERRM );
    END;

        v_total := v_total + 1;    
    END LOOP;

    RETURN jsonb_build_object('status', CASE WHEN v_err_msg IS NULL THEN 'SUCCESS' ELSE 'FAIL' END) 
        || jsonb_build_object('removed', format('%s/%s', v_count, v_total))
        || jsonb_build_object('partitions', v_res);

END;
$function$
;

--rollback select now();

--changeset dmitry.palaznik:2022-02-09-SWS-29117-entity-setting-flat-report runInTransaction:false
--comment Add entity settings flat report type
SET search_path = swmanagement;
ALTER TYPE enum_flat_report_type ADD VALUE IF NOT EXISTS 'es';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM pg_enum where enumlabel = 'es' and enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_flat_report_type');
--rollback RESET search_path;


--changeset pavel.shamshurov:2022-02-11-SWS-32883-add-permissions-for-displaying-aams-code
--comment Add permissions for displaying game aams code
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'entity:game:aams-code' where id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:game:aams-code' where id = 1;
UPDATE roles SET permissions = permissions || '["entity:game:aams-code", "keyentity:game:aams-code"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'entity:game:aams-code' where id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:game:aams-code' where id = 1;
--rollback RESET search_path;


--changeset sergey.malkov:2022-02-15-SWS-31163-Prepare-sw-db-update-to-remove-column-hidden-from-entity_games
--comment revert fnc_add_entity_games and remove column hidden from entity-games
SET search_path = swmanagement;
ALTER FUNCTION fnc_add_entity_games(int4, varchar[]) RENAME TO fnc_add_entity_games_before_4_79_0;
ALTER FUNCTION fnc_add_entity_games_before_4_70_0(int4, varchar[]) RENAME TO fnc_add_entity_games;
ALTER TABLE entity_games DROP COLUMN IF EXISTS hidden;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entity_games ADD COLUMN IF NOT EXISTS hidden BOOLEAN;
--rollback COMMENT ON COLUMN entity_games.hidden IS 'Marks game as hidden. Hidden games can be finished, but cannot be launched.';
--rollback ALTER FUNCTION fnc_add_entity_games(int4, varchar[]) RENAME TO fnc_add_entity_games_before_4_70_0;
--rollback ALTER FUNCTION fnc_add_entity_games_before_4_79_0 (int4, varchar[]) RENAME TO fnc_add_entity_games;
--rollback RESET search_path;


--changeset kirill.kaminskiy:2022-02-16-SWS-32917-keyentity-change-user-type
--comment Add new permissions to change user type for keyentity
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:user:change-type' where id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:user:change-type"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:user:change-type' where id = 1;
--rollback RESET search_path;


--changeset timur.luchkin:2022-02-17-DEVOPS-18378-Extended-pathman-functions endDelimiter:# stripComments:false
--comment Create extended version of sw_add_range_pathman_partitions
SET search_path TO public;

CREATE OR REPLACE FUNCTION sw_add_range_pathman_partitions_extended
   (p_parent_tab     REGCLASS  DEFAULT NULL::REGCLASS
   ,p_prc_till_end   INTEGER   DEFAULT 60
   ,p_range_interval INTERVAL  DEFAULT NULL::INTERVAL
   )
RETURNS TABLE(log_time timestamp without time zone, log_msg text)
LANGUAGE plpgsql AS $$
/*
*******************************************************************************

   Object Name:   sw_add_range_pathman_partitions_extended
   Purpose    :   Based on and extends functionality of "sw_add_range_pathman_partitions"
                    p_parent_tab - table (NULL = all partioned tables)
                    p_prc_till_end - latest partition filled percentage after which need to add new partition
                    p_range_interval - limit processing to specified interval only
   History    :
       2.0.0
           Date    : Feb 17, 2022
           Authors : Timur Luchkin
           Notes   : Initial release (needed by DEVOPS-18378)

   Sample calls:
      Create new partitions for all tables
        SELECT * FROM sw_add_range_pathman_partitions_extended();
      Create new partitions for specific table
        SELECT * FROM sw_add_range_pathman_partitions_extended('swmanagement.spins_history');
      Create new partitions for specific table, specific interval before ends latest partition
        SELECT * FROM sw_add_range_pathman_partitions_extended('swmanagement.spins_history', 80);
      Create new partitions for specific table, specific interval before ends latest partition
        SELECT * FROM sw_add_range_pathman_partitions_extended('swmanagement.audits', 60, interval '1 mon');

*******************************************************************************/
DECLARE
   v_rec           RECORD;
   v_range_max     TIMESTAMP;
   v_txt           VARCHAR;
BEGIN
   SET client_min_messages TO info;

   /* Check if MDB */
   IF (SELECT pg_is_in_recovery()) THEN
       log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
       RETURN;
   END IF;

   -- Loop trough all pg_pathman partitioned tables; 2018-10-16 exclude partioned tables that are in "_archive" schemas;2019-03-25 exclude rounds_history table
   FOR v_rec IN  (SELECT *
                  FROM  (SELECT parent, partition, range_min, range_max, range_interval, time_till_end_sec / range_interval_sec * 100.0 AS prc_till_end
                         FROM ( SELECT parent, partition, range_min, range_max, parttype, expr, range_interval
                                      ,Extract ('epoch' from range_max - clock_timestamp())  AS time_till_end_sec
                                      ,Extract ('epoch' from range_interval)                 AS range_interval_sec
                                FROM  (  SELECT Distinct ON (l.parent) parent, l.partition, l.range_min::TIMESTAMP AS range_min, l.range_max::TIMESTAMP AS range_max, l.parttype, l.expr, c.range_interval::INTERVAL AS range_interval
                                         FROM   pathman_partition_list l
                                                INNER JOIN pathman_config c ON l.parent = c.partrel
                                         WHERE  l.parttype = 2
                                           AND  NOT EXISTS (SELECT NULL
                                                            FROM   pg_catalog.pg_class AS cl
                                                                   JOIN pg_catalog.pg_namespace AS ns ON ns.oid = cl.relnamespace
                                                            WHERE  cl.oid = c.partrel
                                                              AND  ns.nspname ILIKE '%_archive'
                                                          )
                                           AND  l.parent != 'swmanagement.rounds_history'::REGCLASS
                                           AND  l.parent = Coalesce(p_parent_tab, l.parent)
                                         ORDER BY l.parent, l.range_max DESC
                                      ) t
                              ) t1
                        ) AS q
                   WHERE prc_till_end <= p_prc_till_end
                     AND range_interval = Coalesce(p_range_interval, range_interval)
                   LIMIT 1
                 )
   LOOP
      -- If not running autovacuum on parent table add new partition
      IF NOT EXISTS(SELECT * FROM pg_stat_activity WHERE query ilike '%vacuum%'||v_rec.parent::TEXT||' %') THEN
          SELECT append_range_partition(v_rec.parent, NULL, NULL) INTO v_txt;
          log_time := clock_timestamp(); log_msg := 'INFO: New partition '||v_txt||' created.'; RETURN NEXT;
      ELSE
          log_time := clock_timestamp(); log_msg := 'INFO: Partition is not created on '||v_rec.parent::TEXT||' as VACUUM is running on it.'; RETURN NEXT;
      END IF;
   END LOOP;

   RETURN;
END;
$$;

ALTER FUNCTION sw_add_range_pathman_partitions(p_parent_tab regclass, p_prc_till_end integer) RENAME TO sw_add_range_pathman_partitions__before_4_79_0;
ALTER FUNCTION sw_add_range_pathman_partitions__before_4_79_0 SET SCHEMA swbackup;

CREATE OR REPLACE FUNCTION sw_add_range_pathman_partitions
   (p_parent_tab     REGCLASS  DEFAULT NULL::REGCLASS
   ,p_prc_till_end   INTEGER   DEFAULT 60
   )
RETURNS TABLE(log_time timestamp without time zone, log_msg text)
LANGUAGE plpgsql AS $$
/*
*******************************************************************************

    Object Name:   public.sw_add_range_pathman_partitions
    Purpose    : Add range partitions to pg_pathman partitioned tables, except in archive schemas.
                p_parent_tab - table (NULL = all partioned tables)
                p_prc_till_end - latest partition filled percentage after which need to add new partition
    History    :
        1.2.1
            Date    : Feb 17, 2022
            Authors : Timur Luchkin
            Notes   : Use extended version sw_add_range_pathman_partitions_extended
        1.2.0
            Date    : Mar 25, 2019
            Authors : Valdis Akmens
            Notes   : Remove "rounds_history" table.
        1.1.0
            Date    : Oct 18, 2018
            Authors : Valdis Akmens
            Notes   : Adjust function to ignore "_archive" schemas, changed creation criteria from days before to current partition filled percentage (SWDB-74)
        1.0.0
            Date    : Jul 30, 2018
            Authors : Valdis Akmens
            Notes   : Add range partitions to pg_pathman partitioned tables, with default interval.
                p_parent_tab - table (NULL = all partioned tables)
                p_days_before - days before last partition ends(by default new partition is created 3 days before ends latest partition)
                Initial release(SWDB-58)
    Example    :
    -- Create new partitions for all tables
    SELECT * FROM public.sw_add_range_pathman_partitions();
    -- Create new partitions for specific table
    SELECT * FROM public.sw_add_range_pathman_partitions('swmanagement.spins_history');
    -- Create new partitions for specific table, specific interval before ends latest partition
    SELECT * FROM public.sw_add_range_pathman_partitions('swmanagement.spins_history', 60);
*******************************************************************************/
BEGIN
   RETURN QUERY
      SELECT log_time__, log_msg__ FROM public.sw_add_range_pathman_partitions_extended(p_parent_tab => p_parent_tab, p_prc_till_end => p_prc_till_end, p_range_interval => NULL::INTERVAL) AS t (log_time__, log_msg__);
END;
$$;

RESET search_path;

--rollback SET search_path TO public;
--rollback     DROP FUNCTION IF EXISTS sw_add_range_pathman_partitions(p_parent_tab regclass, p_prc_till_end integer);
--rollback     ALTER FUNCTION swbackup.sw_add_range_pathman_partitions__before_4_79_0(p_parent_tab regclass, p_prc_till_end integer) RENAME TO sw_add_range_pathman_partitions;
--rollback     ALTER FUNCTION swbackup.sw_add_range_pathman_partitions SET SCHEMA public;
--rollback     DROP FUNCTION sw_add_range_pathman_partitions_extended (p_parent_tab REGCLASS, p_prc_till_end INTEGER, p_range_interval INTERVAL);
--rollback RESET search_path;


--changeset kirill.kaminskiy:2022-02-17-SWS-32874-delete-redundant-live-games-permissions
--comment Remove redundant live games permissions
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'entity:game:add-live-game' where id = 1;
UPDATE roles SET permissions = permissions - 'entity:game:remove-live-game' where id = 1;
DELETE from permissions where code = 'entity:game:add-live-game';
DELETE from permissions where code = 'entity:game:remove-live-game';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'entity:game:add-live-game' where id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entity:game:remove-live-game' where id = 1;
--rollback UPDATE roles SET permissions = permissions || '["entity:game:add-live-game", "entity:game:remove-live-game"]'::jsonb WHERE id = 1;
--rollback INSERT INTO permissions values('entity:game:add-live-game', 'Add live games', now(), now());
--rollback INSERT INTO permissions values('entity:game:remove-live-game', 'Remove live games', now(), now());
--rollback RESET search_path;

--changeset mikhail.ivanov:2022-02-18-SWS-32866-prepare-for-currency-multipliers-change
--comment create game limits currencies table and add permission
SET search_path = swmanagement;

UPDATE roles SET permissions = permissions - 'game-limits-currencies' where id = 1;
UPDATE roles SET permissions = permissions - 'game-limits-currencies-update' where id = 1;
UPDATE roles SET permissions = permissions - 'game-limits-currencies-create' where id = 1;
UPDATE roles SET permissions = permissions - 'game-limits-currencies-delete' where id = 1;
UPDATE roles SET permissions = permissions || '["game-limits-currencies", "game-limits-currencies-update", "game-limits-currencies-create", "game-limits-currencies-delete"]'::jsonb WHERE id = 1;

CREATE TABLE IF NOT EXISTS game_limits_currencies (
    currency CHAR(3) NOT NULL,
    to_eur_multiplier INTEGER,
    copy_limits_from CHAR(3),
    version INTEGER NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    PRIMARY KEY (currency, version)
);

COMMENT ON TABLE game_limits_currencies IS 'Table to keep game limits currencies parameters';
COMMENT ON COLUMN game_limits_currencies.currency IS 'Currency code';
COMMENT ON COLUMN game_limits_currencies.to_eur_multiplier IS 'Currency toEURMultiplier';
COMMENT ON COLUMN game_limits_currencies.copy_limits_from IS 'Alternative currency from which limits can be copied';
COMMENT ON COLUMN game_limits_currencies.version IS 'Version of game limits currency parameters set';

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'game-limits-currencies' where id = 1;
--rollback UPDATE roles SET permissions = permissions - 'game-limits-currencies-update' where id = 1;
--rollback UPDATE roles SET permissions = permissions - 'game-limits-currencies-create' where id = 1;
--rollback UPDATE roles SET permissions = permissions - 'game-limits-currencies-delete' where id = 1;
--rollback DROP TABLE IF EXISTS game_limits_currencies;
--rollback RESET search_path;

--changeset mikhail.ivanov:2022-02-22-SWS-SWS-33021-implement-adding-lower-stakes-of-aligned-currencies
--comment add new 'lower_stakes'column in stake_ranges table, comment it and fill data
--comment fill game_limits_currencies table with data
SET search_path = swmanagement;

ALTER TABLE stake_ranges ADD COLUMN lower_stakes JSONB;
COMMENT ON COLUMN stake_ranges.lower_stakes IS 'Lower coin bets range';
UPDATE stake_ranges SET lower_stakes = '[0.01, 0.02, 0.03, 0.04, 0.05, 0.08]' WHERE currency IN ('ARS', 'CNY', 'DKK', 'HKD', 'HRK', 'MAD', 'MOP', 'NOK', 'SEK', 'VEF', 'ZAR', 'ZMW');
UPDATE stake_ranges SET lower_stakes = '[0.01, 0.03, 0.05, 0.1, 0.2]' WHERE currency = 'MXN';
UPDATE stake_ranges SET lower_stakes = '[0.01, 0.02, 0.03, 0.04]' WHERE currency = 'RON';
UPDATE stake_ranges SET lower_stakes = '[0.05, 0.1, 0.15, 0.2, 0.25, 0.3]' WHERE currency = 'UAH';

DELETE FROM game_limits_currencies;

INSERT INTO game_limits_currencies(currency, to_eur_multiplier, copy_limits_from, version, created_at, updated_at) VALUES
('EUR', 1, NULL, 1, now(), now()),
('USD', 1, 'EUR', 1, now(), now()),
('GBP', 1, NULL, 1, now(), now()),
('AUD', 1, NULL, 1, now(), now()),
('AZN', 1, NULL, 1, now(), now()),
('BGN', 1, NULL, 1, now(), now()),
('BND', 1, NULL, 1, now(), now()),
('CAD', 1, NULL, 1, now(), now()),
('CHF', 1, NULL, 1, now(), now()),
('GEL', 1, NULL, 1, now(), now()),
('NZD', 1, NULL, 1, now(), now()),
('SGD', 1, NULL, 1, now(), now()),
('BMD', 1, NULL, 1, now(), now()),
('BRL', 5, NULL, 1, now(), now()),
('ILS', 5, NULL, 1, now(), now()),
('MYR', 5, NULL, 1, now(), now()),
('PEN', 5, NULL, 1, now(), now()),
('PLN', 5, NULL, 1, now(), now()),
('TRY', 5, NULL, 1, now(), now()),
('GHS', 5, NULL, 1, now(), now()),
('TND', 5, 'ILS', 1, now(), now()),
('ARS', 10, NULL, 1, now(), now()),
('CNY', 10, NULL, 1, now(), now()),
('DKK', 10, NULL, 1, now(), now()),
('HKD', 10, NULL, 1, now(), now()),
('HRK', 10, NULL, 1, now(), now()),
('MAD', 10, NULL, 1, now(), now()),
('MOP', 10, NULL, 1, now(), now()),
('NOK', 10, NULL, 1, now(), now()),
('SEK', 10, NULL, 1, now(), now()),
('VEF', 10, NULL, 1, now(), now()),
('ZAR', 10, NULL, 1, now(), now()),
('ZMW', 10, NULL, 1, now(), now()),
('CZK', 50, NULL, 1, now(), now()),
('DOP', 50, NULL, 1, now(), now()),
('HNL', 50, NULL, 1, now(), now()),
('INR', 50, NULL, 1, now(), now()),
('KGS', 50, NULL, 1, now(), now()),
('MDL', 50, NULL, 1, now(), now()),
('NIO', 50, NULL, 1, now(), now()),
('PHP', 50, NULL, 1, now(), now()),
('RUB', 50, NULL, 1, now(), now()),
('THB', 50, NULL, 1, now(), now()),
('TWD', 50, NULL, 1, now(), now()),
('UAH', 50, NULL, 1, now(), now()),
('UYU', 50, NULL, 1, now(), now()),
('VES', 50, NULL, 1, now(), now()),
('ISK', 100, NULL, 1, now(), now()),
('JPY', 100, NULL, 1, now(), now()),
('RSD', 100, NULL, 1, now(), now()),
('KES', 100, NULL, 1, now(), now()),
('MZN', 100, 'ISK', 1, now(), now()),
('CLP', 500, NULL, 1, now(), now()),
('HUF', 500, NULL, 1, now(), now()),
('KZT', 500, NULL, 1, now(), now()),
('XOF', 500, NULL, 1, now(), now()),
('CRC', 500, NULL, 1, now(), now()),
('AMD', 500, NULL, 1, now(), now()),
('AOA', 500, 'CLP', 1, now(), now()),
('NGN', 500, 'CLP', 1, now(), now()),
('XAF', 500, 'CLP', 1, now(), now()),
('KRW', 1000, NULL, 1, now(), now()),
('COP', 1000, NULL, 1, now(), now()),
('MNT', 1000, NULL, 1, now(), now()),
('TZS', 1000, NULL, 1, now(), now()),
('MMK', 1000, NULL, 1, now(), now()),
('CDF', 1000, 'COP', 1, now(), now()),
('MWK', 1000, 'COP', 1, now(), now()),
('RWF', 1000, 'COP', 1, now(), now()),
('PYG', 5000, NULL, 1, now(), now()),
('UGX', 5000, 'PYG', 1, now(), now()),
('IDR', 10000, NULL, 1, now(), now()),
('GNF', 10000, 'IDR', 1, now(), now()),
('SLL', 10000, 'IDR', 1, now(), now()),
('VND', 25000, NULL, 1, now(), now()),
('IDS', 10, NULL, 1, now(), now()),
('RUP', 10, NULL, 1, now(), now()),
('VNS', 25, NULL, 1, now(), now()),
('VDO', 25, NULL, 1, now(), now()),
('MXN', 30, NULL, 1, now(), now()),
('BNS', 1000, NULL, 1, now(), now()),
('RON', 5, NULL, 1, now(), now()),
('GEL', 5, NULL, 2, now(), now()),
('VES', 5, NULL, 2, now(), now()),
('TRY', 10, NULL, 2, now(), now()),
('RUB', 100, NULL, 2, now(), now()),
('INR', 100, NULL, 2, now(), now()),
('KGS', 100, NULL, 2, now(), now()),
('ARS', 100, NULL, 2, now(), now()),
('CLP', 1000, NULL, 2, now(), now()),
('COP', 5000, NULL, 2, now(), now()),
('VEF', 25000, NULL, 2, now(), now());

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE stake_ranges DROP COLUMN lower_stakes;
--rollback DELETE FROM game_limits_currencies;
--rollback RESET search_path;


--changeset pavel.shamshurov:2022-02-28-SWS-33230
--comment add permissions to control access to pages
SET search_path = swmanagement;

UPDATE roles SET permissions = permissions - 'hub:engagement:tournaments' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'hub:engagement:prize-drops' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'hub:engagement:must-win-jackpots' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["hub:engagement:tournaments", "hub:engagement:prize-drops", "hub:engagement:must-win-jackpots"]'::jsonb WHERE id = 1;

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'hub:engagement:tournaments' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'hub:engagement:prize-drops' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'hub:engagement:must-win-jackpots' WHERE id = 1;
--rollback RESET search_path;

--changeset mikhail.ivanov:2022-03-01-SFB-907-cd/cd2-update-currency-multipliers
--comment add new currencies into game_limits_currencies table
SET search_path = swmanagement;

DELETE FROM game_limits_currencies WHERE currency IN ('AOA', 'NGN', 'XAF', 'CDF', 'MWK', 'RWF') AND version = 2;

INSERT INTO game_limits_currencies(currency, to_eur_multiplier, copy_limits_from, version, created_at, updated_at) VALUES
('AOA', 1000, NULL, 2, now(), now()),
('NGN', 1000, NULL, 2, now(), now()),
('XAF', 1000, NULL, 2, now(), now()),
('CDF', 5000, NULL, 2, now(), now()),
('MWK', 5000, NULL, 2, now(), now()),
('RWF', 5000, NULL, 2, now(), now());

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM game_limits_currencies WHERE currency IN ('AOA', 'NGN', 'XAF', 'CDF', 'MWK', 'RWF') AND version = 2;
--rollback RESET search_path;
