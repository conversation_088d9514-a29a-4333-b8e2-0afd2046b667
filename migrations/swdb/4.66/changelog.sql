--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset aleh.rudzko:2021-08-02_SWS-28051-allow-to-create-entity-without-country
--comment Drop not null constraint from entities table for default_country field
SET search_path = swmanagement;
ALTER TABLE entities ALTER COLUMN default_country DROP NOT NULL;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entities ALTER COLUMN default_country SET NOT NULL;
--rollback RESET search_path;