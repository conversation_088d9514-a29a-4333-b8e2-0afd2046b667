--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset dmitriy.palaznik:2021-06-01-SWS-24715-flat-reports
--comment Add sequence, type, table, index for flat reports

SET search_path = swmanagement;

CREATE TYPE enum_flat_report_type AS ENUM (
    'rtp',
    'ols',
    'nls'
);

CREATE TABLE flat_reports
(
	id bigserial NOT NULL,
	entity_id integer NOT NULL,
	report jsonb NOT NULL,
	report_type enum_flat_report_type NOT NULL,
	game_code character varying(255) NOT NULL,
	currency character(3),
	created_at timestamp without time zone NOT NULL,
	updated_at timestamp without time zone NOT NULL
);

COMMENT ON TABLE flat_reports IS 'Flat reports for Game Limits (OLS, NLS) or RTP';
COMMENT ON COLUMN flat_reports.id IS 'Flat report ID';
COMMENT ON COLUMN flat_reports.entity_id IS 'Reference to entity table';
COMMENT ON COLUMN flat_reports.report IS 'Flat report data represented as JSON';
COMMENT ON COLUMN flat_reports.report_type IS 'Flat report type';
COMMENT ON COLUMN flat_reports.game_code IS 'Game code which assosiated with a report';
COMMENT ON COLUMN flat_reports.currency IS 'Currency which assosiated with a report';
COMMENT ON COLUMN flat_reports.created_at IS 'Created at date';
COMMENT ON COLUMN flat_reports.updated_at IS 'Updated at date';

CREATE INDEX idx_flat_reports_entity_id_game_code_currency ON flat_reports USING btree (entity_id, game_code, currency);

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP INDEX idx_flat_reports_entity_id_game_code_currency;
--rollback DROP TABLE flat_reports;
--rollback DROP TYPE enum_flat_report_type;
--rollback RESET search_path;

--changeset valdis.akmens:2021-06-03-SWS-28011-ext_bet_win_history-partitioning_new_index runInTransaction:false
--comment Created an index on the table ext_bet_win_history for its partitioning by the field
SET search_path = swadaptergos;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ext_bet_win_inserted_at ON ext_bet_win_history (inserted_at);
RESET search_path;
--rollback SET search_path = swadaptergos;
--rollback DROP INDEX idx_ext_bet_win_inserted_at;
--rollback RESET search_path;


--changeset valdis.akmens:2021-06-03-SWS-28011-ext_bet_win_history-partitioning
--comment Enable partitioning on ext_bet_win_history
SET search_path TO swadaptergos;
ALTER TABLE ext_bet_win_history ALTER COLUMN inserted_at SET NOT NULL;
RESET search_path;

SELECT public.set_init_callback (
                        relation        => 'swadaptergos.ext_bet_win_history'::regclass,
                        callback        => 'public.pathman_callback(jsonb)' );

SELECT public.create_range_partitions (
                        parent_relid    => 'swadaptergos.ext_bet_win_history'::regclass,
                        expression      => 'inserted_at',
                        start_value     => (SELECT date_trunc('month',COALESCE(min(inserted_at),now()))::date FROM swadaptergos.ext_bet_win_history),
                        p_interval      => '1 month'::interval,
                        p_count         => (SELECT CASE WHEN NOT EXISTS(SELECT * FROM swadaptergos.ext_bet_win_history) THEN 1 ELSE NULL END),
                        partition_data  => FALSE );

--rollback SELECT public.drop_partitions(parent_relid => 'swadaptergos.ext_bet_win_history'::regclass);


--changeset aleksey.stepanov:2021-06-08_SWS-27863_new_index_on_games runInTransaction:false
--comment Increase timeout for GET /v1/physical-tables?limit=10000
SET search_path = swmanagement;
CREATE INDEX IF NOT EXISTS idx_games_physical_table_id ON games (physical_table_id);
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP INDEX idx_games_physical_table_id;
--rollback RESET search_path;


--changeset aleh.rudzko:2021-06-11-SWS-23558-implement-patch-of-stake-ranges
--comment Add new permission to master
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'stake-range-update' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["stake-range-update"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'stake-range-update' WHERE id = 1;
--rollback RESET search_path;

--changeset sergey.malkov:2021-06-14-SWS-18345-Minimize-amount-of-duplicates-transaction-exceptions endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Modify function fnc_save_wallet_operation_log - add output of duplicates ids
SET search_path TO swmanagement;

ALTER FUNCTION fnc_save_wallet_operation_log (IN swmanagement.wallet_operation_log[], OUT INTEGER, OUT INTEGER) RENAME TO fnc_save_wallet_operation_log_4_62;

CREATE OR REPLACE FUNCTION fnc_save_wallet_operation_log (IN input_array swmanagement.wallet_operation_log[], OUT inserted INTEGER, OUT duplicates INTEGER, OUT duplicate_ids BIGINT[])
AS
$$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Purpose    : Insert data to the "wallet_operation_log"
   History    :
      1.0.0
         Date    : Feb 18, 2019
         Authors : Timur Luchkin
         Notes   : Initial release
      1.1.0
         Date    : Jun 07, 2019
         Authors : Valdis Akmens
         Notes   : Add output of duplicates ids(SWS-18345)

   Parameters:
      Input batch structure is a set of arrays of next fields (order is important!!!):
         id,operation_id,operation_name,public_id,external_trx_id,is_external,game_id,ts,version,data,params,inserted_at,committed_at
         Note: input value of "inserted_at" field will be overriden by the local assignment

   Sample insert:
      SELECT inserted, duplicates, duplicate_ids
      FROM   fnc_save_wallet_operation_log(
         '{
            "(**********,0,play,OHj9VA7ZsicAAALgOHj9VfFy8n4=,,t,*************,\"2020-02-12 08:16:58.197\",1,\"[{\"\"value\"\": null, \"\"amount\"\": -80, \"\"account\"\": \"\"main\"\", \"\"trxType\"\": \"\"bet\"\", \"\"property\"\": \"\"balance\"\", \"\"prevValue\"\": null, \"\"walletKey\"\": \"\"player:46:PERF_STARS_b12_01_0049_INT:USD\"\"}, {\"\"value\"\": null, \"\"amount\"\": 0, \"\"account\"\": \"\"main\"\", \"\"trxType\"\": \"\"win\"\", \"\"property\"\": \"\"balance\"\", \"\"prevValue\"\": null, \"\"walletKey\"\": \"\"player:46:PERF_STARS_b12_01_0049_INT:USD\"\"}]\",\"{\"\"isTest\"\": false, \"\"gameCode\"\": \"\"sw_qv\"\", \"\"roundEnded\"\": true, \"\"freeBetCoin\"\": null, \"\"balanceAfter\"\": 10000, \"\"balanceBefore\"\": 10000.8, \"\"gameSessionId\"\": \"\"*************\"\"}\",\"2020-02-12 08:16:58.689\",\"2020-02-12 08:16:58.21\")",
            "(**********,0,play,Oum0+Q8ClGoAAALgOum0+Hta4C4=,,t,*************,\"2020-02-12 19:39:19.672\",1,\"[{\"\"value\"\": null, \"\"amount\"\": -20, \"\"account\"\": \"\"main\"\", \"\"trxType\"\": \"\"bet\"\", \"\"property\"\": \"\"balance\"\", \"\"prevValue\"\": null, \"\"walletKey\"\": \"\"player:287:PERF_RELAX_MOCK_a1_01_0002_EXT:USD\"\"}, {\"\"value\"\": null, \"\"amount\"\": 0, \"\"account\"\": \"\"main\"\", \"\"trxType\"\": \"\"win\"\", \"\"property\"\": \"\"balance\"\", \"\"prevValue\"\": null, \"\"walletKey\"\": \"\"player:287:PERF_RELAX_MOCK_a1_01_0002_EXT:USD\"\"}]\",\"{\"\"isTest\"\": false, \"\"gameCode\"\": \"\"sw_wr\"\", \"\"roundEnded\"\": true, \"\"freeBetCoin\"\": null, \"\"balanceAfter\"\": 10000, \"\"balanceBefore\"\": 10000.2, \"\"gameSessionId\"\": \"\"*************\"\"}\",\"2020-02-12 19:39:20.394\",\"2020-02-12 19:39:19.684\")"
         }'
      ) AS t(inserted, duplicates, duplicate_ids);

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   rec                     swmanagement.wallet_operation_log;
   init_ins                BOOLEAN := true;
   batch_days              RECORD;
   live_table              REGCLASS := 'swmanagement.wallet_operation_log'::REGCLASS;
   archive_table           REGCLASS := 'swmanagement_archive.wallet_operation_log'::REGCLASS;
   target_partition        REGCLASS;
   debug_enabled           BOOLEAN  := false;
   rows_inserted           INTEGER;
BEGIN
   inserted := 0;
   duplicates := 0;

   -- First try direct insert to the live parent table (assign local "inserted_at" here)
   BEGIN
      INSERT INTO swmanagement.wallet_operation_log (id,operation_id,operation_name,public_id,external_trx_id,is_external,game_id,ts,version,data,params,inserted_at,committed_at)
         SELECT id,operation_id,operation_name,public_id,external_trx_id,is_external,game_id,ts,version,data,params,now() AS inserted_at, committed_at
         FROM unnest(input_array);
      GET DIAGNOSTICS inserted = ROW_COUNT;

   EXCEPTION
      WHEN unique_violation THEN
         init_ins := false;
         IF debug_enabled THEN
            SET client_min_messages TO INFO;
            RAISE INFO 'UNIQUE VIOLATION EXCEPTION';
            RESET client_min_messages;
         END IF;

      WHEN others THEN
         -- pathman partition not found in Live table
         IF SQLSTATE = 'XX000' AND SQLERRM ~* 'no suitable partition for key' THEN
            init_ins := false;
         ELSE
            RAISE EXCEPTION
               USING ERRCODE = SQLSTATE
                    ,MESSAGE = SQLERRM;
         END IF;

         IF debug_enabled THEN
            SET client_min_messages TO INFO;
            RAISE INFO 'OTHER EXCEPTION (pathman related=%): % - %', NOT init_ins, SQLSTATE, SQLERRM;
            RESET client_min_messages;
         END IF;
   END;

   IF NOT init_ins THEN
   -- Breakdown batch by days to insert directly to the partition
      FOR batch_days IN SELECT ts::DATE AS grp_day, count(*) AS grp_cnt FROM unnest(input_array) GROUP BY ts::DATE
      LOOP
         SELECT partition
         FROM   pathman_partition_list
         WHERE  parent IN (live_table, archive_table)
           AND  batch_days.grp_day >= range_min::DATE
           AND  batch_days.grp_day < range_max::DATE
         INTO STRICT target_partition;

         FOR rec IN SELECT * FROM unnest(input_array) WHERE ts::DATE = batch_days.grp_day ORDER BY ts
         -- TODO: Optimization possible: Insert whole day as a single sub-batch.
         LOOP
            -- HINT: Do nothing and Foreign tables: https://postgresrocks.enterprisedb.com/t5/The-Knowledgebase/quot-INSERT-ON-CONFLICT-DO-NOTHING-quot-syntax-gotchas-with/ta-p/3042
            rec.inserted_at := now();
            EXECUTE 'INSERT INTO '||target_partition::TEXT||'(id,operation_id,operation_name,public_id,external_trx_id,is_external,game_id,ts,version,data,params,inserted_at,committed_at) SELECT $1.* ON CONFLICT DO NOTHING' USING rec;
            GET DIAGNOSTICS rows_inserted = ROW_COUNT;

            IF rows_inserted = 0 THEN
            -- Save to duplicates
               duplicates := duplicates + 1;
               duplicate_ids:= duplicate_ids || rec.id;

               -- Skip "pk" here
               INSERT INTO swmanagement.wallet_operation_log_duplicates (id,operation_id,operation_name,public_id,external_trx_id,is_external,game_id,ts,version,data,params,inserted_at,committed_at)
                  SELECT rec.id,rec.operation_id,rec.operation_name,rec.public_id,rec.external_trx_id,rec.is_external,rec.game_id,rec.ts,rec.version,rec.data,rec.params,rec.inserted_at,rec.committed_at;
            ELSE
            -- All good
               inserted := inserted + 1;
            END IF;
         END LOOP;
      END LOOP;
   END IF;
END
$$
LANGUAGE plpgsql;

GRANT EXECUTE ON FUNCTION fnc_save_wallet_operation_log (IN swmanagement.wallet_operation_log[], OUT INTEGER, OUT INTEGER, OUT duplicate_ids INTEGER[]) TO swmanagement;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_save_wallet_operation_log (IN swmanagement.wallet_operation_log[], OUT INTEGER, OUT INTEGER, OUT duplicate_ids INTEGER[]);
--rollback ALTER FUNCTION fnc_save_wallet_operation_log_4_62 RENAME TO fnc_save_wallet_operation_log;
--rollback RESET search_path;

--changeset aleh.rudzko:2021-07-05-SWS-28661-incorrect-promo-id-when-using-bonus-api-method
--comment Add promo id field to def payments table
SET search_path = swdeferredpmnt;
ALTER TABLE deferred_payments ADD COLUMN promo_id VARCHAR(255);
RESET search_path;

--rollback SET search_path = swdeferredpmnt;
--rollback ALTER TABLE deferred_payments DROP COLUMN promo_id;
--rollback RESET search_path;