--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset mircea.hanghiuc:2023-07-17-SWS-41082-allow-external-provider-send-bonus-payments runInTransaction:false
--comment add external to enum_deferred_payment_registrations_source
SET search_path = swdeferredpmnt;
ALTER TYPE enum_deferred_payment_registrations_source ADD VALUE IF NOT EXISTS 'external';
RESET search_path;

--rollback SELECT NOW();
