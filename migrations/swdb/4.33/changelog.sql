--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vera.kruhl<PERSON><PERSON>:2020-02-28-SWS-XXXX-start-release-4.33.0
--comment label for 4.33.0
select now();
--rollback select now();


--changeset vera.kruhl<PERSON>kova:2020-02-28-SWS-11111-promotion-player-update
--comment Create table to store player promotion updates
SET search_path TO swmanagement;
CREATE SEQUENCE IF NOT EXISTS promotion_players_update_id_seq;
GRANT USAGE ON SEQUENCE promotion_players_update_id_seq TO swmanagement;
CREATE TABLE IF NOT EXISTS promotion_players_update (
	id integer NOT NULL DEFAULT nextval('promotion_players_update_id_seq') PRIMARY KEY,
	player_code CHARACTER VARYING(255) NOT NULL,
	promotion_id INTEGER NOT NULL,
	status CHARACTER VARYING(16),
	finish_status CHARACTER VARYING(16),
	played_at TIMESTAMP WITHOUT TIME ZONE,
	finished_at TIMESTAMP WITHOUT TIME ZONE,
	inserted_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now(),

	CONSTRAINT promotion_players_update_promotion_id_fkey FOREIGN KEY (promotion_id)
            REFERENCES promotions (id) MATCH SIMPLE
			ON UPDATE NO ACTION
			ON DELETE NO ACTION
);
CREATE INDEX IF NOT EXISTS idx_promotion_players_update_promotion_id_player_code ON promotion_players_update USING btree (promotion_id, player_code);
GRANT SELECT,INSERT,UPDATE,DELETE ON promotion_players_update TO swmanagement;

COMMENT ON COLUMN promotion_players_update.id IS 'Sequential identifier';
COMMENT ON COLUMN promotion_players_update.player_code IS 'Player code';
COMMENT ON COLUMN promotion_players_update.promotion_id IS 'Promotion identifier';
COMMENT ON COLUMN promotion_players_update.status IS 'Player promotion status to be updated in promotion_players';
COMMENT ON COLUMN promotion_players_update.finish_status IS 'Player promotion finish status to be updated in promotion_players';
COMMENT ON COLUMN promotion_players_update.played_at IS 'Timestamp when player first played promotion';
COMMENT ON COLUMN promotion_players_update.finished_at IS 'Timestamp when player finished promotion';
COMMENT ON COLUMN promotion_players_update.inserted_at IS 'Timestamp when row was inserted';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP TABLE promotion_players_update; DROP SEQUENCE IF EXISTS promotion_players_update_id_seq;
--rollback RESET search_path;


--changeset valdis.akmens:2020-03-04-DEVOPS-8226-job-to-update-promotion_players endDelimiter:# stripComments:false
--comment Create Postgres job to update promotion_players from promotion_players_update table
SET search_path TO swmanagement;
CREATE OR REPLACE FUNCTION fnc_upd_promotion_players()
RETURNS TABLE(log_time timestamp without time zone, log_msg text)
LANGUAGE plpgsql
AS $function$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

    Object Name:   fnc_upd_promotion_players
    Purpose    :   Update promotion_players from promotion_players_update table.
                    1. select record from promotion_players_update
                    2. select original record from promotion_players matching by (player_code, promotion_id)
                    3. if promotion_players_update.inserted_at > promotion_players.updated_at
                    3.1 if promotion_players_update.status not null, update promotion_players.status with this value
                    3.2 if promotion_players_update.finish_status not null, update promotion_players.finish_status with this value
                    3.3 if promotion_players_update.played_at not null, update promotion_players.played_at with this value
                    3.4 if promotion_players_update.finished_at not null, update promotion_players.finished_at with this value
                    4. remove record from promotion_players_update
    History    :
        1.0.0
            Date    : Mar 02, 2020
            Authors : Valdis Akmens
            Notes   : Initial release (DEVOPS-8226)

    Sample run:
        SELECT * FROM fnc_upd_promotion_players();
~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
    v_rec           RECORD; 
    v_counter       INT:=0;
BEGIN
    log_time := clock_timestamp(); log_msg := 'INFO: Start update promotion_players '; RETURN NEXT;

    /* Check if MDB */
    IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
    END IF;

    FOR v_rec IN (SELECT * FROM promotion_players_update ORDER BY inserted_at ASC) 
    LOOP 
        -- log_time := clock_timestamp(); log_msg := 'INFO: Processing record '||v_rec; RETURN NEXT;
        
        -- Update records that have changes 
        UPDATE promotion_players AS pp SET 
            status          = COALESCE(v_rec.status,pp.status),
            finish_status   = COALESCE(v_rec.finish_status,pp.finish_status),
            played_at       = COALESCE(v_rec.played_at, pp.played_at),
            finished_at     = COALESCE(v_rec.finished_at, pp.finished_at)
        WHERE 
            pp.player_code = v_rec.player_code
        AND pp.promotion_id = v_rec.promotion_id
        AND v_rec.inserted_at > pp.updated_at;
        
        -- Clean promotion_players_update table
        DELETE FROM promotion_players_update AS ppu
        WHERE 
            ppu.player_code = v_rec.player_code
        AND ppu.promotion_id = v_rec.promotion_id
        AND ppu.inserted_at  = v_rec.inserted_at;
        v_counter:= v_counter + 1;
    
    END LOOP;

    log_time := clock_timestamp(); log_msg := 'INFO: Finish update promotion_players. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

    RETURN;
END;
$function$;

INSERT INTO cron.job ( schedule, command, nodename, nodeport, database, username, active)
VALUES('* * * * *', 'SET SESSION search_path TO swmanagement; SELECT * FROM fnc_upd_promotion_players();', 'localhost', (SELECT setting FROM pg_catalog.pg_settings WHERE name = 'port')::INT, 'swdb', 'swsystem', true);

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_upd_promotion_players(); DELETE FROM cron.job WHERE command = 'SET SESSION search_path TO swmanagement; SELECT * FROM fnc_upd_promotion_players();';
--rollback RESET search_path;
