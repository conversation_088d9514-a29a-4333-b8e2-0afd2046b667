--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset anastasia.kostyukova:2020-06-15-SWS-XXXX-start-release-4.40.0
--comment label for 4.40.0
select now();
--rollback select now();

--changeset anastasia.kostyukova:2020-06-15-SWS-18435-rtp-audit
--comment Add new table game rtp history
SET search_path = swmanagement;

CREATE SEQUENCE game_rtp_history_id_seq;
CREATE TABLE IF NOT EXISTS game_rtp_history (
	id INTEGER NOT NULL DEFAULT nextval('game_rtp_history_id_seq') PRIMARY KEY,
	entity_id INTEGER,
	game_id INTEGER,
	rtp_info JSONB NOT NULL,
	rtp_deduction JSONB,
	ts TIMESTAMP NOT NULL,

  FOREIGN KEY (entity_id) REFERENCES entities(id)
);

ALTER SEQUENCE game_rtp_history_id_seq OWNER TO swmanagement;
ALTER TABLE game_rtp_history OWNER TO swmanagement;

COMMENT ON TABLE game_rtp_history IS 'Audit of changing rtp info and rtp deduction info';
COMMENT ON COLUMN game_rtp_history.rtp_info IS 'JSON with rtp info, stored in game features and entity game settings';
COMMENT ON COLUMN game_rtp_history.rtp_deduction IS 'JSON with rtp deduction info, stored in entity game settings';

COMMENT ON COLUMN game_rtp_history.entity_id IS 'Refference to entity table';
COMMENT ON COLUMN game_rtp_history.game_id IS 'Refference to game table, foreign key is removed for performance reason';
COMMENT ON COLUMN game_rtp_history.ts IS 'Date of creation';

CREATE INDEX IF NOT EXISTS idx_game_rtp_history_entity_id_game_id ON game_rtp_history USING btree (entity_id, game_id);

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP TABLE game_rtp_history;
--rollback DROP SEQUENCE game_rtp_history_id_seq;
--rollback DROP INDEX IF EXISTS idx_game_rtp_history_entity_id_game_id;
--rollback RESET search_path;

--changeset stepanov.alekey:2020-06-17-SWS-15489
--comment Add api to store Favorite games per Player
SET search_path = swmanagement;
ALTER TABLE favorite_games_by_player DROP COLUMN count;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE favorite_games_by_player ADD COLUMN count INTEGER NOT NULL DEFAULT 1;
--rollback RESET search_path;

--changeset anastasia.kostyukova:2020-06-15-SWS-18439-rtp-audit endDelimiter:# stripComments:false
--comment Fill game rtp history table, Add game code to game rtp history
SET search_path = swmanagement;
--comment Add game code to game rtp history
ALTER TABLE game_rtp_history ADD COLUMN game_code VARCHAR(255);
COMMENT ON COLUMN game_rtp_history.game_code IS 'Game code, added to avoid joining with game table';

--comment Fill game rtp history table
DO
$$
  DECLARE
    raw record;
    rtpInfo jsonb;
BEGIN
--fill game audit
  FOR raw IN
    SELECT id, code,
        features ->> 'baseRTP' as baseRTP,
        features ->> 'baseRTPRange' as baseRTPRange,
        features ->> 'jpRTP' as jpRTP,
        features ->> 'featuresRTP' as featuresRTP,
        features ->> 'supportsRtpConfigurator' as supportsRtpConfigurator
    FROM games
    WHERE features ->> 'baseRTP' IS NOT NULL OR
        features ->> 'baseRTPRange' IS NOT NULL OR
        features ->> 'jpRTP' IS NOT NULL OR
        features ->> 'featuresRTP' IS NOT NULL OR
        features ->> 'supportsRtpConfigurator' IS NOT NULL
  LOOP
    -- get only rtp related information
    rtpInfo = jsonb_build_object(
      'baseRTP', raw.baseRTP,
      'baseRTPRange', raw.baseRTPRange,
      'jpRTP', raw.jpRTP,
      'featuresRTP', raw.featuresRTP,
      'supportsRtpConfigurator', raw.supportsRtpConfigurator
    );
    INSERT INTO game_rtp_history(id, entity_id, game_id, game_code, rtp_info, rtp_deduction, ts)
    VALUES (default, null, raw.id, raw.code, jsonb_strip_nulls(rtpInfo), null, now());
  END LOOP;

  -- fill entity game audit
  FOR raw IN
    SELECT eg.game_id, eg.entity_id, g.code,
        features ->> 'baseRTP' as baseRTP,
        features ->> 'baseRTPRange' as baseRTPRange,
        features ->> 'jpRTP' as jpRTP,
        features ->> 'featuresRTP' as featuresRTP,
        features ->> 'supportsRtpConfigurator' as supportsRtpConfigurator,
        eg.settings ->> 'rtpConfigurator' as rtpConfigurator
    FROM games g
    INNER JOIN entity_games eg ON  g.id = eg.game_id
    WHERE eg.settings ->> 'rtpConfigurator' IS NOT NULL
  LOOP
    -- get only rtp related information
    rtpInfo = jsonb_build_object(
      'baseRTP', raw.baseRTP,
      'baseRTPRange', raw.baseRTPRange,
      'jpRTP', raw.jpRTP,
      'featuresRTP', raw.featuresRTP,
      'supportsRtpConfigurator', raw.supportsRtpConfigurator
    );

    INSERT INTO game_rtp_history(id, entity_id, game_id, game_code, rtp_info, rtp_deduction, ts)
    VALUES (default, raw.entity_id, raw.game_id, raw.code, jsonb_strip_nulls(rtpInfo), raw.rtpConfigurator::jsonb, now());
  END LOOP;

END
$$
LANGUAGE plpgsql;

--comment Make game code required
ALTER TABLE game_rtp_history ALTER COLUMN game_code SET NOT NULL;

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE game_rtp_history DROP COLUMN game_code;
--rollback TRUNCATE TABLE game_rtp_history;
--rollback RESET search_path;

--changeset anastasia.kostyukova:2020-06-25-SWS-19493-add-jp-permissions
--comment Add jp permissions to superadmin role
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'jackpot' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'jackpot:type' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'jackpot:type:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'jackpot:type:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'jackpot:type:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'jackpot:type:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'jackpot:instance' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'jackpot:instance:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'jackpot:instance:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'jackpot:instance:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'jackpot:instance:delete' WHERE id = 1;

UPDATE roles SET permissions = permissions || '["jackpot", "jackpot:type", "jackpot:type:view", "jackpot:type:edit", "jackpot:type:create", "jackpot:type:delete", "jackpot:instance", "jackpot:instance:view", "jackpot:instance:edit", "jackpot:instance:create", "jackpot:instance:delete"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'jackpot' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'jackpot:type' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'jackpot:type:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'jackpot:type:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'jackpot:type:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'jackpot:type:delete' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'jackpot:instance' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'jackpot:instance:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'jackpot:instance:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'jackpot:instance:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'jackpot:instance:delete' WHERE id = 1;
--rollback RESET search_path;

--changeset aleh.rudzko:2020-07-01-SWS-18862-player-deactivation
--comment Add field which show when player should be deactivated.
SET search_path = swmanagement;
ALTER TABLE players ADD COLUMN deactivated_at timestamp without time zone;
COMMENT ON COLUMN players.deactivated_at IS 'Datetime when player should be deactivated';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE players DROP COLUMN deactivated_at;
--rollback RESET search_path;
