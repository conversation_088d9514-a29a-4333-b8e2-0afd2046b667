--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset aleh.rudzko:2021-05-19-SWS-27596-brand-permission-id
--comment Add new permission to master
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'business-structure:admin' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["business-structure:admin"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'business-structure:admin' WHERE id = 1;
--rollback RESET search_path;


--changeset sergey.malkov:2021-05-24-SWS-18345-Minimize-amount-of-duplicates-transaction-exceptions endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Add stored procedure for saving transaction log
SET search_path TO swmanagement;

CREATE OR REPLACE
FUNCTION fnc_save_wallet_operation_log (IN input_array swmanagement.wallet_operation_log[], OUT inserted INTEGER, OUT duplicates INTEGER)
AS
$$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Purpose    : Insert data to the "wallet_operation_log"
   History    :
      1.0.0
         Date    : Feb 18, 2019
         Authors : Timur Luchkin
         Notes   : Initial release

   Parameters:
      Input batch structure is a set of arrays of next fields (order is important!!!):
         id,operation_id,operation_name,public_id,external_trx_id,is_external,game_id,ts,version,data,params,inserted_at,committed_at
         Note: input value of "inserted_at" field will be overriden by the local assignment

   Sample insert:
      SELECT inserted, duplicates
      FROM   fnc_save_wallet_operation_log(
         '{
            "(**********,0,play,OHj9VA7ZsicAAALgOHj9VfFy8n4=,,t,*************,\"2020-02-12 08:16:58.197\",1,\"[{\"\"value\"\": null, \"\"amount\"\": -80, \"\"account\"\": \"\"main\"\", \"\"trxType\"\": \"\"bet\"\", \"\"property\"\": \"\"balance\"\", \"\"prevValue\"\": null, \"\"walletKey\"\": \"\"player:46:PERF_STARS_b12_01_0049_INT:USD\"\"}, {\"\"value\"\": null, \"\"amount\"\": 0, \"\"account\"\": \"\"main\"\", \"\"trxType\"\": \"\"win\"\", \"\"property\"\": \"\"balance\"\", \"\"prevValue\"\": null, \"\"walletKey\"\": \"\"player:46:PERF_STARS_b12_01_0049_INT:USD\"\"}]\",\"{\"\"isTest\"\": false, \"\"gameCode\"\": \"\"sw_qv\"\", \"\"roundEnded\"\": true, \"\"freeBetCoin\"\": null, \"\"balanceAfter\"\": 10000, \"\"balanceBefore\"\": 10000.8, \"\"gameSessionId\"\": \"\"*************\"\"}\",\"2020-02-12 08:16:58.689\",\"2020-02-12 08:16:58.21\")",
            "(**********,0,play,Oum0+Q8ClGoAAALgOum0+Hta4C4=,,t,*************,\"2020-02-12 19:39:19.672\",1,\"[{\"\"value\"\": null, \"\"amount\"\": -20, \"\"account\"\": \"\"main\"\", \"\"trxType\"\": \"\"bet\"\", \"\"property\"\": \"\"balance\"\", \"\"prevValue\"\": null, \"\"walletKey\"\": \"\"player:287:PERF_RELAX_MOCK_a1_01_0002_EXT:USD\"\"}, {\"\"value\"\": null, \"\"amount\"\": 0, \"\"account\"\": \"\"main\"\", \"\"trxType\"\": \"\"win\"\", \"\"property\"\": \"\"balance\"\", \"\"prevValue\"\": null, \"\"walletKey\"\": \"\"player:287:PERF_RELAX_MOCK_a1_01_0002_EXT:USD\"\"}]\",\"{\"\"isTest\"\": false, \"\"gameCode\"\": \"\"sw_wr\"\", \"\"roundEnded\"\": true, \"\"freeBetCoin\"\": null, \"\"balanceAfter\"\": 10000, \"\"balanceBefore\"\": 10000.2, \"\"gameSessionId\"\": \"\"*************\"\"}\",\"2020-02-12 19:39:20.394\",\"2020-02-12 19:39:19.684\")"
         }'
      ) AS t(inserted, duplicates);

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   rec                     swmanagement.wallet_operation_log;
   init_ins                BOOLEAN := true;
   batch_days              RECORD;
   live_table              REGCLASS := 'swmanagement.wallet_operation_log'::REGCLASS;
   archive_table           REGCLASS := 'swmanagement_archive.wallet_operation_log'::REGCLASS;
   target_partition        REGCLASS;
   debug_enabled           BOOLEAN  := false;
   rows_inserted           INTEGER;
BEGIN
   inserted := 0;
   duplicates := 0;

   -- First try direct insert to the live parent table (assign local "inserted_at" here)
   BEGIN
      INSERT INTO swmanagement.wallet_operation_log (id,operation_id,operation_name,public_id,external_trx_id,is_external,game_id,ts,version,data,params,inserted_at,committed_at)
         SELECT id,operation_id,operation_name,public_id,external_trx_id,is_external,game_id,ts,version,data,params,now() AS inserted_at, committed_at
         FROM unnest(input_array);
      GET DIAGNOSTICS inserted = ROW_COUNT;

   EXCEPTION
      WHEN unique_violation THEN
         init_ins := false;
         IF debug_enabled THEN
            SET client_min_messages TO INFO;
            RAISE INFO 'UNIQUE VIOLATION EXCEPTION';
            RESET client_min_messages;
         END IF;

      WHEN others THEN
         -- pathman partition not found in Live table
         IF SQLSTATE = 'XX000' AND SQLERRM ~* 'no suitable partition for key' THEN
            init_ins := false;
         ELSE
            RAISE EXCEPTION
               USING ERRCODE = SQLSTATE
                    ,MESSAGE = SQLERRM;
         END IF;

         IF debug_enabled THEN
            SET client_min_messages TO INFO;
            RAISE INFO 'OTHER EXCEPTION (pathman related=%): % - %', NOT init_ins, SQLSTATE, SQLERRM;
            RESET client_min_messages;
         END IF;
   END;

   IF NOT init_ins THEN
   -- Breakdown batch by days to insert directly to the partition
      FOR batch_days IN SELECT ts::DATE AS grp_day, count(*) AS grp_cnt FROM unnest(input_array) GROUP BY ts::DATE
      LOOP
         SELECT partition
         FROM   pathman_partition_list
         WHERE  parent IN (live_table, archive_table)
           AND  batch_days.grp_day >= range_min::DATE
           AND  batch_days.grp_day < range_max::DATE
         INTO STRICT target_partition;

         FOR rec IN SELECT * FROM unnest(input_array) WHERE ts::DATE = batch_days.grp_day ORDER BY ts
         -- TODO: Optimization possible: Insert whole day as a single sub-batch.
         LOOP
            -- HINT: Do nothing and Foreign tables: https://postgresrocks.enterprisedb.com/t5/The-Knowledgebase/quot-INSERT-ON-CONFLICT-DO-NOTHING-quot-syntax-gotchas-with/ta-p/3042
            rec.inserted_at := now();
            EXECUTE 'INSERT INTO '||target_partition::TEXT||'(id,operation_id,operation_name,public_id,external_trx_id,is_external,game_id,ts,version,data,params,inserted_at,committed_at) SELECT $1.* ON CONFLICT DO NOTHING' USING rec;
            GET DIAGNOSTICS rows_inserted = ROW_COUNT;

            IF rows_inserted = 0 THEN
            -- Save to duplicates
               duplicates := duplicates + 1;

               -- Skip "pk" here
               INSERT INTO swmanagement.wallet_operation_log_duplicates (id,operation_id,operation_name,public_id,external_trx_id,is_external,game_id,ts,version,data,params,inserted_at,committed_at)
                  SELECT rec.id,rec.operation_id,rec.operation_name,rec.public_id,rec.external_trx_id,rec.is_external,rec.game_id,rec.ts,rec.version,rec.data,rec.params,rec.inserted_at,rec.committed_at;
            ELSE
            -- All good
               inserted := inserted + 1;
            END IF;
         END LOOP;
      END LOOP;
   END IF;
END
$$
LANGUAGE plpgsql;

GRANT EXECUTE ON FUNCTION fnc_save_wallet_operation_log (IN input_array swmanagement.wallet_operation_log[], OUT inserted INTEGER, OUT duplicates INTEGER) TO swmanagement;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_save_wallet_operation_log (IN swmanagement.wallet_operation_log[], OUT INTEGER, OUT INTEGER);
--rollback RESET search_path;


--changeset aleh.rudzko:2021-05-25-SWS-22208-add-features-to-jurisdictions
--comment Add new fields to jurisdictions
SET search_path = swmanagement;
ALTER TABLE jurisdictions ADD COLUMN allowed_countries JSONB;
COMMENT ON COLUMN jurisdictions.allowed_countries IS 'Allowed countries';
ALTER TABLE jurisdictions ADD COLUMN restricted_countries JSONB;
COMMENT ON COLUMN jurisdictions.restricted_countries IS 'Restricted countries';
ALTER TABLE jurisdictions ADD COLUMN default_country VARCHAR(255);
COMMENT ON COLUMN jurisdictions.default_country IS 'Default country';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE jurisdictions DROP COLUMN restricted_countries;
--rollback ALTER TABLE jurisdictions DROP COLUMN allowed_countries;
--rollback ALTER TABLE jurisdictions DROP COLUMN default_country;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2021-06-01_SWS-28001_new_index_on_archived_game_contexts runInTransaction:false
--comment Created an index on the table archived_game_contexts for its partitioning by the field
SET search_path = swgameserver;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_archived_game_contexts_created_at ON archived_game_contexts (created_at);
RESET search_path;

--rollback SET search_path = swgameserver;
--rollback DROP INDEX idx_archived_game_contexts_created_at;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2021-06-01_SWS-28001_partition_archived_game_contexts
--comment Partitioned the big table swgameserver.archived_game_contexts with 1 month interval
SELECT public.set_init_callback (
                        relation        => 'swgameserver.archived_game_contexts'::regclass,
                        callback        => 'public.pathman_callback(jsonb)' );
                    
SELECT public.create_range_partitions (
                        parent_relid    => 'swgameserver.archived_game_contexts'::regclass,
                        expression      => 'created_at',
                        start_value     => (SELECT date_trunc('month', COALESCE (min(created_at), now()))::date FROM swgameserver.archived_game_contexts),
                        p_interval      => '1 month'::interval,
                        p_count         => (CASE WHEN EXISTS (SELECT 1 FROM swgameserver.archived_game_contexts) THEN NULL ELSE 1 END),
                        partition_data  => FALSE );                        
                    
--rollback SELECT public.drop_partitions(parent_relid => 'swgameserver.archived_game_contexts'::regclass);
