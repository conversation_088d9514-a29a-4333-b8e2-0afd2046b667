--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset dmitriy.palaznik:2021-07-29_SWS-29148_freeze-help-page-version-per-game-and-deployment-group
--comment Add new column client_features for game_versions table.
SET search_path = swmanagement;
ALTER TABLE game_versions ADD COLUMN client_features JSONB;
COMMENT ON COLUMN game_versions.client_features IS 'Stores client features (turbo, fastPlay) which will be used when starting the game';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE game_versions DROP COLUMN client_features;
--rollback RESET search_path;
