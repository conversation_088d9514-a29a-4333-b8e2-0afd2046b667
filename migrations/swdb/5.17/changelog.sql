--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset aleksey.stepanov:2023-10-04-SWS-42681
--comment add new 'no_bet_no_chat' flag for live games
SET search_path = swmanagement;

ALTER TABLE players_info ADD COLUMN no_bet_no_chat BOOLEAN NOT NULL DEFAULT FALSE;
COMMENT ON COLUMN players_info.no_bet_no_chat IS 'Flag which means auto enable/disable chat using special rules from live games';

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE players_info DROP COLUMN no_bet_no_chat;
--rollback RESET search_path;

--changeset andrei.stefan:2023-10-31-SWS-42738
--comment add 'wallet_transaction_id' in 'ext_bet_win_history'
SET search_path = swadaptergos;

ALTER TABLE ext_bet_win_history ADD COLUMN wallet_transaction_id VARCHAR(255);
COMMENT ON COLUMN ext_bet_win_history.wallet_transaction_id IS 'Internal transaction id';

RESET search_path;

--rollback SET search_path = swadaptergos;
--rollback ALTER TABLE ext_bet_win_history DROP COLUMN wallet_transaction_id;
--rollback RESET search_path;
