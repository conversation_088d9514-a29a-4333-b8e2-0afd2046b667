--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset emanuel-alin.raileanu:2025-05-31-SWS-50341-add-bonus-api-permissions
--comment Add bonus api permissions
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'player:bonus' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'player:bonus:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'player:bonus:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'player:bonus:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:player:bonus' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:player:bonus:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:player:bonus:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:player:bonus:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["player:bonus:delete", "player:bonus:view", "player:bonus:create", "player:bonus:delete", "keyentity:player:bonus", "keyentity:player:bonus:view", "keyentity:player:bonus:create", "keyentity:player:bonus:delete"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'player:bonus' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'player:bonus:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'player:bonus:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'player:bonus:delete' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:player:bonus' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:player:bonus:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:player:bonus:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:player:bonus:delete' WHERE id = 1;
--rollback RESET search_path;

--changeset emanuel-alin.raileanu:2025-06-03-SWS-50392-implement dynamic domain pools with equal distribution
--comment Add bonus api permissions
SET search_path = swmanagement;

CREATE TABLE dynamic_domain_pools (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    created_at timestamp without time zone DEFAULT NOW(),
    updated_at timestamp without time zone DEFAULT NOW()
);
COMMENT ON TABLE dynamic_domain_pools IS 'Dynamic domain pools entities';
COMMENT ON COLUMN dynamic_domain_pools.name IS 'Dynamic domain pool name';
COMMENT ON COLUMN dynamic_domain_pools.created_at IS 'Creation timestamp';
COMMENT ON COLUMN dynamic_domain_pools.updated_at IS 'Last update timestamp';

CREATE TABLE dynamic_domain_pools_dynamic_domains (
    dynamic_domain_pool_id INT NOT NULL REFERENCES dynamic_domain_pools(id) ON DELETE CASCADE,
    dynamic_domain_id INT NOT NULL REFERENCES dynamic_domains(id) ON DELETE CASCADE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    PRIMARY KEY (dynamic_domain_pool_id, dynamic_domain_id)
);
COMMENT ON TABLE dynamic_domain_pools_dynamic_domains IS 'Connection table between dynamic domain pools and dynamic domains';
COMMENT ON COLUMN dynamic_domain_pools_dynamic_domains.dynamic_domain_pool_id IS 'Dynamic domain pool ID';
COMMENT ON COLUMN dynamic_domain_pools_dynamic_domains.dynamic_domain_id IS 'Dynamic domain ID';
COMMENT ON COLUMN dynamic_domain_pools_dynamic_domains.is_active IS 'Flag that indicates whether the dynamic domain is active and can be used';

CREATE INDEX idx_dynamic_domain_pools_dynamic_domains ON dynamic_domain_pools_dynamic_domains USING btree (dynamic_domain_id);

ALTER TABLE entities ADD COLUMN dynamic_domain_pool_id INTEGER REFERENCES dynamic_domain_pools(id) ON DELETE CASCADE;
COMMENT ON COLUMN entities.dynamic_domain_pool_id IS 'Dynamic domain pool id';

UPDATE roles SET permissions = permissions - 'domain-pool' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'domain-pool:dynamic' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'domain-pool:dynamic:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'domain-pool:dynamic:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'domain-pool:dynamic:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'domain-pool:dynamic:remove' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["domain-pool", "domain-pool:dynamic", "domain-pool:dynamic:view", "domain-pool:dynamic:create", "domain-pool:dynamic:edit", "domain-pool:dynamic:remove"]'::jsonb WHERE id = 1;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool:dynamic:remove' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool:dynamic:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool:dynamic:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool:dynamic:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool:dynamic' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'domain-pool' WHERE id = 1;
--rollback ALTER TABLE entities DROP COLUMN dynamic_domain_pool_id;
--rollback DROP TABLE dynamic_domain_pools_dynamic_domains;
--rollback DROP TABLE dynamic_domain_pools;
--rollback RESET search_path;
