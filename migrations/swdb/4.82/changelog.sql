--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset aleksey.ignatenko:2022-03-24_SWDB-XXX_aggr_player_rounds_by_country_table
--comment New aggregation of rounds by hours with player country (like bo_aggr_player_rounds). Table, config
CREATE TABLE IF NOT EXISTS swmanagement.bo_aggr_player_rounds_by_country (
    date_hour       timestamp   NOT NULL,   -- Hour    
    country_code    char(2)     NOT NULL,   -- Country ISO-code
    brand_id        int4        NOT NULL,   -- Entity
    player_code     varchar(255) NOT NULL,  -- Player code
    game_code       varchar(255) NOT NULL,  -- Game code for filtering by games    
    currency_code   char(3)     NOT NULL, -- Currency ISO-code    
    total_bet       numeric     NOT NULL    DEFAULT 0, -- Staked amount
    total_win       numeric     NOT NULL    DEFAULT 0, -- Returned amount
    currency_code_eur char(3)   NOT NULL    DEFAULT 'USD', -- Currency ISO-code (common)
    total_bet_eur   numeric     NOT NULL    DEFAULT 0, -- Staked amount (in common currency)
    total_win_eur   numeric     NOT NULL    DEFAULT 0, -- Returned amount (in common currency)
    rounds_qty      int4        NOT NULL    DEFAULT 0, -- Number of games played
    events_qty      int4        NOT NULL    DEFAULT 0, -- Number of spins
    first_activity  timestamp   NULL, -- Started the first round at the time
    last_activity   timestamp   NULL, -- Finished the last round at the time
    inserted_at     timestamp   NOT NULL    DEFAULT (now() AT TIME ZONE 'UTC'),
    updated_at      timestamp   NULL,
    PRIMARY KEY (date_hour, country_code, brand_id, player_code, game_code, currency_code)
);
CREATE INDEX IF NOT EXISTS idx_bo_aggr_player_rounds_by_country_brand_id ON swmanagement.bo_aggr_player_rounds_by_country (brand_id);
CREATE INDEX IF NOT EXISTS idx_bo_aggr_player_rounds_by_country_game_code ON swmanagement.bo_aggr_player_rounds_by_country (game_code);

COMMENT ON TABLE swmanagement.bo_aggr_player_rounds_by_country IS 'Table with accumulated played rounds per date-hour/country/brand/player/game/currency';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.date_hour IS 'Hour';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.country_code IS 'Country where the rounds were played from';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.brand_id IS 'Operator';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.player_code IS 'Player';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.game_code IS 'Game';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.currency_code IS 'Currency';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.total_bet IS 'Staked amount';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.total_win IS 'Returned amount';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.currency_code_eur IS 'Currency (common)';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.total_bet_eur IS 'Staked amount (in common currency). Value in EUR is approximate and shouldn''t be used for financial reporting';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.total_win_eur IS 'Returned amount (in common currency). Value in EUR is approximate and shouldn''t be used for financial reporting';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.rounds_qty IS 'Number of games played';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.events_qty IS 'Number of spins';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.first_activity IS 'Started the first round at the time';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.last_activity IS 'Finished the last round at the time';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.inserted_at IS 'Time of appending';
COMMENT ON COLUMN swmanagement.bo_aggr_player_rounds_by_country.updated_at IS 'Time of refreshing';

INSERT INTO swmanagement.bo_aggr_config (aggr_job_name, conf_key, conf_value)
VALUES ('bo_aggr_player_rounds_by_country', 'lock_record', '210101')
ON CONFLICT DO NOTHING;

ALTER TABLE swmanagement.bo_aggr_player_rounds_by_country ALTER COLUMN country_code TYPE varchar(6);

--rollback DELETE FROM SELECT * FROM  swmanagement.bo_aggr_config WHERE aggr_job_name = 'bo_aggr_player_rounds_by_country';
--rollback DROP TABLE swmanagement.bo_aggr_player_rounds_by_country;


--changeset aleksey.ignatenko:2022-03-24_SWDB-XXX_aggr_player_rounds_by_country_func endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment New aggregation of rounds by hours with player country (like bo_aggr_player_rounds). Functions
CREATE OR REPLACE FUNCTION swmanagement.fnc_get_convert_amount
(
    p_curr_code     char(3), 
    p_amount        numeric, 
    p_curr_code_new char(3),
    p_rate_date     date        DEFAULT (now()::date)
)
RETURNS numeric
 LANGUAGE plpgsql
AS $function$
/*
   Object Name:   fnc_get_convert_amount
   Purpose    :   Convert money of the one currency to another
   History    :
      1.0.0
         Date    : 2021-02-25
         Authors : Ales
         Notes   : Release ()
    Sample run:
      SELECT swmanagement.fnc_get_convert_amount ('USD', 12.00, 'EUR');
      SELECT swmanagement.fnc_get_convert_amount ('USD', 12.00, 'EUR', '191020');
*/
DECLARE
    v_rate1 NUMERIC;
    v_rate2 NUMERIC;
BEGIN
    SELECT FIRST_VALUE (rate) OVER (ORDER BY rate_date DESC) 
        INTO v_rate1
    FROM swmanagement.currency_rates 
    WHERE rate_date <= p_rate_date AND currency_code = p_curr_code
    LIMIT 1;

    SELECT FIRST_VALUE (rate) OVER (ORDER BY rate_date DESC) 
        INTO v_rate2
    FROM swmanagement.currency_rates 
    WHERE rate_date <= p_rate_date AND currency_code = p_curr_code_new    
    LIMIT 1;

    RETURN p_amount * coalesce(v_rate1, 0::numeric) / coalesce(v_rate2, 1::numeric);
END $function$;

CREATE OR REPLACE FUNCTION swmanagement.fnc_bo_aggr_refresh_player_rounds_by_country
(
    p_huge_interval     INTERVAL    DEFAULT '3 hour'::INTERVAL
)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
/*
   Object Name:   fnc_bo_aggr_refresh_player_rounds_by_country
   Purpose    :   To perform B/O aggregation of rounds by countries where they were played
   History    :
      1.0.0
         Date    : 2021-02-25
         Authors : Ales
         Notes   : Release ()
    Sample run:
      SELECT swmanagement.fnc_bo_aggr_refresh_player_rounds_by_country ();
*/
DECLARE
    v_res               jsonb;
    v_huge_interval     INTERVAL := COALESCE(p_huge_interval, '12 hours'::INTERVAL);
    v_rep_curr_code     char(3) := 'EUR'; 
    v_job_start_time    TIMESTAMP;
    v_job_finish_time   TIMESTAMP;
    v_force_end_time    TIMESTAMP;
    v_last_inserted_at  TIMESTAMP;
    v_new_inserted_at   TIMESTAMP;   
    v_counter           BIGINT;
    v_ins_rows          BIGINT;
    v_upd_rows          BIGINT;
BEGIN
    v_job_start_time := clock_timestamp();

    SELECT conf_value 
        INTO v_last_inserted_at 
    FROM swmanagement.bo_aggr_config 
    WHERE aggr_job_name = 'bo_aggr_player_rounds_by_country' AND conf_key = 'lock_record' 
    FOR UPDATE;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_player_rounds_by_country/lock_record" pair';
    END IF;
    
    v_new_inserted_at := v_last_inserted_at + v_huge_interval;
    IF (v_new_inserted_at > (now() AT TIME ZONE 'UTC') - INTERVAL '2 day')
    THEN
        v_force_end_time := (now() AT TIME ZONE 'UTC') - INTERVAL '2 day';
        v_new_inserted_at := v_force_end_time;
    END IF;

    WITH cte_aggr_rounds AS (
        SELECT date_trunc('HOUR', h.finished_at)        AS date_hour
            , COALESCE(s.played_from_country, '--')     AS played_from_country
            , h.brand_id                                AS brand_id
            , h.player_code
            , h.game_code
            , h.currency                                AS currency_code            
            , count(h.id)::BIGINT                       AS rounds_qty
            , sum(h.total_events)::INTEGER              AS events_qty
            , sum(h.total_bet)                          AS total_bet
            , sum(h.total_win)                          AS total_win            
            , min(h.started_at)                         AS first_activity
            , max(h.finished_at)                        AS last_activity
        FROM swmanagement.rounds_finished h
        INNER JOIN swmanagement.sessions_history s ON s.id = h.session_id
        WHERE NOT h.test
            AND COALESCE(h.inserted_at, h.started_at) >= v_last_inserted_at AND COALESCE(h.inserted_at, h.started_at) < v_new_inserted_at
        GROUP BY s.played_from_country, h.player_code, h.brand_id, h.game_code, h.currency, date_trunc('HOUR', h.finished_at)
    )
    , cte_aggr_rounds_convert AS (
        SELECT r.date_hour, r.played_from_country, r.brand_id, r.player_code, r.game_code,
            r.currency_code, r.total_bet, r.total_win,
            v_rep_curr_code AS currency_code_eur, 
            swmanagement.fnc_get_convert_amount (r.currency_code, r.total_bet, v_rep_curr_code, r.date_hour::date) AS total_bet_eur, 
            swmanagement.fnc_get_convert_amount (r.currency_code, r.total_win, v_rep_curr_code, r.date_hour::date) AS total_win_eur,
            r.rounds_qty, r.events_qty, r.first_activity, r.last_activity,
            CASE WHEN t.brand_id IS NULL THEN TRUE ELSE FALSE END AS is_new
        FROM cte_aggr_rounds r
        LEFT JOIN swmanagement.bo_aggr_player_rounds_by_country t 
            ON t.date_hour=r.date_hour AND t.country_code=r.played_from_country AND t.brand_id=r.brand_id AND t.player_code=r.player_code AND t.game_code=r.game_code AND t.currency_code=r.currency_code    
    )
    , cte_inserted AS (
        INSERT INTO swmanagement.bo_aggr_player_rounds_by_country AS a (
            date_hour, country_code, brand_id, player_code, game_code, 
            currency_code, total_bet, total_win, currency_code_eur, total_bet_eur, total_win_eur,
            rounds_qty, events_qty, first_activity, last_activity )
        SELECT date_hour, played_from_country, brand_id, player_code, game_code, 
            currency_code, total_bet, total_win, currency_code_eur, total_bet_eur, total_win_eur,
            rounds_qty, events_qty, first_activity, last_activity
        FROM cte_aggr_rounds_convert 
        WHERE is_new 
        RETURNING * )
    , cte_updated AS ( 
        UPDATE swmanagement.bo_aggr_player_rounds_by_country AS t 
        SET total_bet = t.total_bet + r.total_bet, 
            total_win = t.total_win + r.total_win, 
            total_bet_eur = t.total_bet_eur + r.total_bet_eur, 
            total_win_eur = t.total_win_eur + r.total_win_eur,
            rounds_qty = t.rounds_qty + r.rounds_qty, 
            events_qty = t.events_qty + r.events_qty,
            updated_at = now() AT TIME ZONE 'UTC'
        FROM cte_aggr_rounds_convert r
        WHERE NOT r.is_new 
            AND t.date_hour=r.date_hour AND t.country_code=r.played_from_country AND t.brand_id=r.brand_id AND t.player_code=r.player_code AND t.game_code=r.game_code AND t.currency_code=r.currency_code
        RETURNING *
    )
    SELECT (SELECT count(*) FROM cte_inserted) AS ins_rows, (SELECT count(*) FROM cte_updated) AS upd_rows
        INTO v_ins_rows, v_upd_rows;
    
    v_counter := v_ins_rows + v_upd_rows;
    
    /* set the last timestamp */
    UPDATE swmanagement.bo_aggr_config 
    SET conf_value = v_new_inserted_at,
        updated_at = now() AT TIME ZONE 'UTC'
    WHERE aggr_job_name = 'bo_aggr_player_rounds_by_country' AND  conf_key = 'lock_record';
    
    /* log */
    v_job_finish_time := clock_timestamp();

    v_res := jsonb_build_object (
                'rows_applied', v_counter, 'rows_inserted', v_ins_rows, 'rows_updated', v_upd_rows,
                'job_started_at', v_job_start_time, 'job_finished_at', v_job_finish_time, 
                'job_time', to_char((v_job_finish_time - v_job_start_time), 'mi:ss.ms'),
                'period', to_char(v_last_inserted_at, 'yyyy-mm-dd hh24:mi') || ' .. ' || to_char(v_new_inserted_at, 'yyyy-mm-dd hh24:mi')
            );
        
    INSERT INTO swmanagement.bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts, details)
      VALUES ('bo_aggr_player_rounds_by_country', v_job_start_time, v_job_finish_time, v_counter, v_last_inserted_at, v_new_inserted_at, v_force_end_time, v_res::text);
    
    RETURN v_res;
END $function$
;

/* schedule once in 10 minutes */
WITH cte_run AS (
SELECT cron.schedule('*/10 * * * *'
    , '/* Aggr rounds by countries */ SELECT swmanagement.fnc_bo_aggr_refresh_player_rounds_by_country ();'
))
SELECT * FROM cte_run WHERE NOT EXISTS (SELECT 1 FROM cron.job j WHERE j.command ILIKE '%fnc_bo_aggr_refresh_player_rounds_by_country%');

--rollback SELECT cron.unschedule(j.jobid) FROM cron.job j WHERE j.command ILIKE '%fnc_bo_aggr_refresh_player_rounds_by_country%';
--rollback DROP FUNCTION IF EXISTS swmanagement.fnc_bo_aggr_refresh_player_rounds_by_country;
--rollback DROP FUNCTION IF EXISTS swmanagement.fnc_get_convert_amount;


--changeset sergey.malkov:2022-03-29-SWS-33634-Add-additional-user-status-LOCKED_BY_AUTHENTICATION runInTransaction:false
--comment Added locked_by_auth element to enum_users_status
SET search_path = swmanagement;
ALTER TYPE enum_users_status ADD VALUE IF NOT EXISTS 'locked_by_auth';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM pg_enum where enumlabel = 'locked_by_auth' and enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_users_status');
--rollback RESET search_path;


--changeset pavel.shamshurov:2022-03-28-SWS-33490-create-table-for-bi-report-domains
--comment Create table for bi report domains and add new permissions
SET search_path = swmanagement;

CREATE TABLE IF NOT EXISTS bi_report_domains (
	id SERIAL PRIMARY KEY,
	base_url VARCHAR(255) NOT NULL,
	trust_server_url VARCHAR(255) NOT NULL,
	is_selected BOOLEAN DEFAULT false NOT NULL,
	version INTEGER NOT NULL DEFAULT 0,
	created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
  updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL
);
COMMENT ON TABLE bi_report_domains IS 'BI report domains';
COMMENT ON COLUMN bi_report_domains.id IS 'BI report domains id';
COMMENT ON COLUMN bi_report_domains.base_url IS 'BI report base url';
COMMENT ON COLUMN bi_report_domains.trust_server_url IS 'BI report trust server url';
COMMENT ON COLUMN bi_report_domains.is_selected IS 'Flag which marks the domains as selected to generate BI report';
COMMENT ON COLUMN bi_report_domains.version IS 'BI report domains version';
COMMENT ON COLUMN bi_report_domains.created_at IS 'Date when BI report domains are created';
COMMENT ON COLUMN bi_report_domains.updated_at IS 'Date when BI report domains are updated';

UPDATE roles SET permissions = permissions - 'keyentity:bi-reports-domains' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi-reports-domains:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi-reports-domains:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi-reports-domains:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi-reports-domains:select' WHERE id = 1;

UPDATE roles SET permissions = permissions || '["keyentity:bi-reports-domains", "keyentity:bi-reports-domains:create", "keyentity:bi-reports-domains:view", "keyentity:bi-reports-domains:edit", "keyentity:bi-reports-domains:select"]'::jsonb WHERE id = 1;

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi-reports-domains' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi-reports-domains:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi-reports-domains:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi-reports-domains:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi-reports-domains:select' WHERE id = 1;
--rollback DROP TABLE IF EXISTS bi_report_domains;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2022-03-09_DEVOPS-18894_cleanup_external_rounds endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Cleaning the table swadapterb365.rounds from data older than 1 months except unfinished rounds
ALTER FUNCTION swsystem.fnc_clean_data SET SCHEMA swbackup;

CREATE OR REPLACE FUNCTION swsystem.fnc_clean_pathman_partition 
( 
    p_check_only    bool    DEFAULT FALSE, 
    p_debug         bool    DEFAULT FALSE
)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name:   fnc_clean_pathman_partition
    Purpose    : Remove partitions with non-actual data from DB
    History    :
        1.0.0
            Date    : 2022-03-31
            Authors : Ales
            Notes   : Release (DEVOPS-18894)
            
    Sample run:
        SELECT swsystem.fnc_clean_pathman_partition(false, true);
********************************************************************************************************/
DECLARE
    v_res       jsonb       := '{}'::jsonb;
    v_today     timestamp   := date_trunc('DAY', now() AT TIME ZONE 'UTC');
    v_lifespan  INTERVAL    := INTERVAL '1 mon';    
    c           record;
    v_is_rest   bool;
    v_num       int;
    v_sql       text;
    v_msg       text;
    v_err_msg   text;
    v_part_name text;
    v_count     int         := 0;
BEGIN
    /* check on partition complete */
    IF EXISTS (SELECT 1 FROM ONLY swadapterb365.rounds) THEN
        RAISE INFO 'The parent table swadapterb365.rounds has records';
        RETURN jsonb_build_object('status', 'FAIL')             
            || jsonb_build_object('result', 'The parent table swadapterb365.rounds has records');
    END IF;
    
    /* old partitions */
    FOR c IN (
        WITH cte_cut_off AS (SELECT (v_today - v_lifespan) AS dead_line)
        SELECT parent AS tabrel, "partition" AS partrel, expr AS dt_col, 
                range_min, range_max, dead_line,
                split_part(parent::text, '.', 1) AS schema_name, split_part(parent::text, '.', 2) AS table_name
        FROM public.pathman_partition_list p CROSS JOIN cte_cut_off d                 
        WHERE parttype = 2 AND parent = 'swadapterb365.rounds'::regclass
            AND p.range_max < d.dead_line::TEXT
        ORDER BY p.range_min ASC 
    ) LOOP 
        /* init cycle */
        v_num := 0;
        v_part_name := c.partrel::text;
        DROP TABLE IF EXISTS tmp_partition_rest;
        
        /* check */
        v_sql := format ('SELECT 1 FROM %s r WHERE NOT EXISTS (SELECT 1 FROM swmanagement.rounds_unfinished u WHERE u.id = public.sw_get_internal_id(r.sw_round_id)) LIMIT 1'
                        , c.partrel);             
        EXECUTE v_sql INTO v_num;
        v_num := COALESCE (v_num, 0);
    
        IF (v_num = 0)
        THEN
            v_sql := format ('SELECT 1 FROM %s r WHERE EXISTS (SELECT 1 FROM swmanagement.rounds_unfinished u WHERE u.id = public.sw_get_internal_id(r.sw_round_id)) LIMIT 1'
                        , c.partrel);
            EXECUTE v_sql INTO v_num;
            v_num := COALESCE (v_num, 0);
        
            IF (v_num = 0)
            THEN
                v_is_rest := FALSE;
                v_msg := 'removed: empty';
                v_err_msg := CASE WHEN p_check_only THEN v_msg ELSE NULL END;
            ELSE /* found unfinished rounds in the partition */
                v_msg := 'not removed because of: only unfinished rounds found here. Skipped';
                v_err_msg := v_msg;
            END IF;
        ELSE /* found rounds to clear */            
            v_sql := format ('CREATE TEMP TABLE tmp_partition_rest AS SELECT * FROM %s r WHERE EXISTS (SELECT 1 FROM swmanagement.rounds_unfinished u WHERE u.id = public.sw_get_internal_id(r.sw_round_id));'
                        , c.partrel);
            --RAISE INFO '%', v_sql;
            EXECUTE v_sql;
        
            SELECT EXISTS (SELECT 1 FROM tmp_partition_rest) INTO v_is_rest;
        
            IF (NOT v_is_rest)
            THEN
                v_msg := 'removed: no unfinished rounds';
                v_err_msg := CASE WHEN p_check_only THEN v_msg ELSE NULL END;            
            ELSE /* found unfinished rounds in the partition */
                v_msg := 'cleared but not removed due to: at least one unfinished round here';
                v_err_msg := CASE WHEN p_check_only THEN v_msg ELSE NULL END;
            END IF;
        END IF;
        
        /* check on auto-vacuum */
        IF (v_err_msg IS NULL AND 
            EXISTS (SELECT 1 FROM pg_stat_activity
                 WHERE query ~* 'autovacuum' AND query ~* (v_part_name) AND pid != pg_backend_pid()) 
            )
        THEN
            v_msg := 'involved by parallel maintenance process. Skipped';
            v_err_msg := v_msg;
        END IF;
        
        /* log */
        IF (p_debug OR p_check_only) THEN 
            v_msg := CASE WHEN p_check_only THEN 'tested and must be '||v_msg ELSE v_msg END;
            PERFORM swsystem.fnc_log_partition_cleanup( v_part_name, c.dead_line, p_check_only, v_num, (v_err_msg IS NOT NULL), 
                    format('Partition %s of table %s is %s', v_part_name, c.tabrel::text, v_msg) );                
        END IF;
        
        /* clean */
        IF (v_err_msg IS NULL) 
        THEN
            IF (NOT v_is_rest)
            THEN
                /* save to history */
                INSERT INTO swsystem.partition_cleanup_history (partition_name, schema_name, table_name, dt_col_name, range_min, range_max)
                VALUES (v_part_name, c.schema_name, c.table_name, c.dt_col, c.range_min, c.range_max);
                
                /* remove partition */
                SELECT public.drop_range_partition(c.partrel, TRUE) INTO v_part_name;                
            ELSE /* remained unfinished rounds */ 
                v_sql := format ('TRUNCATE %s;', c.partrel);
                EXECUTE v_sql;
            
                v_sql := format ('INSERT INTO %s (operator_round_id, sw_round_id, created_at, updated_at) SELECT * FROM tmp_partition_rest;'
                    , c.partrel);  
                EXECUTE v_sql;
            END IF;
            
            DROP TABLE IF EXISTS tmp_partition_rest;
        
            RETURN jsonb_build_object('status', 'SUCCESS')
                || jsonb_build_object('result', v_msg)
                || jsonb_build_object('partition', v_part_name)
                || jsonb_build_object('skipped', format('%s partitions', v_count))
                || jsonb_build_object('skipped partitions', v_res);
        END IF;
    
        /* add to result */                
        v_res := v_res || jsonb_build_object( v_part_name, v_msg );
         
        v_count := v_count + 1;  
    END LOOP;
    
    v_res := jsonb_build_object('status', 'FAIL')             
            || jsonb_build_object('result', 'nothing to remove')
            || jsonb_build_object('skipped', format('%s partitions', v_count))
            || jsonb_build_object('skipped partitions', v_res);
    
    RETURN v_res;
END $function$;

--rollback DROP FUNCTION swsystem.fnc_clean_pathman_parition;
--rollback ALTER FUNCTION swbackup.fnc_clean_data SET SCHEMA swsystem;
