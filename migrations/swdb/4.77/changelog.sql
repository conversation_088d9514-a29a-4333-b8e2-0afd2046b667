--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset kirill.kaminskiy:2021-12-28-SWS-31868-userType-patch-restriction
--comment Add new permissions for user (user:change-type)
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'user:change-type' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["user:change-type"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'user:change-type' WHERE id = 1;
--rollback RESET search_path;

--changeset kirill.kaminskiy:2022-01-03_SWS-31708-add-ability-to-change-live-games
--comment Add new permissions for live games to master
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'entity:live-game' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'entity:live-game:add-live-game' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'entity:live-game:remove-live-game' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'entity:live-game:change-state:enabled' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'entity:live-game:change-state' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'entity:live-game:change-state:disabled' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:live-game' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:live-game:change-state' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:live-game:change-state:disabled' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:live-game:change-state:enabled' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["entity:live-game", "entity:live-game:add-live-game", "entity:live-game:remove-live-game", "entity:live-game:change-state", "entity:live-game:change-state:disabled", "entity:live-game:change-state:enabled"]'::jsonb WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:live-game", "keyentity:live-game:change-state:disabled", "keyentity:live-game:change-state", "keyentity:live-game:change-state:enabled"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'entity:live-game' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entity:live-game:add-live-game' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entity:live-game:remove-live-game' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entity:live-game:change-state:enabled' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entity:live-game:change-state' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entity:live-game:change-state:disabled' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:live-game' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:live-game:change-state' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:live-game:change-state:disabled' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:live-game:change-state:enabled' WHERE id = 1;
--rollback RESET search_path;

--changeset georgiy.kovrey:2022-01-12_MICGMG-228-incorrect-bet-ticket
--comment Add new permissions for live games to master
SET search_path = swadaptermgm;
ALTER TABLE micgaming_rounds ADD COLUMN sw_transaction_id character varying(255) NOT NULL DEFAULT '';
COMMENT ON COLUMN micgaming_rounds.sw_transaction_id IS 'Skywinds transaction id.';

ALTER TABLE micgaming_rounds DROP CONSTRAINT micgaming_rounds_pkey;
ALTER TABLE micgaming_rounds ADD PRIMARY KEY (sw_round_id, operator_round_id, sw_transaction_id);
RESET search_path;
--rollback SET search_path = swadaptermgm;
--rollback ALTER TABLE micgaming_rounds DROP CONSTRAINT micgaming_rounds_pkey;
--rollback ALTER TABLE micgaming_rounds DROP COLUMN sw_transaction_id;
--rollback ALTER TABLE micgaming_rounds ADD PRIMARY KEY (sw_round_id, operator_round_id);
--rollback RESET search_path;

--changeset kirill.kaminskiy:2022-01-20-SWS-31769-set-entity-status-test
--comment Add new permissions for master (entity:change-state-test)
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'entity:change-state-test' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["entity:change-state-test"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'entity:change-state-test' WHERE id = 1;
--rollback RESET search_path;