--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset mikhail.ivanov:2022-06-15-SWS-25236_rtp-reducer-children-see-inherited-from-entity-title-of-parents
--comment Add permissions for fetch detailed info in rtp-report
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'keyentity:gamertp:detailed-view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'gamertp:detailed-view' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:gamertp:detailed-view", "gamertp:detailed-view"]'::jsonb WHERE id = 1;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:gamertp:detailed-view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'gamertp:detailed-view' WHERE id = 1;
--rollback RESET search_path;
