--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset andrey.shmigiro:2020-02-13-SWS-XXXX-start-release-4.32.0
--comment label for 4.32.0
select now();
--rollback select now();


--changeset alexander.guzanov:2020-02-13-SWS-15975
--comment Keep transaction duplicates in a separate table
SET search_path = swmanagement;

CREATE TABLE IF NOT EXISTS wallet_operation_log_duplicates(
    pk BIGSERIAL PRIMARY KEY,
    id BIGINT NOT NULL,
    operation_id INTEGER NOT NULL,
    operation_name <PERSON><PERSON><PERSON><PERSON>(255),
    public_id CHAR(28) NOT NULL,
    external_trx_id VARCHAR(255),
    is_external BOOLEAN NOT NULL,
    game_id VARCHAR(255),
    ts TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    version INTEGER NOT NULL,
    data JSONB NOT NULL,
    params JSONB,
    inserted_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now(),
    committed_at TIMESTAMP WITHOUT TIME ZONE
);
COMMENT ON TABLE wallet_operation_log_duplicates IS 'The table contains duplicates, that occurred during unloading process';
ALTER TABLE wallet_operation_log_duplicates OWNER TO swmanagement;

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP TABLE wallet_operation_log_duplicates;
--rollback RESET search_path;

--changeset anastasia.kostyukova:2020-02-20-SWS-16291
--comment Add new merchant types for external ipm adapter
SET search_path = swmanagement;
INSERT INTO merchant_types(type, url, schema, created_at, updated_at)
SELECT 'seamless_ipm', null, (select schema from merchant_types where type = 'seamless')::jsonb, now()::date, now()::date
WHERE EXISTS (select schema from merchant_types where type = 'seamless');
INSERT INTO merchant_types(type, url, schema, created_at, updated_at)
SELECT 'seamless_mrch', null, (select schema from merchant_types where type = 'seamless')::jsonb, now()::date, now()::date
WHERE EXISTS (select schema from merchant_types where type = 'seamless');
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM merchant_types WHERE type in ('seamless_ipm', 'seamless_mrch');
--rollback RESET search_path;

--changeset sergey.malkov:2020-02-21-SWS-SWS-16128
--comment Transfer out jackpot seed/progressive w/o destination pool
SET search_path = swjackpot;
ALTER TABLE jp_transfer_log ALTER COLUMN to_pool DROP NOT NULL;
ALTER TABLE jp_transfer_log ALTER COLUMN to_pool_seed DROP NOT NULL;
ALTER TABLE jp_transfer_log ALTER COLUMN to_pool_progressive DROP NOT NULL;
ALTER TABLE remote_jp_transfer_log ALTER COLUMN to_pool DROP NOT NULL;
ALTER TABLE remote_jp_transfer_log ALTER COLUMN to_pool_seed DROP NOT NULL;
ALTER TABLE remote_jp_transfer_log ALTER COLUMN to_pool_progressive DROP NOT NULL;
RESET search_path;
--rollback SET search_path = swjackpot;
--rollback ALTER TABLE jp_transfer_log ALTER COLUMN to_pool SET NOT NULL;
--rollback ALTER TABLE jp_transfer_log ALTER COLUMN to_pool_seed SET NOT NULL;
--rollback ALTER TABLE jp_transfer_log ALTER COLUMN to_pool_progressive SET NOT NULL;
--rollback ALTER TABLE remote_jp_transfer_log ALTER COLUMN to_pool SET NOT NULL;
--rollback ALTER TABLE remote_jp_transfer_log ALTER COLUMN to_pool_seed SET NOT NULL;
--rollback ALTER TABLE remote_jp_transfer_log ALTER COLUMN to_pool_progressive SET NOT NULL;
--rollback RESET search_path;


--changeset stepanov:aleksey:2020-02-24-BYSWGPRO-8020 endDelimiter:# stripComments:false
--comment Parse existing game category translations that are in title
SET search_path = swmanagement;
CREATE OR REPLACE FUNCTION fnc_replace_title_and_update_translations_game_category()
  RETURNS SETOF game_categories AS $BODY$
DECLARE
	categories record;
	new_translations jsonb;
	en_title VARCHAR;
	find_title VARCHAR;
	lang_code VARCHAR;
	lang_title VARCHAR;
BEGIN
	FOR categories in (select id, string_to_array(title, '|') as titles, translations from game_categories where title like '%:%' ORDER BY id) LOOP
		if categories IS NULL then
			RETURN ;
		end if;

		en_title := '';
		new_translations := '{}';
		FOREACH find_title IN ARRAY categories.titles LOOP
				if (strpos(find_title, 'en:') > 0) then
					en_title := regexp_replace(find_title, 'en:', '');
				else
				  lang_code := split_part(find_title, ':', 1);
					if lang_code = 'ch' then
						lang_code := 'zh';
					end if;
				  lang_title := split_part(find_title, ':', 2);
                  new_translations := jsonb_concat(new_translations, jsonb_build_object(lang_code, json_build_object('title', lang_title)));
				end if;
		END LOOP;

		UPDATE game_categories SET title = en_title, translations = new_translations::jsonb where id = categories.id;
	END LOOP;
END;
$BODY$
  LANGUAGE plpgsql;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_replace_title_and_update_translations_game_category();
--rollback RESET search_path;


--changeset andrey.shmigiro:2020-02-26-NO-JIRA-fix-swdeferredpmnt-permissions
--comment Fix swdeferredpmnt permissions
GRANT role_swdeferredpmnt_write TO swdeferredpmnt;
--rollback REVOKE role_swdeferredpmnt_write FROM swdeferredpmnt;
