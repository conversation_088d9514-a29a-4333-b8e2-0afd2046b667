--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset pavel.shamshurov:2022-03-10-SWS-33383-update-country-limitation
--comment update country limitation to support region code
SET search_path = swmanagement;
ALTER TABLE sessions_history ALTER COLUMN played_from_country TYPE varchar(6);
ALTER TABLE sessions_history_duplicates ALTER COLUMN played_from_country TYPE varchar(6);
RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE sessions_history ALTER COLUMN played_from_country TYPE varchar(6);
RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE sessions_history ALTER COLUMN played_from_country TYPE varchar(6);
RESET search_path;

--rollback SELECT NOW();


--changeset aleksey.ignatenko:2022-03-09_SWS-32913_new_index_on_external_rounds_by_time
--comment Create index on swadapterb365.rounds by insert time field before the table partition
SET search_path = swadapterb365;
CREATE INDEX IF NOT EXISTS idx_rounds_created_at ON rounds USING btree (created_at);
RESET search_path;

--rollback SET search_path = swadapterb365;
--rollback DROP INDEX idx_rounds_created_at;
--rollback RESET search_path;


--changeset aleksey.ignatenko:2022-03-09_SWS-32913_cleanup_external_rounds endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Clean the table swadapterb365.rounds from data older than 3-8 months depending on unfinished those rounds or not
--comment !!! After applying to DB it requires to run partitioning 
SELECT public.set_init_callback (
                        relation        => 'swadapterb365.rounds'::regclass,
                        callback        => 'public.pathman_callback(jsonb)' );
SELECT public.create_range_partitions (
                        parent_relid    => 'swadapterb365.rounds'::regclass,
                        expression      => 'created_at',
                        start_value     => (SELECT date_trunc('week',COALESCE(min(created_at),now()))::date FROM swadapterb365.rounds),
                        p_interval      => '7 days'::interval,
                        p_count         => (SELECT CASE WHEN NOT EXISTS (SELECT 1 FROM swadapterb365.rounds) THEN 1 ELSE NULL END),
                        partition_data  => FALSE ); 
                        
CREATE OR REPLACE FUNCTION swsystem.fnc_clean_data 
( 
    p_check_only    bool    DEFAULT FALSE, 
    p_debug         bool    DEFAULT FALSE
)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
/********************************************************************************************************
    Object Name:   fnc_clean_data
    Purpose    : Remove partitions with non-actual data from DB
    History    :
        1.0.0
            Date    : 2022-03-23
            Authors : Ales
            Notes   : Release (SWS-32913)
            
    Sample run:
        SELECT swsystem.fnc_clean_data(false, true);
********************************************************************************************************/
DECLARE
    v_res       jsonb       := '{}'::jsonb;
    v_today     timestamp   := date_trunc('DAY', now() AT TIME ZONE 'UTC');
    v_lifespan  INTERVAL    := INTERVAL '1 mon';
    v_suspspan  INTERVAL    := INTERVAL '8 mon';
    c           record;  
    v_num       int;
    v_sql       text;
    v_msg       text;
    v_err_msg   text;
    v_part_name text;
    v_count     int         := 0;
BEGIN
    IF EXISTS (SELECT 1 FROM ONLY swadapterb365.rounds) THEN
        RETURN jsonb_build_object('status', 'FAIL')             
            || jsonb_build_object('result', 'The parent table swadapterb365.rounds has records');
    END IF;
    
    FOR c IN (
        WITH cte_cut_off AS (SELECT (v_today - v_lifespan) AS dead_line)
        SELECT parent AS tabrel, "partition" AS partrel, expr AS dt_col, 
                range_min, range_max, dead_line,
                split_part(parent::text, '.', 1) AS schema_name, split_part(parent::text, '.', 2) AS table_name
        FROM public.pathman_partition_list p CROSS JOIN cte_cut_off d                 
        WHERE parttype = 2 AND parent = 'swadapterb365.rounds'::regclass
            AND p.range_max < d.dead_line::TEXT
        ORDER BY p.range_min ASC 
    ) LOOP 
    
        v_num := 0;
        v_part_name := c.partrel::text;
        
        /* check */
        IF (c.range_max < (v_today - v_suspspan)::TEXT)
        THEN
            v_msg := 'removed: older than 8 month';
            v_err_msg := CASE WHEN p_check_only THEN v_msg ELSE NULL END;
        ELSE /* not very old -> need additional check */ 
            v_sql := format ('SELECT 1 FROM %s r WHERE EXISTS (SELECT 1 FROM swmanagement.rounds_unfinished u WHERE u.id = r.sw_round_id::int8) LIMIT 1'
                , c.partrel);
            --RAISE INFO '%', v_sql; 
            EXECUTE v_sql INTO v_num;
            v_num := COALESCE (v_num, 0);
    
            IF (v_num = 0)
            THEN
                v_msg := 'removed: no unfinished rounds';
                v_err_msg := CASE WHEN p_check_only THEN v_msg ELSE NULL END;                        
            ELSE /* suspicious rows */
                v_msg := 'not removed because of: at least one unfinished round found';
                v_err_msg := v_msg;
            END IF;
        END IF;
        
        /* check on auto-vacuum */
        IF (v_err_msg IS NULL AND 
            EXISTS (SELECT 1 FROM pg_stat_activity
                 WHERE query ~* 'autovacuum' AND query ~* (v_part_name) AND pid != pg_backend_pid()) 
            )
        THEN
            v_msg := 'involved by parallel maintenance process. Skipped';
            v_err_msg := v_msg;
        END IF;
        
        /* log */
        IF (p_debug OR p_check_only) THEN 
            v_msg := CASE WHEN p_check_only THEN 'tested and must be '||v_msg ELSE v_msg END;
            PERFORM swsystem.fnc_log_partition_cleanup( v_part_name, c.dead_line, p_check_only, v_num, (v_err_msg IS NOT NULL), 
                    format('Partition %s of table %s is %s', v_part_name, c.tabrel::text, v_msg) );                
        END IF;
        
        /* remove */
        IF (v_err_msg IS NULL) THEN 
            SELECT public.drop_range_partition(c.partrel, TRUE) INTO v_part_name;
        
            /* save to history */
            INSERT INTO swsystem.partition_cleanup_history (partition_name, schema_name, table_name, dt_col_name, range_min, range_max)
            VALUES (v_part_name, c.schema_name, c.table_name, c.dt_col, c.range_min, c.range_max);
        
            RETURN jsonb_build_object('status', 'SUCCESS')
                || jsonb_build_object('result', 'removed')
                || jsonb_build_object('partition', v_part_name)
                || jsonb_build_object('skipped', format('%s partitions', v_count))
                || jsonb_build_object('skipped partitions', v_res);
        END IF;
    
        /* add to result */                
        v_res := v_res || jsonb_build_object( v_part_name, v_msg );
         
        v_count := v_count + 1;  
    END LOOP;
    
    v_res := jsonb_build_object('status', 'FAIL')             
            || jsonb_build_object('result', 'nothing to remove')
            || jsonb_build_object('skipped', format('%s partitions', v_count))
            || jsonb_build_object('skipped partitions', v_res);
    
    RETURN v_res;
END $function$;

SELECT cron.schedule('10 */3 * * *'
                , '/* Cleanup old data */ SELECT swsystem.fnc_clean_data();'
            );        
 
--rollback SELECT cron.unschedule(jobid) FROM cron.job WHERE command ILIKE '%fnc_clean_data%';
--rollback DROP FUNCTION swsystem.fnc_clean_data;
--rollback SELECT public.drop_partitions('swadapterb365.rounds', FALSE);


--changeset timur.luchkin:2022-03-21-SWS-32913-Disable-cron-job
--comment Disable cleaning pgcron job for swadapterb365.rounds
SET search_path TO cron;
   SELECT unschedule(jobid) FROM cron.job WHERE command = '/* Cleanup old data */ SELECT swsystem.fnc_clean_data();';
RESET search_path;

--rollback SET search_path TO cron;
--rollback    SELECT schedule('10 */3 * * *', '/* Cleanup old data */ SELECT swsystem.fnc_clean_data();');
--rollback RESET search_path;

