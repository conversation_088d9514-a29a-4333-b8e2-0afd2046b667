--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2022-11-05_SWS-36864-add-column-operator-country-to-session-history
--comment Add 'operator_country' fields for 'sessions_history_duplicates'
SET search_path TO swmanagement;
ALTER TABLE sessions_history_duplicates ADD COLUMN operator_country VARCHAR(6);
COMMENT ON COLUMN sessions_history_duplicates.operator_country IS 'Country player from operator';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE sessions_history_duplicates DROP COLUMN operator_country;
--rollback RESET search_path;

--changeset vladimir.minakov:2022-11-08-SWS-37715-update-lobby_session_id-limitation
--comment update lobby_session_id limitation to support IPM-mock
SET search_path = swmanagement;
ALTER TABLE spins_history ALTER COLUMN lobby_session_id TYPE VARCHAR(255);
ALTER TABLE spins_history_duplicates ALTER COLUMN lobby_session_id TYPE VARCHAR(255);
RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE spins_history ALTER COLUMN lobby_session_id TYPE VARCHAR(255);
RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE spins_history ALTER COLUMN lobby_session_id TYPE VARCHAR(255);
RESET search_path;

--rollback SELECT NOW();
