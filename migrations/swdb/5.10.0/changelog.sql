--liquibase formatted sql

--changeset timur.luchkin:2023-07-12-SWS-41249-fix-archive-ro endDelimiter:# stripComments:false
--comment Fix audits_login and audits_login_session tables in the swmanagement_archive_ro schema
DO
$$
DECLARE
   /*
      TLU: Fix tables in "swmanagement_archive_ro" created by mistake in "swdb\5.9.0\changelog.sql"
   */
   fs_name              TEXT;
   historical_schema    TEXT     := 'swmanagement';
   target_schema        TEXT     := 'swmanagement_archive_ro';
   target_tables        TEXT[]   := '{audits_login,audits_login_session}';
   target_table         TEXT;
BEGIN
   SET client_min_messages TO INFO;

   -- Check for the foreign server
   BEGIN
      SELECT srvname
      FROM   pg_foreign_server WHERE srvname ~* '^sw_hcl.+ro$'
      INTO   STRICT fs_name;
      RAISE INFO '[%] Foreign server is found. Proceed ...', To_Cha<PERSON>(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS');
   EXCEPTION
      WHEN no_data_found THEN
         RAISE INFO '[%] Foreign server not found. Will do emulation', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS');
      WHEN too_many_rows THEN
         RAISE EXCEPTION '[%] Too many foreign servers. Can''t choose one!', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS');
   END;

   -- Real foreign server (Prod/Stage)
   IF fs_name IS NOT NULL THEN
      FOREACH target_table IN ARRAY target_tables
      LOOP
         RAISE INFO '[%] Validate table %', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'), target_table;
         IF ((SELECT table_type FROM information_schema.tables WHERE table_schema = target_schema and table_name = target_table)) = 'BASE TABLE' THEN
            -- Drop normal table & create foreign one instead
            EXECUTE FORMAT('DROP TABLE %s.%s', target_schema, target_table);
            EXECUTE FORMAT('IMPORT FOREIGN SCHEMA %s LIMIT TO (%s.%s) FROM SERVER %s INTO %s', historical_schema, historical_schema, target_table, fs_name, target_schema);
            RAISE INFO '[%] Table % converted', To_Char(clock_timestamp(), 'YYYY-MM-DD HH24:MI:SS'), target_table;
         END IF;
      END LOOP;
   END IF;

   RESET client_min_messages;
END;
$$;

--rollback SELECT now();

--changeset timur.luchkin:2023-07-13-SWS-41249-fix-audits-pathman
--comment Additional fixes to audits_login and audits_login_session tables
--validCheckSum: 7:fca14f5d7046a517d83dfa54cc239ef6
SET SEARCH_PATH TO swsystem, public;

SELECT public.set_init_callback (
   relation        => 'swmanagement_archive.audits_login'::regclass,
   callback        => 'public.pathman_callback(jsonb)' );

SELECT public.create_range_partitions (
   parent_relid    => 'swmanagement_archive.audits_login'::regclass,
   expression      => 'ts',
   start_value     => date_trunc('week', '2010-01-04'::DATE),
   p_interval      => '7 days'::interval,
   p_count         => 1,
   partition_data  => false );

SELECT public.set_init_callback (
   relation        => 'swmanagement_archive.audits_login_session'::regclass,
   callback        => 'public.pathman_callback(jsonb)' );

SELECT public.create_range_partitions (
   parent_relid    => 'swmanagement_archive.audits_login_session'::regclass,
   expression      => 'started_at',
   start_value     => date_trunc('week', '2010-01-04'::DATE),
   p_interval      => '7 days'::interval,
   p_count         => 1,
   partition_data  => false );

SELECT partrel::text||' -> auto_off', set_auto(partrel, false) FROM public.pathman_config_params WHERE partrel =ANY ('{swmanagement.audits_login,swmanagement.audits_login_session,swmanagement_archive.audits_login,swmanagement_archive.audits_login_session}'::REGCLASS[]);
SELECT partrel::text||' -> parent_off', set_enable_parent(partrel, false) FROM public.pathman_config_params WHERE partrel =ANY ('{swmanagement.audits_login,swmanagement.audits_login_session,swmanagement_archive.audits_login,swmanagement_archive.audits_login_session}'::REGCLASS[]);
RESET search_path;

--rollback SELECT now();


--changeset vladimir.minakov:2023-07-20_SWS-41085-analyse-how-we-calculate-player_country-for-session
--comment Add 'operator_player_country' fields for 'sessions_history'
SET search_path TO swmanagement;
ALTER TABLE sessions_history ADD COLUMN operator_player_country VARCHAR(6);
COMMENT ON COLUMN sessions_history.operator_player_country IS 'Player country, provided by operator or IP';
ALTER TABLE sessions_history_duplicates ADD COLUMN operator_player_country VARCHAR(6);
COMMENT ON COLUMN sessions_history_duplicates.operator_player_country IS 'Player country, provided by operator or IP';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_player_country;
--rollback ALTER TABLE sessions_history_duplicates DROP COLUMN operator_player_country;
--rollback RESET search_path;

SET search_path = swmanagement_archive;
ALTER TABLE sessions_history ADD COLUMN operator_player_country VARCHAR(6);
COMMENT ON COLUMN sessions_history.operator_player_country IS 'Player country, provided by operator or IP';
RESET search_path;
--rollback SET search_path TO swmanagement_archive;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_player_country;
--rollback RESET search_path;

SET search_path = swmanagement_archive_ro;
ALTER TABLE sessions_history ADD COLUMN operator_player_country VARCHAR(6);
COMMENT ON COLUMN sessions_history.operator_player_country IS 'Player country, provided by operator or IP';
RESET search_path;
--rollback SET search_path TO swmanagement_archive_ro;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_player_country;
--rollback RESET search_path;
