--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset pavel.shamshurov:2020-09-17-SWS-XXXX-start-release-4.44.2
--comment label for 4.44.2
select now();
--rollback select now();

--changeset pavel.shamshurov:2020-09-17-SWB365-88-create-role-and-schema
--comment create role, schema
SET search_path = swsystem;
CREATE ROLE swadapterb365 PASSWORD 'changeme' LOGIN;
ALTER ROLE swadapterb365 SET search_path TO swadapterb365, public;
CREATE SCHEMA swadapterb365;
ALTER SCHEMA swadapterb365 OWNER TO swsystem;
GRANT USAGE ON SCHEMA swadapterb365 TO swadapterb365;
ALTER DEFAULT PRIVILEGES IN SCHEMA swadapterb365 FOR ROLE swsystem GRANT ALL ON TABLES TO swadapterb365;
ALTER DEFAULT PRIVILEGES IN SCHEMA swadapterb365 FOR ROLE swsystem GRANT ALL ON SEQUENCES TO swadapterb365;
ALTER DEFAULT PRIVILEGES IN SCHEMA swadapterb365 FOR ROLE swsystem GRANT EXECUTE ON FUNCTIONS TO swadapterb365;
ALTER DEFAULT PRIVILEGES IN SCHEMA swadapterb365 FOR ROLE swsystem GRANT USAGE ON TYPES TO swadapterb365;
RESET search_path;

--rollback SET search_path = swsystem;
--rollback ALTER DEFAULT PRIVILEGES IN SCHEMA swadapterb365 FOR ROLE swsystem REVOKE USAGE ON TYPES FROM swadapterb365;
--rollback ALTER DEFAULT PRIVILEGES IN SCHEMA swadapterb365 FOR ROLE swsystem REVOKE EXECUTE ON FUNCTIONS FROM swadapterb365;
--rollback ALTER DEFAULT PRIVILEGES IN SCHEMA swadapterb365 FOR ROLE swsystem REVOKE ALL ON SEQUENCES FROM swadapterb365;
--rollback ALTER DEFAULT PRIVILEGES IN SCHEMA swadapterb365 FOR ROLE swsystem REVOKE ALL ON TABLES FROM swadapterb365;
--rollback REVOKE USAGE ON SCHEMA swadapterb365 FROM swadapterb365;
--rollback DROP SCHEMA IF EXISTS swadapterb365;
--rollback ALTER ROLE swadapterb365 RESET search_path;
--rollback DROP ROLE IF EXISTS swadapterb365;
--rollback RESET search_path;

--changeset pavel.shamshurov:2020-09-17-SWB365-88-create-table
--comment create table rounds
SET search_path = swadapterb365;
CREATE TABLE IF NOT EXISTS rounds (
  operator_round_id VARCHAR(100) PRIMARY KEY,
  sw_round_id VARCHAR(100) NOT NULL,
  created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
  updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_rounds_sw_round_id ON rounds USING btree (sw_round_id);
RESET search_path;
--rollback SET search_path = swadapterb365;
--rollback DROP TABLE IF EXISTS rounds;
--rollback RESET search_path;
