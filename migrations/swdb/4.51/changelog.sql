--liquibase formatted sql

--changeset aleh.rudzko:2020-11-24-SWS-21970-add-limit-levels-management
--comment add limit levels table
SET search_path = swmanagement;
CREATE TABLE IF NOT EXISTS limit_levels (
    id SERIAL PRIMARY KEY,
    title varchar(255) NOT NULL,
    entity_id INTEGER NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE limit_levels IS 'Limit levels table';
COMMENT ON COLUMN limit_levels.id IS 'Limit levels ID';
COMMENT ON COLUMN limit_levels.title IS 'Limit levels title';
COMMENT ON COLUMN limit_levels.entity_id IS 'Reference to entity table';
COMMENT ON COLUMN limit_levels.created_at IS 'Created at date';
COMMENT ON COLUMN limit_levels.updated_at IS 'Updated at date';

ALTER TABLE ONLY limit_levels
    ADD FOREIGN KEY (entity_id) REFERENCES entities(id) ON UPDATE CASCADE ON DELETE CASCADE;

CREATE UNIQUE INDEX IF NOT EXISTS idx_limit_levels_title_entity_id on limit_levels (title, entity_id);
CREATE INDEX IF NOT EXISTS idx_limit_levels_entity_id ON limit_levels (entity_id);

UPDATE roles SET permissions = permissions - 'limit-level' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'limit-level:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:limit-level' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:limit-level:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:limit-level:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:limit-level:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:limit-level:update' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:limit-level:create", "keyentity:limit-level:view", "keyentity:limit-level", "keyentity:limit-level:delete", "keyentity:limit-level:update", "limit-level", "limit-level:view"]' WHERE id = 1;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP TABLE limit_levels;
--rollback UPDATE roles SET permissions = permissions - 'limit-level' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'limit-level:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:limit-level' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:limit-level:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:limit-level:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:limit-level:delete' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:limit-level:update' WHERE id = 1;
--rollback RESET search_path;

--changeset aleh.rudzko:2020-11-25-SWS-21973-add-entity-limit-levels-management
--comment add entity game limit levels table

SET search_path = swmanagement;

CREATE TABLE IF NOT EXISTS entity_game_limit_levels (
    id SERIAL PRIMARY KEY,
    level_id INTEGER NOT NULL,
    entity_game_id INTEGER NOT NULL,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    hidden BOOLEAN NOT NULL DEFAULT FALSE,
    currency char(3),
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE entity_game_limit_levels IS 'Entity limit levels table';
COMMENT ON COLUMN entity_game_limit_levels.id IS 'Entity limit levels ID';
COMMENT ON COLUMN entity_game_limit_levels.level_id IS 'Reference to label table';
COMMENT ON COLUMN entity_game_limit_levels.entity_game_id IS 'Reference to entity game table';
COMMENT ON COLUMN entity_game_limit_levels.is_default IS 'Indicates default level';
COMMENT ON COLUMN entity_game_limit_levels.hidden IS 'Indicates that level hidden for entity';
COMMENT ON COLUMN entity_game_limit_levels.currency IS 'Currency which hidden for level';
COMMENT ON COLUMN entity_game_limit_levels.created_at IS 'Created at date';
COMMENT ON COLUMN entity_game_limit_levels.updated_at IS 'Updated at date';

ALTER TABLE ONLY entity_game_limit_levels
    ADD FOREIGN KEY (entity_game_id) REFERENCES entity_games(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY entity_game_limit_levels
    ADD FOREIGN KEY (level_id) REFERENCES limit_levels(id) ON UPDATE CASCADE ON DELETE CASCADE;

CREATE INDEX IF NOT EXISTS idx_entity_game_limit_levels_entity_game_id ON entity_game_limit_levels (entity_game_id);
CREATE INDEX IF NOT EXISTS idx_entity_game_limit_levels_level_id ON entity_game_limit_levels (level_id);

UPDATE roles SET permissions = permissions - 'keyentity:entity-game-limit-level' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:entity-game-limit-level:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:entity-game-limit-level:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:entity-game-limit-level:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'entity-game-limit-level:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'entity-game-limit-level' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:entity-game-limit-level:create", "keyentity:entity-game-limit-level:view", "keyentity:entity-game-limit-level", "entity-game-limit-level", "entity-game-limit-level:view", "keyentity:entity-game-limit-level:delete"]' WHERE id = 1;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP TABLE entity_game_limit_levels;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:entity-game-limit-level' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:entity-game-limit-level:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:entity-game-limit-level:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:entity-game-limit-level:delete' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entity-game-limit-level:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'entity-game-limit-level' WHERE id = 1;
--rollback RESET search_path;

--changeset valdis.akmens:2020-12-10-DEVOPS-11637-jobs-for-cleanup-autotests
--comment Config table for autotests cleanup
SET search_path = swsystem;

CREATE TABLE IF NOT EXISTS autotests_cleanup_config
(
    clean_up_mask       VARCHAR,
    lifespan            INTERVAL NOT NULL,
    is_active bool      NOT NULL DEFAULT false,
    inserted_at         timestamp NOT NULL DEFAULT timezone('UTC', now())
);
COMMENT ON TABLE autotests_cleanup_config                  IS 'Config for autotests generated data clean-up';
COMMENT ON COLUMN autotests_cleanup_config.clean_up_mask   IS 'Mask for filtering entities by name';
COMMENT ON COLUMN autotests_cleanup_config.lifespan        IS 'Period after which data is cleaned.';
COMMENT ON COLUMN autotests_cleanup_config.is_active       IS 'Is job active.';
COMMENT ON COLUMN autotests_cleanup_config.inserted_at     IS 'Time when the setting was added';
RESET search_path;

--rollback SET search_path = swsystem;
--rollback DROP TABLE IF EXISTS autotests_cleanup_config;
--rollback RESET search_path;

--changeset valdis.akmens:2020-12-15-DEVOPS-11755-update-monitoring-aggregation endDelimiter:# stripComments:false
--comment Update monitoring aggregation

SET search_path TO monitoring;

ALTER FUNCTION fnc_aggr_rounds() RENAME TO fnc_aggr_rounds_before_4_51_0;

CREATE OR REPLACE FUNCTION fnc_aggr_rounds()
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
*******************************************************************************
    Object Name:   fnc_aggr_rounds
    Purpose    :   Aggregate aggr_rounds table. Table is used for monitoring tools.
    History    :
        1.0.0
            Date    :  May 20, 2020
            Authors : Valdis Akmens
            Notes   : Release (SWDB-106)
        1.0.1
            Date    :  Dec 15, 2020
            Authors : Valdis Akmens
            Notes   : Update monitoring aggregation (DEVOPS-11755)
    Sample run:
      SELECT * FROM monitoring.fnc_aggr_rounds();
*******************************************************************************
*/
DECLARE
    v_lock_id               INTEGER := 'monitoring.aggr_rounds'::regclass::integer;
    v_max_finished_at       TIMESTAMP;
    v_min_finished_at       TIMESTAMP;
    v_counter               BIGINT;
    v_off_users             VARCHAR[]:='{"kafka_offloader", "redis_game_offloader"}';
    v_lower_limit           TIMESTAMP;
    v_upper_limit           TIMESTAMP;
    v_huge_interval         INTERVAL:= '10 minutes'::INTERVAL;
    v_partiton_prune        TIMESTAMP;
    v_lower_limit_prev      TIMESTAMP;
    v_upper_limit_prev      TIMESTAMP;
    v_exec_sql              VARCHAR;
BEGIN

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job started'; RETURN NEXT;

    /* Check if MDB */
    IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
    END IF;

    /* Check if another job is running*/
    IF NOT (SELECT pg_try_advisory_xact_lock(v_lock_id)) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Another function is running. Skip'; RETURN NEXT;
        RETURN;
    END IF;

    v_lower_limit:=(SELECT COALESCE(MAX(max_finished_at),date_trunc('MINUTE',now())) FROM monitoring.aggr_rounds);
    v_upper_limit:=(SELECT date_trunc('MINUTE', ts_upper_limit) + FLOOR(date_part('SECOND', ts_upper_limit))::INTEGER / 20 * interval '20 sec' AS ts_upper_20sec_limit
                    FROM  (
                                SELECT LEAST((SELECT MIN(xact_start)
                                                FROM pg_stat_activity sa
                                                WHERE state <> 'idle'::text
                                                AND usename = ANY(v_off_users)
                                            ), Now() )::TIMESTAMP AS ts_upper_limit
                            ) AS x);
    v_partiton_prune:=(SELECT date_trunc('MINUTE',now()) - v_huge_interval);

        /* Automatically prevent too huge intervals */
    IF (v_upper_limit - v_lower_limit) > v_huge_interval THEN
        v_lower_limit := v_upper_limit - v_huge_interval;
    END IF;

    v_lower_limit_prev:= v_lower_limit - '40 SECONDS'::INTERVAL;
    v_upper_limit_prev:= v_lower_limit;
    --RAISE INFO 'v_lower_limit = % ; v_upper_limit = % ; v_lower_limit_prev= % ; v_upper_limit_prev = %', v_lower_limit,v_upper_limit,v_lower_limit_prev,v_upper_limit_prev;
    v_exec_sql:= '
    WITH cte_xrates AS (
            SELECT r.currency_code, r.rate
            FROM swmanagement.currency_rates AS r
            JOIN (
                    SELECT currency_code, MAX(rate_date) AS rate_date
                    FROM swmanagement.currency_rates
                    WHERE rate_date <= CURRENT_DATE
                    GROUP BY currency_code
                )
                    AS m ON r.currency_code = m.currency_code AND r.rate_date = m.rate_date
            ORDER BY r.currency_code, r.rate_date DESC
        ), cte_prev AS (
            SELECT DISTINCT x.round_id
            FROM swmanagement.spins_history AS x
            WHERE
                x.ts > '''||v_lower_limit_prev||'''
            AND x.ts < '''||v_upper_limit_prev||'''
        ),
    cte_insert AS (
        INSERT INTO monitoring.aggr_rounds (ts_period, brand_id, game_code, player_code, currency, is_test_round, is_test_brand, device_id, rounds_cnt,rounds_fin_cnt, events_cnt, sessions_cnt, max_unload_lag, bet_sum_base, win_sum_base, jp_contrib_sum_base, jp_win_sum_base, bet_sum, win_sum, jp_contrib_sum, jp_win_sum, max_finished_at)
        SELECT date_trunc(''MINUTE'', h.ts) + FLOOR(date_part(''SECOND'', h.ts))::INTEGER / 20 * interval ''20 sec'' AS ts_20sec
            ,h.brand_id                                              AS brand_id
            ,h.game_code                                             AS game_code
            ,h.player_code                                           AS player_code
            ,h.currency                                              AS currency_code
            ,h.test                                                  AS is_test
            ,e.is_test                                               AS is_test_brand
            ,h.device_id                                             AS device
            --
            ,COALESCE(COUNT(DISTINCT
                    CASE WHEN
                            NOT EXISTS(SELECT pr.round_id
                                        FROM cte_prev AS pr
                                        WHERE pr.round_id = h.round_id
                                        )
                        THEN
                        h.round_id
                        END
                    ),0)                                              AS rounds_cnt
            ,COALESCE(COUNT(DISTINCT
                    CASE WHEN h.round_ended = TRUE THEN h.round_id END
                    ),0)                                              AS rounds_fin_cnt
            ,COALESCE(COUNT(*)::BIGINT,0)                             AS events_cnt
            ,COALESCE(COUNT(DISTINCT
                    CASE WHEN
                            NOT EXISTS(SELECT pr_2.round_id
                                        FROM cte_prev AS pr_2
                                        WHERE pr_2.round_id = h.round_id
                                        )
                        THEN
                        h.session_id
                        END
                    ),0)                                                    AS sessions_cnt
            ,MAX(GREATEST(h.inserted_at, h.ts) - h.ts)                      AS max_unload_lag
            --
            ,COALESCE(SUM(h.bet * xr.rate)::NUMERIC(20,2),0)                 AS bet_sum_base
            ,COALESCE(SUM(h.win * xr.rate)::NUMERIC(20,2),0)                 AS win_sum_base
            ,COALESCE(SUM(h.total_jp_contribution * xr.rate)::NUMERIC(20,2),0)     AS jp_contrib_sum_base
            ,COALESCE(SUM(h.total_jp_win * xr.rate)::NUMERIC(20,2),0)              AS jp_win_sum_base
            --
            ,COALESCE(SUM(h.bet),0)                                         AS bet_sum
            ,COALESCE(SUM(h.win),0)                                         AS win_sum
            ,COALESCE(SUM(h.total_jp_contribution),0)                       AS jp_contrib_sum
            ,COALESCE(SUM(h.total_jp_win),0)                                AS jp_win_sum
            --
            ,MAX(h.inserted_at)                                             AS max_finished_at
        FROM   swmanagement.spins_history AS h
        JOIN   swmanagement.entities        AS e ON e.id = h.brand_id
        JOIN   cte_xrates                   AS xr ON h.currency = xr.currency_code
        WHERE
            h.ts >= '''||v_partiton_prune||'''
        AND COALESCE(h.inserted_at, h.ts) > '''||v_lower_limit||'''
        AND COALESCE(h.inserted_at, h.ts) < '''||v_upper_limit||'''
        GROUP BY
            date_trunc(''MINUTE'', h.ts) + FLOOR(date_part(''SECOND'', h.ts))::INTEGER / 20 * interval ''20 sec''
            ,h.brand_id
            ,h.game_code
            ,h.player_code
            ,h.currency
            ,h.test
            ,e.is_test
            ,h.device_id
        RETURNING *
        )
    SELECT MAX(max_finished_at), MIN(max_finished_at),COUNT(*)
    FROM cte_insert ';

    --RAISE INFO 'v_exec_sql: %', v_exec_sql;

    EXECUTE v_exec_sql
    INTO v_max_finished_at, v_min_finished_at,v_counter;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "aggr_rounds" finished. Rows processed: '||quote_nullable(COALESCE(v_counter, 0))||' Timeframe processed: ('||quote_nullable(COALESCE(v_min_finished_at,v_lower_limit))||'; '||quote_nullable(COALESCE(v_max_finished_at,v_upper_limit))||')'; RETURN NEXT;

    v_exec_sql:='
    WITH cte_insert AS (
        INSERT INTO monitoring.aggr_rounds_external (ts_period, player_code,is_test_round, game_provider, rounds_cnt)
        SELECT date_trunc(''MINUTE'', inserted_at) + FLOOR(date_part(''SECOND'', inserted_at))::INTEGER / 20 * interval ''20 sec'' AS ts_20sec
            ,player_code
            ,is_test
            ,CASE game_provider_code
                WHEN ''IG'' THEN ''ig''
                ELSE ''ext_other''
            END::VARCHAR(20)             AS group_type
            ,Count(Distinct(round_id))    AS rounds_cnt
        FROM   swadaptergos.ext_bet_win_history
        WHERE
            inserted_at >  '''||v_lower_limit||'''::TIMESTAMP
        AND  inserted_at <  '''||v_upper_limit||'''::TIMESTAMP
        GROUP BY date_trunc(''MINUTE'', inserted_at) + FLOOR(date_part(''SECOND'', inserted_at))::INTEGER / 20 * interval ''20sec''
            ,player_code
            ,is_test
            ,CASE game_provider_code
                WHEN ''IG'' THEN ''ig''
                ELSE ''ext_other''
            END
        RETURNING *
        )
    SELECT COUNT(*)
    FROM cte_insert ';
    EXECUTE v_exec_sql
    INTO v_counter;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "aggr_rounds_external" finished. Rows processed: '||quote_nullable(COALESCE(v_counter, 0)); RETURN NEXT;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job finished'; RETURN NEXT;
    RETURN;
END;
$function$
;

RESET search_path;
--rollback SET search_path TO monitoring;
--rollback DROP FUNCTION IF EXISTS fnc_aggr_rounds();
--rollback ALTER FUNCTION fnc_aggr_rounds_before_4_51_0() RENAME TO fnc_aggr_rounds;
--rollback RESET search_path;


--changeset timur.luchkin:2020-12-17__SWDB-165 runInTransaction:false
--comment Drop index duplicates
SET search_path TO swjackpot;
   DROP INDEX IF EXISTS jp_contribution_unique;
   DROP INDEX IF EXISTS idx_jp_contribution_log_unique;
   DROP INDEX IF EXISTS jp_contribution_log_unique;
   DROP INDEX IF EXISTS idx_jp_win_log_unique;
   DROP INDEX IF EXISTS jp_instance_type_id;
   DROP INDEX IF EXISTS remote_jp_contribution_log_unique;
   DROP INDEX IF EXISTS idx_remote_jp_win_log_unique;

SET search_path TO swmanagement;
   DROP INDEX IF EXISTS agents_brand_id;
   DROP INDEX IF EXISTS game_category_brand_ukey;
   DROP INDEX IF EXISTS game_categories_brand_id;
   DROP INDEX IF EXISTS merchants_type_code_key;
   DROP INDEX IF EXISTS roles_id;
   DROP INDEX IF EXISTS roles_entity_id;
   DROP INDEX IF EXISTS user_roles_role_id;
   DROP INDEX IF EXISTS players_agent_id;
   DROP INDEX IF EXISTS players_brand_id;
   DROP INDEX IF EXISTS players_brand_id_email;
   ALTER TABLE ONLY players DROP CONSTRAINT IF EXISTS players_brand_id_email_key;
   DROP INDEX IF EXISTS player_password_resets_player_id;
   DROP INDEX IF EXISTS player_sessions_player_id;
   DROP INDEX IF EXISTS payment_methods_brand_id;
   DROP INDEX IF EXISTS idx_payment_method_brand_id_code_unique;
   DROP INDEX IF EXISTS "payment_method_brandId_code_key";
   DROP INDEX IF EXISTS payments_extrx_brand_key;
   DROP INDEX IF EXISTS notifications_id;
   DROP INDEX IF EXISTS wallet_win_bet_brand_id_payment_date_currency;
   DROP INDEX IF EXISTS lobby_brand_ukey;
   DROP INDEX IF EXISTS promotions_brand_id;
   DROP INDEX IF EXISTS promotions_creator_id;
   DROP INDEX IF EXISTS promotions_updater_id;
   DROP INDEX IF EXISTS jurisdictions_creator_id;
   DROP INDEX IF EXISTS jurisdictions_updater_id;
   DROP INDEX IF EXISTS terminal_brand_ukey;
   DROP INDEX IF EXISTS notification_receivers_receiver;
   DROP INDEX IF EXISTS site_tokens_brand_id;
   DROP INDEX IF EXISTS idx_promotion_reward_rebate_promo_id;
   DROP INDEX IF EXISTS idx_promotion_reward_rebate_inserted_at;
   DROP INDEX IF EXISTS idx_promotion_reward_freebet_inserted_at;
   DROP INDEX IF EXISTS idx_promotion_reward_freebet_promo_id;
   DROP INDEX IF EXISTS available_sites_entity_id;
   DROP INDEX IF EXISTS idx_promotion_reward_virtual_money_id;
   DROP INDEX IF EXISTS idx_promotion_reward_virtual_money_at;
   DROP INDEX IF EXISTS player_responsible_gaming_brand_id_player_code_jurisdiction;
   DROP INDEX IF EXISTS player_responsible_gaming_settings_updated_at;
   DROP INDEX IF EXISTS jurisdiction_entity_jurisdiction_id_entity_id;
   DROP INDEX IF EXISTS wallet_win_bet_payment_date_idx;
RESET search_path;

--rollback   SET search_path TO swjackpot;
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS jp_contribution_unique ON jp_contribution_log (trx_id, jackpot_id, pool);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS idx_jp_contribution_log_unique ON jp_contribution_log (trx_id, jackpot_id, pool);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS jp_contribution_log_unique ON jp_contribution_log (trx_id, jackpot_id, pool);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS idx_jp_win_log_unique ON jp_win_log (trx_id, jackpot_id, pool, event_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS jp_instance_type_id ON jp_instance (type_id);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS remote_jp_contribution_log_unique ON remote_jp_contribution_log (trx_id, jackpot_id, pool);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS idx_remote_jp_win_log_unique ON remote_jp_win_log (trx_id, jackpot_id, pool, event_id);
--rollback     SET search_path TO swmanagement;
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS agents_brand_id ON agents USING btree (brand_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS game_categories_brand_id ON game_categories USING btree (brand_id);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS game_category_brand_ukey ON game_categories USING btree (title, brand_id);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS merchants_type_code_key ON merchants USING btree (type, code);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS players_agent_id ON players USING btree (agent_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS players_brand_id ON players USING btree (brand_id);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS players_brand_id_email ON players USING btree (brand_id, email);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS roles_entity_id ON roles USING btree (entity_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS roles_id ON roles USING btree (id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS user_roles_role_id ON user_roles USING btree (role_id);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS player_responsible_gaming_brand_id_player_code_jurisdiction ON player_responsible_gaming USING btree (brand_id, player_code, jurisdiction);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS player_responsible_gaming_settings_updated_at ON player_responsible_gaming_settings USING btree (updated_at);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS jurisdiction_entity_jurisdiction_id_entity_id ON jurisdiction_entity USING btree (jurisdiction_id, entity_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS idx_promotion_reward_virtual_money_at ON promotion_reward_virtual_money USING btree (inserted_at);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS idx_promotion_reward_virtual_money_id ON promotion_reward_virtual_money USING btree (promo_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS available_sites_entity_id ON available_sites USING btree (entity_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS idx_promotion_reward_freebet_inserted_at ON promotion_reward_freebets USING btree (inserted_at);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS idx_promotion_reward_freebet_promo_id ON promotion_reward_freebets USING btree (promo_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS idx_promotion_reward_rebate_inserted_at ON promotion_reward_rebates USING btree (inserted_at);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS idx_promotion_reward_rebate_promo_id ON promotion_reward_rebates USING btree (promo_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS site_tokens_brand_id ON site_tokens USING btree (brand_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS notification_receivers_receiver ON notification_receivers USING btree (receiver);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS terminal_brand_ukey ON terminals USING btree (title, brand_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS jurisdictions_creator_id ON jurisdictions USING btree (creator_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS jurisdictions_updater_id ON jurisdictions USING btree (updater_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS promotions_brand_id ON promotions USING btree (brand_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS promotions_creator_id ON promotions USING btree (creator_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS promotions_updater_id ON promotions USING btree (updater_id);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS lobby_brand_ukey ON lobbies USING btree (title, brand_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS wallet_win_bet_brand_id_payment_date_currency ON wallet_win_bet USING btree (brand_id, payment_date, currency) WITH (fillfactor='100');
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS notifications_id ON notifications USING btree (id);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS payments_extrx_brand_key ON payments USING btree (ext_trx_id, brand_id);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS idx_payment_method_brand_id_code_unique ON payment_methods USING btree (brand_id, code);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS payment_methods_brand_id ON payment_methods USING btree (brand_id);
--rollback     CREATE UNIQUE  INDEX CONCURRENTLY IF NOT EXISTS "payment_method_brandId_code_key" ON payment_methods USING btree (brand_id, code);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS player_sessions_player_id ON player_sessions USING btree (player_id);
--rollback     CREATE         INDEX CONCURRENTLY IF NOT EXISTS player_password_resets_player_id ON player_password_resets USING btree (player_id);
--rollback     ALTER TABLE ONLY players ADD CONSTRAINT players_brand_id_email_key UNIQUE (brand_id, email);
--rollback   RESET search_path;
