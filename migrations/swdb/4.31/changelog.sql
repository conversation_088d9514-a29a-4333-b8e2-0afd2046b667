--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement, public;
--example: CREATE TABLE tmp(id int);
--example: --rollback DROP TABLE tmp;


--changeset andrey.shmigiro:2020-01-14-SWS-XXXX-start-release-4.31.0
--comment label for 4.31.0
select now();
--rollback select now();


--changeset vera.k<PERSON><PERSON><PERSON>kova:2020-02-13-SWS-15667-local-jp
--comment Mark jackpot that are owned by operator
SET search_path TO swjackpot;
ALTER TABLE jp_instance ADD COLUMN is_owned BOOLEAN NOT NULL DEFAULT false;
COMMENT ON COLUMN jp_instance.is_owned IS 'Indicates if jackpot instance is owned by some operator';
RESET search_path;
--rollback SET search_path TO swjackpot;
--rollback ALTER TABLE jp_instance DROP COLUMN is_owned;
--rollback RESET search_path;


--changeset stepanov.aleksey:2020-01-29-SWS-15527
--comment Change Title for Game Category per Lobby function
SET search_path TO swmanagement;
ALTER TABLE lobby_menu_items ADD COLUMN translations JSONB;
ALTER TABLE lobby_menu_items ADD COLUMN icon TEXT;
ALTER TABLE lobby_menu_items ADD COLUMN entity_id INTEGER;
COMMENT ON COLUMN lobby_menu_items.translations IS 'Translations for lobby';
COMMENT ON COLUMN lobby_menu_items.icon IS 'Lobby item icon';
COMMENT ON COLUMN lobby_menu_items.entity_id IS 'Id of entity from entities table';

ALTER TABLE lobby_menu_items
    ADD CONSTRAINT lobby_menu_items_entity_id_fkey
        FOREIGN KEY (entity_id) REFERENCES entities(id) ON UPDATE NO ACTION ON DELETE CASCADE;
ALTER TABLE lobby_menu_items
    ADD CONSTRAINT entity_id_lobby_id_game_category_id UNIQUE (entity_id, lobby_id, game_category_id);
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE lobby_menu_items DROP COLUMN translations;
--rollback ALTER TABLE lobby_menu_items DROP COLUMN icon;
--rollback ALTER TABLE lobby_menu_items DROP COLUMN entity_id;
--rollback RESET search_path;


--changeset stepanov.aleksey:2020-01-27-SWS-15850
--comment Add flag to communicate type of video player provider
SET search_path TO swmanagement;
UPDATE games SET features = jsonb_set("features", '{live, providerSettings, player}', jsonb '"playtech"') WHERE features @> '{"live": {"provider": "pt"}}';
UPDATE games SET features = jsonb_set("features", '{live, providerSettings, player}', jsonb '"nanocosmos"') WHERE features @> '{"live": {"provider": "ezugi"}}';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback UPDATE games set features=json_strip_nulls(json(jsonb_set("features", '{live, providerSettings, player}', jsonb 'null'))) where features @> '{"live": {"provider": "pt"}}' OR features @> '{"live": {"provider": "ezugi"}}';
--rollback RESET search_path;


--changeset sergey.malkov:2020-01-31-SWS-15753-new-column
--comment Write information about player's domain to session history
SET search_path TO swmanagement;
ALTER TABLE sessions_history ADD COLUMN referrer CHARACTER VARYING(255);
COMMENT ON COLUMN sessions_history.referrer IS 'Operator''s site url that player comes from';
ALTER TABLE sessions_history_duplicates ADD COLUMN referrer CHARACTER VARYING(255);
COMMENT ON COLUMN sessions_history_duplicates.referrer IS 'Operator''s site url that player comes from';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE sessions_history DROP COLUMN referrer;
--rollback ALTER TABLE sessions_history_duplicates DROP COLUMN referrer;
--rollback RESET search_path;


--changeset vera.kruhliakova:2020-02-04-SWS-15760-rtp-features
--comment Rename rtp/rtpRange feature to baseRTP/baseRTPRange
SET search_path TO swmanagement;
UPDATE games SET features = features || jsonb_build_object('baseRTP', (features ->> 'rtp')::numeric) WHERE features - 'rtp' != features;
UPDATE games SET features = features || jsonb_build_object('baseRTPRange', (features ->> 'rtpRange')::json) WHERE features - 'rtpRange' != features;
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback UPDATE games SET features = features - 'baseRTP' - 'baseRTPRange' WHERE features ? 'rtp' OR features ? 'rtpRange';
--rollback RESET search_path;


--changeset vera.kruhliakova:2020-02-10-SWS-15531-exposure-threshold
--comment Add live baccarat exposure limit threshold
SET search_path TO swmanagement;
UPDATE games set features=jsonb_set("features", '{live, providerSettings, exposureThreshold}', jsonb '60') where provider_game_code in ('sw_live_cbac','sw_live_ncbac');
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback UPDATE games set features=json_strip_nulls(json(jsonb_set("features", '{live, providerSettings, exposureThreshold}', jsonb 'null'))) where provider_game_code in ('sw_live_cbac','sw_live_ncbac');
--rollback RESET search_path;


--changeset sergey.malkov:2020-01-31-SWS-15753-new-column-for-archive-data
--comment Write information about player's domain to session history
SET search_path TO swmanagement_archive;
ALTER TABLE swmanagement_archive.sessions_history ADD COLUMN referrer CHARACTER VARYING(255);
COMMENT ON COLUMN swmanagement_archive.sessions_history.referrer IS 'Operator''s site url that player comes from';
RESET search_path;
--rollback SET search_path TO swmanagement_archive;
--rollback ALTER TABLE swmanagement_archive.sessions_history DROP COLUMN referrer;
--rollback RESET search_path;
