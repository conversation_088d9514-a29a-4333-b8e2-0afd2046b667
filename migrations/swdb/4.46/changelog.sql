--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset stepanov.aleksey:2020-09-21-SWS-XXXX-start-release-4.46.0
--comment label for 4.46.0
select now();
--rollback select now();

--changeset stepanov.aleksey:2020-09-21-SWS-21127-add-permission
--comment Add "hub:studio" permission
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'hub:studio' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["hub:studio"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'hub:studio' WHERE id = 1;
--rollback RESET search_path;

--changeset aleh.rudzko:2020-09-23-SWS-21529-move-stake-ranges-to-db
--comment create table for stake range and fill it, add permission
SET search_path = swmanagement;

UPDATE roles SET permissions = permissions - 'stake-range' where id = 1;
UPDATE roles SET permissions = permissions || '["stake-range"]'::jsonb WHERE id = 1;

CREATE TABLE IF NOT EXISTS stake_ranges (
    currency CHAR(3) PRIMARY KEY,
    coin_bets JSONB NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);

COMMENT ON TABLE stake_ranges IS 'Stake ranges table for coinBets per currency';
COMMENT ON COLUMN stake_ranges.currency IS 'Currency code';
COMMENT ON COLUMN stake_ranges.coin_bets IS 'Coin bets range';

INSERT INTO stake_ranges(currency, coin_bets, created_at, updated_at) VALUES
('EUR', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('USD', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('GBP', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('AUD', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('AZN', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('BGN', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('BND', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('CAD', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('CHF', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('GEL', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('NZD', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('SGD', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('BMD', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('XXX', '[0.01,0.02,0.03,0.05,0.08,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,60,80,90,100,150,200,250,300,400,500,600]', now(), now()),
('BRL', '[0.05,0.1,0.2,0.3,0.4,0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,300,400,450,500,750,1000,1500,2000,2500,3000]', now(), now()),
('ILS', '[0.05,0.1,0.2,0.3,0.4,0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,300,400,450,500,750,1000,1500,2000,2500,3000]', now(), now()),
('MYR', '[0.05,0.1,0.2,0.3,0.4,0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,300,400,450,500,750,1000,1500,2000,2500,3000]', now(), now()),
('PEN', '[0.05,0.1,0.2,0.3,0.4,0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,300,400,450,500,750,1000,1500,2000,2500,3000]', now(), now()),
('PLN', '[0.05,0.1,0.2,0.3,0.4,0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,300,400,450,500,750,1000,1500,2000,2500,3000]', now(), now()),
('TRY', '[0.05,0.1,0.2,0.3,0.4,0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,300,400,450,500,750,1000,1500,2000,2500,3000]', now(), now()),
('GHS', '[0.05,0.1,0.2,0.3,0.4,0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,300,400,450,500,750,1000,1500,2000,2500,3000]', now(), now()),
('ARS', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('CNY', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('DKK', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('HKD', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('HRK', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('MAD', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('MOP', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('NOK', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('SEK', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('VEF', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('ZAR', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('ZMW', '[0.01,0.02,0.03,0.05,0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('CZK', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('DOP', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('HNL', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('INR', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('KGS', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('MDL', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('NIO', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('PHP', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('RUB', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('THB', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('TWD', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('UAH', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('UYU', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('VES', '[0.5,1,2,3,4,5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,3000,4000,4500,5000,7500,10000,15000,20000,25000,30000]', now(), now()),
('ISK', '[1,2,3,5,8,10,20,30,50,80,100,200,300,500,800,1000,2000,3000,5000,6000,8000,9000,10000,15000,20000,25000,30000,40000,50000,60000]', now(), now()),
('JPY', '[1,2,3,5,8,10,20,30,50,80,100,200,300,500,800,1000,2000,3000,5000,6000,8000,9000,10000,15000,20000,25000,30000,40000,50000,60000]', now(), now()),
('RSD', '[1,2,3,5,8,10,20,30,50,80,100,200,300,500,800,1000,2000,3000,5000,6000,8000,9000,10000,15000,20000,25000,30000,40000,50000,60000]', now(), now()),
('KES', '[1,2,3,5,8,10,20,30,50,80,100,200,300,500,800,1000,2000,3000,5000,6000,8000,9000,10000,15000,20000,25000,30000,40000,50000,60000]', now(), now()),
('CLP', '[5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,4000,5000,10000,15000,25000,30000,40000,45000,50000,75000,100000,125000,150000,200000,250000,300000]', now(), now()),
('HUF', '[5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,4000,5000,10000,15000,25000,30000,40000,45000,50000,75000,100000,125000,150000,200000,250000,300000]', now(), now()),
('KZT', '[5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,4000,5000,10000,15000,25000,30000,40000,45000,50000,75000,100000,125000,150000,200000,250000,300000]', now(), now()),
('XOF', '[5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,4000,5000,10000,15000,25000,30000,40000,45000,50000,75000,100000,125000,150000,200000,250000,300000]', now(), now()),
('CRC', '[5,10,20,30,40,50,100,150,250,400,500,1000,1500,2500,4000,5000,10000,15000,25000,30000,40000,45000,50000,75000,100000,125000,150000,200000,250000,300000]', now(), now()),
('KRW', '[10,20,30,50,80,100,200,300,500,800,1000,2000,3000,5000,8000,10000,20000,30000,50000,60000,80000,90000,100000,150000,200000,250000,300000,400000,500000,600000]', now(), now()),
('COP', '[10,20,30,50,80,100,200,300,500,800,1000,2000,3000,5000,8000,10000,20000,30000,50000,60000,80000,90000,100000,150000,200000,250000,300000,400000,500000,600000]', now(), now()),
('MNT', '[10,20,30,50,80,100,200,300,500,800,1000,2000,3000,5000,8000,10000,20000,30000,50000,60000,80000,90000,100000,150000,200000,250000,300000,400000,500000,600000]', now(), now()),
('TZS', '[10,20,30,50,80,100,200,300,500,800,1000,2000,3000,5000,8000,10000,20000,30000,50000,60000,80000,90000,100000,150000,200000,250000,300000,400000,500000,600000]', now(), now()),
('MMK', '[10,20,30,50,80,100,200,300,500,800,1000,2000,3000,5000,8000,10000,20000,30000,50000,60000,80000,90000,100000,150000,200000,250000,300000,400000,500000,600000]', now(), now()),
('PYG', '[50,100,150,250,400,500,1000,1500,2500,4000,5000,10000,15000,25000,40000,50000,100000,150000,250000,300000,400000,450000,500000,750000,1000000,1250000,1500000,2000000,2500000,3000000]', now(), now()),
('IDR', '[10,20,30,50,80,100,200,300,500,800,1000,2000,3000,5000,8000,10000,20000,30000,50000,80000,100000,200000,300000,500000,600000,800000,900000,1000000,1500000,2000000,2500000,3000000,4000000,5000000,6000000]', now(), now()),
('VND', '[250,500,750,1500,2000,2500,5000,7500,15000,20000,25000,50000,75000,125000,200000,250000,500000,750000,1250000,1500000,2000000,2250000,2500000,3750000,5000000,6250000,7500000,10000000,12500000,15000000]', now(), now()),
('IDS', '[0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('RUP', '[0.1,0.2,0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,100,200,300,500,600,800,900,1000,1500,2000,2500,3000,4000,5000,6000]', now(), now()),
('VNS', '[0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,150,200,250,500,750,1500,2000,2500,4000,5000,6500,7500,10000,15000]', now(), now()),
('VDO', '[0.3,0.5,0.8,1,2,3,5,8,10,20,30,50,80,150,200,250,500,750,1500,2000,2500,4000,5000,6500,7500,10000,15000]', now(), now()),
('MXN', '[0.01,0.03,0.05,0.1,0.2,0.3,0.6,0.9,2,3,6,9,20,30,60,90,150,250,300,600,900,1500,2000,2500,3000,4500,6000,7500,9000,10000,15000,20000]', now(), now()),
('BNS', '[10,20,30,50,80,100,200,300,500,800,1000,2000,3000,5000,8000,10000,20000,30000,50000,60000,80000,90000,100000,150000,200000,250000,300000,400000,500000,600000]', now(), now()),
('RON', '[0.01,0.02,0.03,0.05,0.1,0.15,0.25,0.4,1,2,3,4,5,10,15,25,40,50,100,150,250,300,400,450,500,750,1000,1250,1500]', now(), now());

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'stake-range' where id = 1;
--rollback DROP TABLE IF EXISTS stake_ranges;
--rollback RESET search_path = swmanagement;

--changeset nataliia.pliashko:2020-09-23-SWS-21722-add-new-permissions
--comment Add permissions for show/hide Debits and Credits columns in Player report
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'bi:report:player-show-hide-column:debits-credits' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:player-show-hide-column:debits-credits' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["keyentity:bi:report:player-show-hide-column:debits-credits", "bi:report:player-show-hide-column:debits-credits"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:player-show-hide-column:debits-credits' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:player-show-hide-column:debits-credits' WHERE id = 1;
--rollback RESET search_path;


--changeset nataliia.pliashko:2020-09-22-SWS-21531-new-permissions-for-integration-tests
--comment create new permissions for integrations test API
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'integrationtests' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:integrationtests' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'integrationtests:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:integrationtests:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'integrationtests:run' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:integrationtests:run' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["integrationtests", "integrationtests:view","integrationtests:run", "keyentity:integrationtests", "keyentity:integrationtests:view","keyentity:integrationtests:run"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'integrationtests' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:integrationtests' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'integrationtests:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:integrationtests:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'integrationtests:run' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:integrationtests:run' WHERE id = 1;
--rollback RESET search_path;


--changeset nataliia.pliashko:2020-09-29-SWS-21106-add-serverUrl-to-sisal-merchant-schema
--comment add serverUrl parameter to sisal merchant schema
SET search_path = swmanagement;
INSERT INTO merchant_types(
	type, url, schema, created_at, updated_at)
VALUES('sisal', null, '{
  "serverUrl": {
    "type": "text",
    "title": "MERCHANT.PARAMETERS.serverUrl",
    "defaultValue": ""
  }
}'::JSONB, now(), now())
ON CONFLICT DO NOTHING;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DELETE FROM merchant_types WHERE type = 'sisal';
--rollback RESET search_path;

--changeset stepanov.aleksey:2020-09-22-SWS-21088-add-new-live-studio-type runInTransaction:false
--comment Add new "Live Studio" type
SET search_path = swmanagement;
ALTER TYPE enum_entities_type ADD VALUE IF NOT EXISTS 'liveStudio';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback DELETE FROM pg_enum where enumlabel = 'liveStudio' and enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_entities_type');
--rollback RESET search_path;

--changeset nataliia.pliashko:2020-09-23-SWS-20453-update-merchant-types-schema
--comment Update merchant-types schema per integrations
SET search_path = swmanagement;
UPDATE merchant_types SET schema = schema - ARRAY['isPromoInternal','supportTransfer', 'supportForceFinishAndRevert', 'forceFinishAndRevertInSWWalletOnly', 'supportPlayMoney'] WHERE type IN ('stars', 'stars_mock', 'dev_stars');
UPDATE merchant_types SET schema = schema || '{
    "isPromoInternal": {
      "type": "boolean",
      "title": "MERCHANT.PARAMETERS.isPromoInternal",
      "defaultValue": false
    },
    "supportTransfer": {
      "type": "boolean",
      "title": "MERCHANT.PARAMETERS.supportTransfer",
      "defaultValue": false
    },
    "supportForceFinishAndRevert": {
      "type": "boolean",
      "title": "MERCHANT.PARAMETERS.supportForceFinishAndRevert",
      "defaultValue": false
    },
    "forceFinishAndRevertInSWWalletOnly": {
      "type": "boolean",
      "title": "MERCHANT.PARAMETERS.forceFinishAndRevertInSWWalletOnly",
      "defaultValue": false
    },
    "supportPlayMoney": {
      "type": "boolean",
      "title": "MERCHANT.PARAMETERS.supportPlayMoney",
      "defaultValue": true
    }
}' WHERE type IN ('stars', 'stars_mock', 'dev_stars');

UPDATE merchant_types SET schema = schema - ARRAY['password', 'username', 'serverUrl', 'isPromoInternal', 'supportTransfer', 'isUnderAAMSRegulation', 'supportForceFinishAndRevert', 'forceFinishAndRevertInSWWalletOnly'] WHERE type IN ('seamless', 'seamless_ipm','seamless_mrch', 'pariplay');
UPDATE merchant_types SET schema = schema || '{
  "password": {
    "type": "password",
    "title": "MERCHANT.PARAMETERS.password",
    "defaultValue": ""
  },
  "username": {
    "type": "string",
    "title": "MERCHANT.PARAMETERS.username",
    "defaultValue": ""
  },
  "serverUrl": {
    "type": "text",
    "title": "MERCHANT.PARAMETERS.serverUrl",
    "defaultValue": ""
  },
  "isPromoInternal": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.isPromoInternal",
    "defaultValue": false
  },
  "supportTransfer": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.supportTransfer",
    "defaultValue": false
  },
  "isUnderAAMSRegulation": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.isUnderAAMSRegulation",
    "defaultValue": false
  },
  "supportForceFinishAndRevert": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.supportForceFinishAndRevert",
    "defaultValue": false
  },
  "forceFinishAndRevertInSWWalletOnly": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.forceFinishAndRevertInSWWalletOnly",
    "defaultValue": false
  }
}' WHERE type IN ('seamless', 'seamless_ipm','seamless_mrch', 'pariplay');

UPDATE merchant_types SET schema = schema - ARRAY['isPromoInternal', 'supportTransfer', 'isUnderAAMSRegulation', 'supportForceFinishAndRevert', 'forceFinishAndRevertInSWWalletOnly'] WHERE type IN ('everymatrix', 'everymatrix_mock', 'mrgreen', 'mrgreen_mock');
UPDATE merchant_types SET schema = schema || '{
  "isPromoInternal": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.isPromoInternal",
    "defaultValue": true
  },
  "supportTransfer": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.supportTransfer",
    "defaultValue": true
  },
  "isUnderAAMSRegulation": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.isUnderAAMSRegulation",
    "defaultValue": false
  },
  "supportForceFinishAndRevert": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.supportForceFinishAndRevert",
    "defaultValue": false
  },
  "forceFinishAndRevertInSWWalletOnly": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.forceFinishAndRevertInSWWalletOnly",
    "defaultValue": false
  }
}' WHERE type IN ('everymatrix', 'everymatrix_mock', 'mrgreen', 'mrgreen_mock');

UPDATE merchant_types SET schema = schema - ARRAY['isPromoInternal', 'supportTransfer', 'supportForceFinishAndRevert', 'forceFinishAndRevertInSWWalletOnly'] WHERE type IN ('gan', 'soft_swiss');
UPDATE merchant_types SET schema = schema || '{
  "isPromoInternal": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.isPromoInternal",
    "defaultValue": true
  },
  "supportTransfer": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.supportTransfer",
    "defaultValue": true
  },
  "supportForceFinishAndRevert": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.supportForceFinishAndRevert",
    "defaultValue": false
  },
  "forceFinishAndRevertInSWWalletOnly": {
    "type": "boolean",
    "title": "MERCHANT.PARAMETERS.forceFinishAndRevertInSWWalletOnly",
    "defaultValue": false
  }
}' WHERE type IN ('gan', 'soft_swiss');
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE merchant_types SET schema = schema - ARRAY['isPromoInternal','supportTransfer', 'supportForceFinishAndRevert', 'forceFinishAndRevertInSWWalletOnly', 'supportPlayMoney'] WHERE type IN ('stars', 'stars_mock', 'dev_stars');
--rollback UPDATE merchant_types SET schema = schema - ARRAY['username', 'isPromoInternal', 'supportTransfer', 'isUnderAAMSRegulation', 'supportForceFinishAndRevert', 'forceFinishAndRevertInSWWalletOnly'] WHERE type IN ('seamless', 'seamless_ipm','seamless_mrch');
--rollback UPDATE merchant_types SET schema = schema - ARRAY['isPromoInternal', 'supportTransfer', 'isUnderAAMSRegulation', 'supportForceFinishAndRevert', 'forceFinishAndRevertInSWWalletOnly'] WHERE type IN ('pariplay');
--rollback UPDATE merchant_types SET schema = schema - ARRAY['isPromoInternal', 'supportTransfer', 'isUnderAAMSRegulation', 'supportForceFinishAndRevert', 'forceFinishAndRevertInSWWalletOnly'] WHERE type IN ('everymatrix', 'everymatrix_mock', 'mrgreen', 'mrgreen_mock');
--rollback UPDATE merchant_types SET schema = schema - ARRAY['isPromoInternal', 'supportTransfer', 'supportForceFinishAndRevert', 'forceFinishAndRevertInSWWalletOnly'] WHERE type IN ('gan', 'soft_swiss');
--rollback RESET search_path;


--changeset aleksey.stepanov:2020-10-01-SWS-20794-table-manager
--comment create new permissions and new tables for table manager API
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'physicaltable' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:physicaltable' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'physicaltable:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:physicaltable:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'physicaltable:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:physicaltable:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'physicaltable:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:physicaltable:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'physicaltable:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:physicaltable:delete' WHERE id = 1;

UPDATE roles SET permissions = permissions - 'zone' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:zone' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'zone:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:zone:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'zone:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:zone:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'zone:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:zone:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'zone:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:zone:delete' WHERE id = 1;

UPDATE roles SET permissions = permissions - 'keyentity:tag' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:tag:view' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:tag:edit' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:tag:create' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:tag:delete' WHERE id = 1;
UPDATE roles SET permissions = permissions ||
'["zone","keyentity:zone","zone:view","keyentity:zone:view","zone:delete","keyentity:zone:delete","zone:create","keyentity:zone:create","zone:edit","keyentity:zone:edit","keyentity:tag", "keyentity:tag:view","keyentity:tag:edit","keyentity:tag:create","keyentity:tag:delete", "physicaltable", "physicaltable:view","physicaltable:view","keyentity:physicaltable:view", "physicaltable:edit", "keyentity:physicaltable:edit", "physicaltable:create","keyentity:physicaltable:create","physicaltable:delete","keyentity:physicaltable:delete"]'::jsonb WHERE id = 1;

CREATE TABLE IF NOT EXISTS pht_tags (
    id SERIAL PRIMARY KEY,
    title varchar(255) NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE pht_tags IS 'Tags for physical table';
COMMENT ON COLUMN pht_tags.id IS 'Id of tag from tags table';
COMMENT ON COLUMN pht_tags.title IS 'Tag title';
COMMENT ON COLUMN pht_tags.created_at IS 'Created At';
COMMENT ON COLUMN pht_tags.updated_at IS 'Updated At';

CREATE TABLE IF NOT EXISTS pht_zones (
    id SERIAL PRIMARY KEY,
    entity_id int4 NOT NULL,
    title varchar(255) NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW(),
    CONSTRAINT pht_zones_entity_id_fkey FOREIGN KEY (entity_id)
        REFERENCES entities (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE
);
COMMENT ON TABLE pht_zones IS 'Zones for physical table';
COMMENT ON COLUMN pht_zones.id IS 'Id of zone from zones table';
COMMENT ON COLUMN pht_zones.title IS 'Zone title';
COMMENT ON COLUMN pht_zones.created_at IS 'Created At';
COMMENT ON COLUMN pht_zones.updated_at IS 'Updated At';

CREATE TABLE IF NOT EXISTS pht_physical_tables (
    id SERIAL PRIMARY KEY,
    entity_id int4 NOT NULL,
    game_type varchar(255) NOT NULL,
    title varchar(255) NOT NULL,
    status varchar(255) NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW(),
    bet_time int4,
    delay int4,
    language varchar(255) NOT NULL,
    is_restrict_table bool DEFAULT false,
    min_balance int4,
    is_autowheel bool DEFAULT false,
    max_time int4,
    spin_now int4,
    zone_id int4,
    settings jsonb,
    video_settings jsonb,
    CONSTRAINT pht_physical_tables_entity_id_fkey FOREIGN KEY (entity_id)
        REFERENCES entities (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE,
    CONSTRAINT pht_physical_tables_zone_id_fkey FOREIGN KEY (zone_id)
        REFERENCES pht_zones (id)
        ON UPDATE CASCADE
        ON DELETE SET NULL
);
COMMENT ON TABLE pht_physical_tables IS 'Physical tables';
COMMENT ON COLUMN pht_physical_tables.id IS 'Id of physical table';
COMMENT ON COLUMN pht_physical_tables.entity_id IS 'Entity ID';
COMMENT ON COLUMN pht_physical_tables.game_type IS 'Game Type';
COMMENT ON COLUMN pht_physical_tables.title IS 'Physical table title';
COMMENT ON COLUMN pht_physical_tables.status IS 'Status';
COMMENT ON COLUMN pht_physical_tables.created_at IS 'Created At';
COMMENT ON COLUMN pht_physical_tables.updated_at IS 'Updated At';
COMMENT ON COLUMN pht_physical_tables.bet_time IS 'Bet Time';
COMMENT ON COLUMN pht_physical_tables.delay IS 'Delay';
COMMENT ON COLUMN pht_physical_tables.language IS 'Language';
COMMENT ON COLUMN pht_physical_tables.is_restrict_table IS 'Is Restrict Table';
COMMENT ON COLUMN pht_physical_tables.min_balance IS 'Min Balance';
COMMENT ON COLUMN pht_physical_tables.is_autowheel IS 'Is Autowheel';
COMMENT ON COLUMN pht_physical_tables.max_time IS 'Max Time';
COMMENT ON COLUMN pht_physical_tables.spin_now IS 'Spin Now';
COMMENT ON COLUMN pht_physical_tables.zone_id IS 'Zone Id';
COMMENT ON COLUMN pht_physical_tables.settings IS 'Settings';
COMMENT ON COLUMN pht_physical_tables.video_settings IS 'Video Settings';

CREATE TABLE IF NOT EXISTS pht_physical_table_tags (
    physical_table_id int8 NOT NULL,
    tag_id int8 NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    PRIMARY KEY (physical_table_id, tag_id),
    CONSTRAINT pht_physical_table_tags_physical_table_id_fkey FOREIGN KEY (physical_table_id)
        REFERENCES pht_physical_tables (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE,
    CONSTRAINT pht_physical_table_tags_tag_id_fkey FOREIGN KEY (tag_id)
        REFERENCES pht_tags (id)
        ON UPDATE NO ACTION
        ON DELETE CASCADE
);
COMMENT ON TABLE pht_physical_table_tags IS 'Physical tables tags';
COMMENT ON COLUMN pht_physical_table_tags.physical_table_id IS 'Physical Table ID';
COMMENT ON COLUMN pht_physical_table_tags.tag_id IS 'Tag ID';
COMMENT ON COLUMN pht_physical_table_tags.created_at IS 'Created At';

ALTER TABLE games ADD COLUMN physical_table_id INTEGER;
COMMENT ON COLUMN games.physical_table_id IS 'Physical table id';

ALTER TABLE games
    ADD CONSTRAINT games_physical_table_id_fkey
        FOREIGN KEY (physical_table_id) REFERENCES pht_physical_tables(id) ON UPDATE CASCADE ON DELETE SET NULL;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'integrationtests' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'physicaltable' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:physicaltable' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'physicaltable:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:physicaltable:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'physicaltable:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:physicaltable:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'physicaltable:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:physicaltable:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'physicaltable:delete' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:physicaltable:delete' WHERE id = 1;

--rollback UPDATE roles SET permissions = permissions - 'zone' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:zone' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'zone:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:zone:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'zone:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:zone:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'zone:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:zone:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'zone:delete' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:zone:delete' WHERE id = 1;

--rollback UPDATE roles SET permissions = permissions - 'keyentity:tag' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:tag:view' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:tag:edit' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:tag:create' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:tag:delete' WHERE id = 1;

--rollback ALTER TABLE games DROP COLUMN physical_table_id;
--rollback DROP TABLE IF EXISTS pht_physical_table_tags;
--rollback DROP TABLE IF EXISTS pht_tags;
--rollback DROP TABLE IF EXISTS pht_physical_tables;
--rollback DROP TABLE IF EXISTS pht_zones;
--rollback RESET search_path;

--changeset evgeniy.gandziuk:2020-10-08-SWS-21999-is_local-flag
--comment Mark jackpots that are belong to one operator only
SET search_path = swjackpot;
ALTER TABLE jp_instance ADD COLUMN is_local BOOLEAN NOT NULL DEFAULT false;
COMMENT ON COLUMN jp_instance.is_local IS 'Indicate if jackpot instance belongs to only one operator';
RESET search_path;
--rollback SET search_path = swjackpot;
--rollback ALTER TABLE jp_instance DROP COLUMN is_local;
--rollback RESET search_path;


--changeset sergey.malkov:2020-10-09-SWB365-252-Trx-unloader:-add-additional-fields-round_bets-round_wins-ggrl_calculation
--comment Add additional fields to wallet_win_bet table for other way of ggr report calculation
SET search_path = swmanagement;
CREATE TYPE enum_wallet_win_bet_ggr_calculation AS ENUM ('round', 'wallet');
ALTER TABLE wallet_win_bet ADD COLUMN ggr_calculation enum_wallet_win_bet_ggr_calculation;
COMMENT ON COLUMN wallet_win_bet.ggr_calculation IS 'Type of ggr calculation';
ALTER TABLE wallet_win_bet ADD COLUMN round_wins numeric;
COMMENT ON COLUMN wallet_win_bet.round_wins IS 'Total win of round (excluding jackpot wins)';
ALTER TABLE wallet_win_bet ADD COLUMN round_bets numeric;
COMMENT ON COLUMN wallet_win_bet.round_bets IS 'Total bet of round';
RESET search_path;
SET search_path = swmanagement_archive;
   ALTER TABLE wallet_win_bet ADD COLUMN ggr_calculation swmanagement.enum_wallet_win_bet_ggr_calculation;
   ALTER TABLE wallet_win_bet ADD COLUMN round_wins numeric;
   ALTER TABLE wallet_win_bet ADD COLUMN round_bets numeric;
RESET search_path;
SET search_path = swmanagement_archive_ro;
   ALTER TABLE wallet_win_bet ADD COLUMN ggr_calculation swmanagement.enum_wallet_win_bet_ggr_calculation;
   ALTER TABLE wallet_win_bet ADD COLUMN round_wins numeric;
   ALTER TABLE wallet_win_bet ADD COLUMN round_bets numeric;
RESET search_path;
--rollback SET search_path = swmanagement_archive;
--rollback    ALTER TABLE wallet_win_bet DROP COLUMN ggr_calculation;
--rollback    ALTER TABLE wallet_win_bet DROP COLUMN round_wins;
--rollback    ALTER TABLE wallet_win_bet DROP COLUMN round_bets;
--rollback RESET search_path;
--rollback SET search_path = swmanagement_archive_ro;
--rollback    ALTER TABLE wallet_win_bet DROP COLUMN ggr_calculation;
--rollback    ALTER TABLE wallet_win_bet DROP COLUMN round_wins;
--rollback    ALTER TABLE wallet_win_bet DROP COLUMN round_bets;
--rollback RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE wallet_win_bet DROP COLUMN ggr_calculation;
--rollback ALTER TABLE wallet_win_bet DROP COLUMN round_wins;
--rollback ALTER TABLE wallet_win_bet DROP COLUMN round_bets;
--rollback DROP TYPE IF EXISTS enum_wallet_win_bet_ggr_calculation;
--rollback RESET search_path;

--changeset valdis.akmens:2020-10-12-SWB365-254-add-ggr-calculation-rounds-wallet endDelimiter:# stripComments:false
--comment Change aggregation fields for win,bet calculation, based on wallet_win_bet.ggr_calculation
SET search_path TO swmanagement;

ALTER FUNCTION fnc_bo_aggr_refresh_jobs (TIMESTAMP, VARCHAR) RENAME TO fnc_bo_aggr_refresh_jobs_before_4_46_0;

CREATE OR REPLACE FUNCTION fnc_bo_aggr_refresh_jobs(p_force_end_hour timestamp without time zone, p_work_mem character varying DEFAULT NULL::character varying)
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
*******************************************************************************

   Object Name:   fnc_bo_aggr_refresh_jobs
   Purpose    :   To perform B/O aggregation jobs
   History    :
      1.0.0
         Date    : Feb 03, 2017
         Authors : Timur Luchkin
         Notes   : Release (BYDEVO-260)

      1.0.1
         Date    : Mar 07, 2017
         Authors : Timur Luchkin
         Notes   : Add more details required for watchdog to monitor jobs
                   (BYSWBO-73)

      1.0.2
         Date    : Jun 09, 2017
         Authors : Timur Luchkin
         Notes   : Tables renamed to follow snake style
                   (BYDEVO-578)
                   Add logs history logging

      1.0.3
         Date    : Jul 17, 2017
         Authors : Andrey Shmigiro
         Notes   : Added table "bo_aggr_win_bets" for aggregation data from "wallet_win_bet"
                   (BYDEVO-513)

      1.0.4
         Date    : Jul 24, 2017
         Authors : Andrey Shmigiro
         Notes   : Added table "bo_aggr_player_rounds" for aggregation data about played
                     rounds from "bo_aggr_rounds";
                   Added column "exchange_rate" to table "bo_aggr_win_bets";
                   Changed calculation method for column "played_games_qty" in table "bo_aggr_win_bets";
                   (SWS-1561)

      1.0.5
         Date    : Aug 15, 2017
         Authors : Andrey Shmigiro
         Notes   : Added column "start_balance", "end_balance", "device_code" to table
                   "bo_aggr_player_rounds";
                   (SWS-1651 / SWS-1720)

      1.1.0
         Date    : Sep 28, 2017
         Authors : Timur Luchkin
         Notes   : Fix issues with missed spins (SWS-1873)
                   More logging details

      1.1.1
         Date    : Oct 19, 2017
         Authors : Andrey Shmigiro
         Notes   : Exclude test wallet operations from bo_aggr_win_bets (SWS-1789)
                   Exclude jackpot wins from bo_aggr_win_bets (SWS-1863)
                   Exclude free bets from bo_aggr_win_bets (SWS-1943)

      1.1.2
         Date    : Oct 19, 2017
         Authors : Andrey Shmigiro
         Notes   : Added columns for jackpot's and free_bet's wins to bo_agr_win_bets's tbl (SWS-2805)
                   Exclude NULLs values of start&end balances for bo_aggr_rounds

      1.1.3
            Date    : Jan 12, 2018
            Authors : Valdis Akmens
            Notes   : Change aggregation of wallet_win_bet from payment_date to inserted_at (BYDEVO-1280)

      1.1.4
            Date    : Mar 08, 2018
            Authors : Valdis Akmens
            Notes   : fnc_bo_aggr_refresh_jobs takes too much time after the partitioning has been installed (SWDB-24)
                Added new parameter p_work_mem to set larger work_mem for function to get rid off "Sort Method: external merge  Disk"
                Changed bo_aggr_win_bets aggregation to replace "LEFT JOIN" with sub-queries (because of partitioning, wrong estimations lead to non-optimal JOIN strategies)
                p_work_mem: NULL, 64MB, 128MB, 256MB ..

      1.1.5
            Date    : Jun 11, 2018
            Authors : Valdis Akmens
            Notes   : Change aggregation source for tables "bo_aggr_brand_currency" and "bo_aggr_player_rounds"
                         from "bo_aggr_rounds" to "rounds_history"(SWDB-49)

      1.1.6
            Date    : Sep 17, 2018
            Authors : Valdis Akmens
            Notes   : Remove "bo_aggr_rounds" from aggregation completely (SWDB-69)

      1.1.7
          Date    : Nov 06, 2018
          Authors : Timur Luchkin
          Notes   : Change aggregation logic to allow "bo_aggr_player_rounds" and "bo_aggr_win_bets" tables partitioning (SWDB-44)

      1.1.8
          Date    : Mar 22, 2019
          Authors : Timur Luchkin
          Notes   : Change aggregation source table from rounds_history to rounds_finished

      1.1.9
         Date    : Apr 30, 2019
         Authors : Andrey Shmigiro
         Notes   : Added column "debit", "credit" to table "bo_aggr_win_bets" (DEVOPS-5075);

      1.2.0
         Date    : Jul 30, 2019
         Authors : Valdis Akmens
         Notes   : Change aggregation of wins for table "bo_aggr_win_bets" and "bo_aggr_win_bets_by_brand" because of new wallet_transactions_types (SWS-11908);

      1.2.1
         Date    : Aug 22, 2019
         Authors : Valdis Akmens
         Notes   : Subtract "bet" for table "bo_aggr_win_bets" and "bo_aggr_win_bets_by_brand" aggregation when bet_rollback = TRUE  (SWS-12584);

      1.2.2
         Date    : Apr 15, 2020
         Authors : Valdis Akmens
         Notes   : Replace CTE(DELETE+INSERT) to temporary tables (SWDB-132);

      1.2.3
         Date    : Oct 12, 2020
         Authors : Valdis Akmens
         Notes   : Change aggregation fields for win,bet calculation, based on wallet_win_bet.ggr_calculation (SWB365-254);

  Sample run:
      SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL);
      SELECT * FROM fnc_bo_aggr_refresh_jobs ('2016-11-28 23:00:00');

	    SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL, NULL);
      SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL, '64MB');
	    SELECT * FROM fnc_bo_aggr_refresh_jobs ('2017-09-25 06:00:00', '64MB');

*******************************************************************************
*/
DECLARE
   v_last_inserted_at      TIMESTAMP;
   v_new_inserted_at       TIMESTAMP;
   v_time_back_msec        INTEGER;
   v_counter               BIGINT;
   v_job_start_time        TIMESTAMP;
   v_force_end_time        TIMESTAMP;
   v_huge_interval_win_bet INTERVAL := '1 day'::INTERVAL;
   v_huge_interval_rounds  INTERVAL := '12 hours'::INTERVAL;
BEGIN

     log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job started'; RETURN NEXT;

     /* Check if MDB */
     IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
     END IF;

     /* Change work_mem parameter */
     IF p_work_mem IS NOT NULL THEN
        EXECUTE 'SET work_mem TO '''||p_work_mem||'''' ;
     END IF;

     /* To prevent misses of the lazy offloaded data */
     SELECT split_part(sett,'=',2)::INTEGER
     INTO   v_time_back_msec
     FROM   (SELECT unnest(useconfig) AS sett
             FROM   pg_user
            WHERE  usename = 'redis_game_offloader'
            ) t1
     WHERE sett LIKE 'statement_timeout=%';

     IF NOT FOUND THEN
        v_time_back_msec := 60 * 1000;
     END IF;

     /* manyachello */
     v_time_back_msec := v_time_back_msec + 5000;

    /* ~~~ bo_aggr_brand_currency ~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   v_job_start_time := clock_timestamp();
   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_brand_currency" started'; RETURN NEXT;

   SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_brand_currency' AND conf_key = 'lock_record' FOR UPDATE;

   IF NOT FOUND THEN
      RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_brand_currency/lock_record" pair';
   END IF;

   v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                             ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                             );

    /* Automatically prevent too huge intervals */
     IF (v_force_end_time - v_last_inserted_at) > v_huge_interval_rounds THEN
        v_force_end_time := v_last_inserted_at + v_huge_interval_rounds;
     END IF;

    WITH fresh_data AS
    (
        SELECT
        Date_Trunc('HOUR', h.finished_at)       AS date_hour
        ,h.brand_id                             AS brand_id
        ,h.currency                             AS currency_code
        ,SUM(h.total_bet)                       AS bet
        ,SUM(h.total_win)                       AS win
        ,SUM(h.total_bet)  - SUM(h.total_win)   AS revenue
        ,COUNT(h.id)::BIGINT                    AS finished_rounds
        ,MAX(COALESCE(inserted_at, started_at)) AS max_inserted_at
    FROM  rounds_finished AS h
    WHERE NOT h.test
      AND COALESCE(inserted_at, started_at) >= v_last_inserted_at
      AND COALESCE(inserted_at, started_at) <  v_force_end_time
    GROUP BY Date_Trunc('HOUR', h.finished_at)
    ,h.brand_id
    ,h.currency
    ),
    cte_upsert AS
    (
        INSERT INTO bo_aggr_brand_currency (date_hour, date_day, brand_id, currency_code, bet, win, revenue, finished_rounds)
        SELECT
                date_hour
                ,Date_Trunc('DAY', date_hour)::DATE     AS date_day
                ,brand_id
                ,currency_code
                ,bet
                ,win
                ,revenue
                ,finished_rounds
        FROM fresh_data
        ON CONFLICT (date_hour, brand_id, currency_code) DO
        UPDATE SET
                bet             = bo_aggr_brand_currency.bet            + EXCLUDED.bet,
                win             = bo_aggr_brand_currency.win            + EXCLUDED.win,
                revenue         = bo_aggr_brand_currency.revenue        + EXCLUDED.revenue,
                finished_rounds = bo_aggr_brand_currency.finished_rounds + EXCLUDED.finished_rounds
    )
   SELECT  MAX(max_inserted_at), COUNT(*)
   FROM   fresh_data
   INTO    v_new_inserted_at, v_counter;

   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_brand_currency" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

   UPDATE bo_aggr_config SET
           conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
   WHERE  aggr_job_name = 'bo_aggr_brand_currency'
   AND  conf_key = 'lock_record';

   -- Log
   INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
      VALUES ('bo_aggr_brand_currency', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time), v_force_end_time);

   /* ~~~ bo_aggr_player_rounds ~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   v_job_start_time := clock_timestamp();
   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_player_rounds" started'; RETURN NEXT;

   SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_player_rounds' AND conf_key = 'lock_record' FOR UPDATE;

   IF NOT FOUND THEN
      RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_player_rounds/lock_record" pair';
   END IF;

   v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                             ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                             );

    /* Automatically prevent too huge intervals */
    IF (v_force_end_time - v_last_inserted_at) > v_huge_interval_rounds THEN
    v_force_end_time := v_last_inserted_at + v_huge_interval_rounds;
    END IF;

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_rounds_finished_fresh AS
        SELECT Date_Trunc('HOUR', h.finished_at)          AS date_hour
            ,Date_Trunc('HOUR', h.finished_at)::date    AS date_day
            ,h.brand_id                                 AS brand_id
            ,h.player_code
            ,h.game_code
            ,h.currency                                 AS currency_code
            ,MAX(h.device_id)                           AS device_code    /* This is wrong! We should add this field to the primary key!!! */
            ,COUNT(h.id)::BIGINT                        AS rounds_qty
            ,SUM(h.total_events)::INTEGER               AS events_qty
            ,SUM(h.total_bet)                           AS total_bet
            ,SUM(h.total_win)                           AS total_win
            ,SUM(h.total_bet)  - SUM(h.total_win)       AS total_revenue
            ,null::numeric                              AS start_balance
            ,null::numeric                              AS end_balance
            ,min(h.started_at)                          AS first_activity
            ,MAX(h.finished_at)                         AS last_activity
            ,MAX(COALESCE(inserted_at, started_at))     AS max_inserted_at
        FROM rounds_finished AS h
        WHERE
            NOT  h.test
            AND  COALESCE(inserted_at, started_at) >= v_last_inserted_at
            AND  COALESCE(inserted_at, started_at) <  v_force_end_time
        GROUP BY h.player_code, h.brand_id, h.currency, h.game_code, date_trunc('HOUR', h.finished_at);

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_rounds_finished_deleted AS SELECT * FROM bo_aggr_player_rounds LIMIT 0;
    WITH
    cte_delete_existing_data AS (
        DELETE FROM bo_aggr_player_rounds d
        USING tmp_bo_aggr_refresh_jobs_rounds_finished_fresh f
        WHERE  d.date_hour      = f.date_hour
            AND  d.brand_id       = f.brand_id
            AND  d.game_code      = f.game_code
            AND  d.currency_code  = f.currency_code
            AND  d.player_code    = f.player_code
        RETURNING d.*
    )
    INSERT INTO tmp_bo_aggr_refresh_jobs_rounds_finished_deleted
    SELECT * FROM cte_delete_existing_data;

    INSERT INTO bo_aggr_player_rounds (date_hour, date_day, brand_id, player_code, game_code, device_code, currency_code, exchange_rate, rounds_qty, events_qty, total_bet, total_win, total_revenue, start_balance, end_balance, first_activity, last_activity)
        SELECT fd.date_hour,
            fd.date_hour::DATE,
            fd.brand_id,
            fd.player_code,
            fd.game_code,
            coalesce(fd.device_code, '') AS device_code,
            fd.currency_code,
            fd.exchange_rate,
            fd.rounds_qty,
            fd.events_qty,
            fd.total_bet,
            fd.total_win,
            fd.total_revenue,
            fd.start_balance,
            fd.end_balance,
            fd.first_activity,
            fd.last_activity
        FROM (
                SELECT date_hour
                    ,brand_id
                    ,player_code
                    ,game_code
                    ,currency_code
                    ,MAX(device_code)       AS device_code
                    ,Avg(exchange_rate)     AS exchange_rate
                    ,SUM(rounds_qty   )     AS rounds_qty
                    ,SUM(events_qty   )     AS events_qty
                    ,SUM(total_bet    )     AS total_bet
                    ,SUM(total_win    )     AS total_win
                    ,SUM(total_revenue)     AS total_revenue
                    ,(array_remove(array_agg(start_balance ORDER BY first_activity ASC), NULL))[1] AS start_balance
                    ,(array_remove(array_agg(end_balance ORDER BY last_activity DESC), NULL))[1] AS end_balance
                    ,Min(first_activity)    AS first_activity
                    ,MAX(last_activity)     AS last_activity
                FROM   (
                        SELECT date_hour
                                ,brand_id
                                ,player_code
                                ,game_code
                                ,device_code
                                ,currency_code
                                ,(SELECT cr2.rate
                                FROM   currency_rates cr2
                                WHERE  cr2.currency_code = t0.currency_code
                                    AND  cr2.rate_date <= t0.date_day
                                ORDER BY cr2.rate_date DESC LIMIT 1) AS exchange_rate
                                ,rounds_qty
                                ,events_qty
                                ,total_bet
                                ,total_win
                                ,total_revenue
                                ,start_balance
                                ,end_balance
                                ,first_activity
                                ,last_activity
                        FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_fresh t0
                        UNION ALL
                        SELECT date_hour
                                ,brand_id
                                ,player_code
                                ,game_code
                                ,NULL::VARCHAR AS device_code /* We can't use old value, because insert can fault due to current PK */
                                ,currency_code
                                ,exchange_rate
                                ,rounds_qty
                                ,events_qty
                                ,total_bet
                                ,total_win
                                ,total_revenue
                                ,start_balance
                                ,end_balance
                                ,first_activity
                                ,last_activity
                        FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_deleted
                        ) t
                GROUP BY date_hour, brand_id, game_code, currency_code, player_code
            ) fd;

    SELECT  MAX(max_inserted_at), COUNT(*)
    FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_fresh
    INTO    v_new_inserted_at, v_counter;

    DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_rounds_finished_fresh;
    DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_rounds_finished_deleted;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_player_rounds" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

        UPDATE bo_aggr_config SET
                conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
        WHERE  aggr_job_name = 'bo_aggr_player_rounds'
            AND  conf_key = 'lock_record';

    -- Log
    INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
        VALUES ('bo_aggr_player_rounds', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time), v_force_end_time);

        /* ~~~ bo_aggr_win_bets ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        v_job_start_time := clock_timestamp();
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets" started'; RETURN NEXT;

        SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_win_bets' AND conf_key = 'lock_record' FOR UPDATE;

        IF NOT FOUND THEN
            RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_win_bets/lock_record" pair';
        END IF;

        v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                                ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                                );

        /* Automatically prevent too huge intervals */
        IF (v_force_end_time - v_last_inserted_at) > v_huge_interval_win_bet THEN
            v_force_end_time := v_last_inserted_at + v_huge_interval_win_bet;
        END IF;

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh AS
        SELECT date_trunc('HOUR', b.payment_date) AS payment_date_hour,
                b.brand_id,
                b.game_code,
                b.player_code,
                b.currency AS currency_code,
                COUNT(DISTINCT b.game_id) AS played_games_qty,
                SUM(case when b.transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN bet_rollback = FALSE THEN (CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) ELSE (-1)*(CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) END) end) AS total_bets,
                SUM(case when b.transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) end) AS total_wins,
                MAX(b.payment_date)::timestamp(0) as last_payment_ts,
                SUM(case when b.transaction_type = 'jackpot' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end) as total_jp_wins,
                SUM(case when b.transaction_type = 'free_bet' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end) as total_freebet_wins,
                MAX(COALESCE(inserted_at, payment_date)) AS max_inserted_at,
                SUM(debit) AS debit,
                SUM(credit) AS credit
        FROM  wallet_win_bet b
        WHERE COALESCE(inserted_at, payment_date) >= v_last_inserted_at
            AND COALESCE(inserted_at, payment_date) < v_force_end_time
            AND COALESCE(b.is_test, false) = false
                -- AND b.transaction_type is null
        GROUP BY b.player_code, b.brand_id, b.currency, b.game_code, date_trunc('HOUR', b.payment_date);

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted AS SELECT * FROM bo_aggr_win_bets LIMIT 0;
    WITH cte_delete_existing_data AS (
            DELETE FROM bo_aggr_win_bets d
            USING tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh f
            WHERE d.payment_date_hour = f.payment_date_hour
            AND d.brand_id          = f.brand_id
            AND d.game_code         = f.game_code
            AND d.currency_code     = f.currency_code
            AND d.player_code       = f.player_code
            RETURNING d.*
        )
    INSERT INTO tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted
    SELECT * FROM cte_delete_existing_data;

    INSERT INTO bo_aggr_win_bets (payment_date_hour, payment_date_day, brand_id, game_code, player_code,
            currency_code, played_games_qty, total_bets, total_wins, last_payment_ts,
            exchange_rate, total_jp_wins, total_freebet_wins, debit, credit)
    SELECT
        d.payment_date_hour,
        d.payment_date_hour::DATE AS payment_date_day,
        d.brand_id,
        d.game_code,
        d.player_code,
        d.currency_code,
        /* bo_aggr_player_rounds - contains only finished rounds. TLU: This value is incorrect and should be fixed. wallet_win_bet should also has finished flags */
        COALESCE( (SELECT pr.rounds_qty
                    FROM   bo_aggr_player_rounds pr
                    WHERE  d.payment_date_hour = pr.date_hour
                    AND  d.brand_id = pr.brand_id
                    AND  d.game_code = pr.game_code
                    AND  d.currency_code = pr.currency_code
                    AND  d.player_code = pr.player_code
                    LIMIT 1), 0) AS played_games_qty,
        d.total_bets,
        d.total_wins,
        d.last_payment_ts,
        (SELECT cr2.rate
        FROM   currency_rates cr2
        WHERE  cr2.currency_code = d.currency_code
            AND  cr2.rate_date <= d.payment_date_hour::DATE
        ORDER BY cr2.rate_date DESC LIMIT 1) AS exchange_rate,
        d.total_jp_wins,
        d.total_freebet_wins,
        d.debit,
        d.credit
        FROM (
                SELECT payment_date_hour
                    ,brand_id
                    ,game_code
                    ,player_code
                    ,currency_code
                    --,SUM(played_games_qty) AS played_games_qty
                    ,SUM(total_bets) AS total_bets
                    ,SUM(total_wins) AS total_wins
                    ,MAX(last_payment_ts) AS last_payment_ts
                    ,SUM(total_jp_wins     ) AS total_jp_wins
                    ,SUM(total_freebet_wins) AS total_freebet_wins
                    ,SUM(debit) AS debit
                    ,SUM(credit) AS credit
                FROM   (
                        SELECT payment_date_hour
                                ,brand_id
                                ,game_code
                                ,player_code
                                ,currency_code
                                --,played_games_qty
                                ,total_bets
                                ,total_wins
                                ,last_payment_ts
                                ,total_jp_wins
                                ,total_freebet_wins
                                ,debit
                                ,credit
                        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh
                        UNION ALL
                        SELECT payment_date_hour
                                ,brand_id
                                ,game_code
                                ,player_code
                                ,currency_code
                                --,played_games_qty
                                ,total_bets
                                ,total_wins
                                ,last_payment_ts
                                ,total_jp_wins
                                ,total_freebet_wins
                                ,debit
                                ,credit
                        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted
                        ) uni_on
                GROUP BY payment_date_hour, brand_id, game_code, currency_code, player_code
            ) d;

        SELECT MAX(max_inserted_at), COUNT(*)
        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh
        INTO   v_new_inserted_at, v_counter;

        DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh;
        DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted;

        -- GET DIAGNOSTICS v_counter = ROW_COUNT;
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

            UPDATE bo_aggr_config SET
                conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
            WHERE  aggr_job_name = 'bo_aggr_win_bets'
            AND  conf_key = 'lock_record';

        -- Log
        INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
            VALUES ('bo_aggr_win_bets', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, v_new_inserted_at, v_force_end_time);


        /* ~~~ bo_aggr_win_bets_by_brand ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        v_job_start_time := clock_timestamp();
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets_by_brand" started'; RETURN NEXT;

        SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_win_bets_by_brand' AND conf_key = 'lock_record' FOR UPDATE;

        IF NOT FOUND THEN
            RAISE EXCEPTION 'Table "bo_aggr_win_bets_by_brand" has no valid record for "bo_aggr_win_bets_by_brand/lock_record" pair';
        END IF;

        /* To limit huge load intervals */
        v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                                ,current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)
                                ,v_last_inserted_at + v_huge_interval_win_bet
                                );


        WITH fresh_data as
        ( SELECT b.brand_id,
                b.currency as currency_code,
                SUM(case when transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN bet_rollback = FALSE THEN (CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) ELSE (-1)*(CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) END) end) as bet,
                SUM(case when transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) end) as win,
                SUM(case when transaction_type = 'jackpot' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end) as jackpot_win,
                SUM(case when transaction_type = 'free_bet' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end) as free_bet_win,
                COUNT(*) as events_count,
                MAX(b.payment_date) as last_payment_ts,
                MAX(COALESCE(inserted_at, payment_date)) AS max_inserted_at
            FROM wallet_win_bet b
            WHERE COALESCE(inserted_at, payment_date) > v_last_inserted_at
                AND COALESCE(inserted_at, payment_date) < v_force_end_time
                AND coalesce(b.is_test, false) = false
            GROUP BY b.brand_id, b.currency
        ),
        upsert_aggr AS
        ( INSERT INTO bo_aggr_win_bets_by_brand (brand_id, currency_code, bet, win, revenue, jackpot_win, free_bet_win)
                SELECT brand_id, currency_code, bet, win, (bet - win) as revenue, jackpot_win, free_bet_win
                FROM fresh_data
        ON CONFLICT ON CONSTRAINT bo_aggr_win_bets_by_brand_pkey DO
                UPDATE SET bet = bo_aggr_win_bets_by_brand.bet + EXCLUDED.bet,
                        win = bo_aggr_win_bets_by_brand.win + EXCLUDED.win,
                        revenue = bo_aggr_win_bets_by_brand.revenue + EXCLUDED.revenue,
                        jackpot_win = bo_aggr_win_bets_by_brand.jackpot_win + EXCLUDED.jackpot_win,
                        free_bet_win = bo_aggr_win_bets_by_brand.free_bet_win + EXCLUDED.free_bet_win
        )
        SELECT MAX(max_inserted_at), SUM(events_count)
        FROM fresh_data
        INTO v_new_inserted_at, v_counter;


        -- GET DIAGNOSTICS v_counter = ROW_COUNT;
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets_by_brand" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

            UPDATE bo_aggr_config SET
                conf_value = coalesce(v_new_inserted_at, v_force_end_time - '1 msec'::INTERVAL)
            WHERE  aggr_job_name = 'bo_aggr_win_bets_by_brand'
            AND  conf_key = 'lock_record';

        -- Log
        INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
            VALUES ('bo_aggr_win_bets_by_brand', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, v_new_inserted_at, v_force_end_time);

        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job finished'; RETURN NEXT;

        /* ~~~ Maintenance ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        -- Clear old logs
        DELETE FROM bo_aggr_history WHERE started_at < (current_date - Interval '1 MONTH');

        -- Reset config parameters
        reset work_mem;

        RETURN;
    END;
$function$
;


RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_aggr_refresh_jobs(TIMESTAMP, VARCHAR);
--rollback ALTER FUNCTION fnc_bo_aggr_refresh_jobs_before_4_46_0 (TIMESTAMP, VARCHAR) RENAME TO fnc_bo_aggr_refresh_jobs;
--rollback RESET search_path;

--changeset aleh.rudzko:SWS-21777-jackpot-win-capping;
--comment Add new flag - supportsWinCap;

SET search_path TO swjackpot;
ALTER TABLE jp_type ADD COLUMN supports_win_cap BOOLEAN DEFAULT FALSE;
RESET search_path;

--SET search_path TO swjackpot;
--rollback ALTER TABLE jp_type DROP COLUMN supports_win_cap;
--RESET search_path;

--changeset valdis.akmens:2020-10-12-SWS-22290-ignore-bonus-transactions-2 endDelimiter:# stripComments:false
--comment Ignore transactions in wallet_win_bet where game_code is null
SET search_path TO swmanagement;

ALTER FUNCTION fnc_bo_aggr_refresh_jobs (TIMESTAMP, VARCHAR) RENAME TO fnc_bo_aggr_refresh_jobs_before_4_46_0_2;

CREATE OR REPLACE FUNCTION fnc_bo_aggr_refresh_jobs(p_force_end_hour timestamp without time zone, p_work_mem character varying DEFAULT NULL::character varying)
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
*******************************************************************************

   Object Name:   fnc_bo_aggr_refresh_jobs
   Purpose    :   To perform B/O aggregation jobs
   History    :
      1.0.0
         Date    : Feb 03, 2017
         Authors : Timur Luchkin
         Notes   : Release (BYDEVO-260)

      1.0.1
         Date    : Mar 07, 2017
         Authors : Timur Luchkin
         Notes   : Add more details required for watchdog to monitor jobs
                   (BYSWBO-73)

      1.0.2
         Date    : Jun 09, 2017
         Authors : Timur Luchkin
         Notes   : Tables renamed to follow snake style
                   (BYDEVO-578)
                   Add logs history logging

      1.0.3
         Date    : Jul 17, 2017
         Authors : Andrey Shmigiro
         Notes   : Added table "bo_aggr_win_bets" for aggregation data from "wallet_win_bet"
                   (BYDEVO-513)

      1.0.4
         Date    : Jul 24, 2017
         Authors : Andrey Shmigiro
         Notes   : Added table "bo_aggr_player_rounds" for aggregation data about played
                     rounds from "bo_aggr_rounds";
                   Added column "exchange_rate" to table "bo_aggr_win_bets";
                   Changed calculation method for column "played_games_qty" in table "bo_aggr_win_bets";
                   (SWS-1561)

      1.0.5
         Date    : Aug 15, 2017
         Authors : Andrey Shmigiro
         Notes   : Added column "start_balance", "end_balance", "device_code" to table
                   "bo_aggr_player_rounds";
                   (SWS-1651 / SWS-1720)

      1.1.0
         Date    : Sep 28, 2017
         Authors : Timur Luchkin
         Notes   : Fix issues with missed spins (SWS-1873)
                   More logging details

      1.1.1
         Date    : Oct 19, 2017
         Authors : Andrey Shmigiro
         Notes   : Exclude test wallet operations from bo_aggr_win_bets (SWS-1789)
                   Exclude jackpot wins from bo_aggr_win_bets (SWS-1863)
                   Exclude free bets from bo_aggr_win_bets (SWS-1943)

      1.1.2
         Date    : Oct 19, 2017
         Authors : Andrey Shmigiro
         Notes   : Added columns for jackpot's and free_bet's wins to bo_agr_win_bets's tbl (SWS-2805)
                   Exclude NULLs values of start&end balances for bo_aggr_rounds

      1.1.3
            Date    : Jan 12, 2018
            Authors : Valdis Akmens
            Notes   : Change aggregation of wallet_win_bet from payment_date to inserted_at (BYDEVO-1280)

      1.1.4
            Date    : Mar 08, 2018
            Authors : Valdis Akmens
            Notes   : fnc_bo_aggr_refresh_jobs takes too much time after the partitioning has been installed (SWDB-24)
                Added new parameter p_work_mem to set larger work_mem for function to get rid off "Sort Method: external merge  Disk"
                Changed bo_aggr_win_bets aggregation to replace "LEFT JOIN" with sub-queries (because of partitioning, wrong estimations lead to non-optimal JOIN strategies)
                p_work_mem: NULL, 64MB, 128MB, 256MB ..

      1.1.5
            Date    : Jun 11, 2018
            Authors : Valdis Akmens
            Notes   : Change aggregation source for tables "bo_aggr_brand_currency" and "bo_aggr_player_rounds"
                         from "bo_aggr_rounds" to "rounds_history"(SWDB-49)

      1.1.6
            Date    : Sep 17, 2018
            Authors : Valdis Akmens
            Notes   : Remove "bo_aggr_rounds" from aggregation completely (SWDB-69)

      1.1.7
          Date    : Nov 06, 2018
          Authors : Timur Luchkin
          Notes   : Change aggregation logic to allow "bo_aggr_player_rounds" and "bo_aggr_win_bets" tables partitioning (SWDB-44)

      1.1.8
          Date    : Mar 22, 2019
          Authors : Timur Luchkin
          Notes   : Change aggregation source table from rounds_history to rounds_finished

      1.1.9
         Date    : Apr 30, 2019
         Authors : Andrey Shmigiro
         Notes   : Added column "debit", "credit" to table "bo_aggr_win_bets" (DEVOPS-5075);

      1.2.0
         Date    : Jul 30, 2019
         Authors : Valdis Akmens
         Notes   : Change aggregation of wins for table "bo_aggr_win_bets" and "bo_aggr_win_bets_by_brand" because of new wallet_transactions_types (SWS-11908);

      1.2.1
         Date    : Aug 22, 2019
         Authors : Valdis Akmens
         Notes   : Subtract "bet" for table "bo_aggr_win_bets" and "bo_aggr_win_bets_by_brand" aggregation when bet_rollback = TRUE  (SWS-12584);

      1.2.2
         Date    : Apr 15, 2020
         Authors : Valdis Akmens
         Notes   : Replace CTE(DELETE+INSERT) to temporary tables (SWDB-132);

      1.2.2.1
         Date    : Oct 14, 2020
         Authors : Valdis Akmens
         Notes   : Ignore transactions in wallet_win_bet where game_code is null (SWS-22290);

      1.2.3
         Date    : Oct 12, 2020
         Authors : Valdis Akmens
         Notes   : Change aggregation fields for win,bet calculation, based on wallet_win_bet.ggr_calculation (SWB365-254);

  Sample run:
      SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL);
      SELECT * FROM fnc_bo_aggr_refresh_jobs ('2016-11-28 23:00:00');

	    SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL, NULL);
      SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL, '64MB');
	    SELECT * FROM fnc_bo_aggr_refresh_jobs ('2017-09-25 06:00:00', '64MB');

*******************************************************************************
*/
DECLARE
   v_last_inserted_at      TIMESTAMP;
   v_new_inserted_at       TIMESTAMP;
   v_time_back_msec        INTEGER;
   v_counter               BIGINT;
   v_job_start_time        TIMESTAMP;
   v_force_end_time        TIMESTAMP;
   v_huge_interval_win_bet INTERVAL := '1 day'::INTERVAL;
   v_huge_interval_rounds  INTERVAL := '12 hours'::INTERVAL;
BEGIN

     log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job started'; RETURN NEXT;

     /* Check if MDB */
     IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
     END IF;

     /* Change work_mem parameter */
     IF p_work_mem IS NOT NULL THEN
        EXECUTE 'SET work_mem TO '''||p_work_mem||'''' ;
     END IF;

     /* To prevent misses of the lazy offloaded data */
     SELECT split_part(sett,'=',2)::INTEGER
     INTO   v_time_back_msec
     FROM   (SELECT unnest(useconfig) AS sett
             FROM   pg_user
            WHERE  usename = 'redis_game_offloader'
            ) t1
     WHERE sett LIKE 'statement_timeout=%';

     IF NOT FOUND THEN
        v_time_back_msec := 60 * 1000;
     END IF;

     /* manyachello */
     v_time_back_msec := v_time_back_msec + 5000;

    /* ~~~ bo_aggr_brand_currency ~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   v_job_start_time := clock_timestamp();
   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_brand_currency" started'; RETURN NEXT;

   SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_brand_currency' AND conf_key = 'lock_record' FOR UPDATE;

   IF NOT FOUND THEN
      RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_brand_currency/lock_record" pair';
   END IF;

   v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                             ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                             );

    /* Automatically prevent too huge intervals */
     IF (v_force_end_time - v_last_inserted_at) > v_huge_interval_rounds THEN
        v_force_end_time := v_last_inserted_at + v_huge_interval_rounds;
     END IF;

    WITH fresh_data AS
    (
        SELECT
        Date_Trunc('HOUR', h.finished_at)       AS date_hour
        ,h.brand_id                             AS brand_id
        ,h.currency                             AS currency_code
        ,SUM(h.total_bet)                       AS bet
        ,SUM(h.total_win)                       AS win
        ,SUM(h.total_bet)  - SUM(h.total_win)   AS revenue
        ,COUNT(h.id)::BIGINT                    AS finished_rounds
        ,MAX(COALESCE(inserted_at, started_at)) AS max_inserted_at
    FROM  rounds_finished AS h
    WHERE NOT h.test
      AND COALESCE(inserted_at, started_at) >= v_last_inserted_at
      AND COALESCE(inserted_at, started_at) <  v_force_end_time
    GROUP BY Date_Trunc('HOUR', h.finished_at)
    ,h.brand_id
    ,h.currency
    ),
    cte_upsert AS
    (
        INSERT INTO bo_aggr_brand_currency (date_hour, date_day, brand_id, currency_code, bet, win, revenue, finished_rounds)
        SELECT
                date_hour
                ,Date_Trunc('DAY', date_hour)::DATE     AS date_day
                ,brand_id
                ,currency_code
                ,bet
                ,win
                ,revenue
                ,finished_rounds
        FROM fresh_data
        ON CONFLICT (date_hour, brand_id, currency_code) DO
        UPDATE SET
                bet             = bo_aggr_brand_currency.bet            + EXCLUDED.bet,
                win             = bo_aggr_brand_currency.win            + EXCLUDED.win,
                revenue         = bo_aggr_brand_currency.revenue        + EXCLUDED.revenue,
                finished_rounds = bo_aggr_brand_currency.finished_rounds + EXCLUDED.finished_rounds
    )
   SELECT  MAX(max_inserted_at), COUNT(*)
   FROM   fresh_data
   INTO    v_new_inserted_at, v_counter;

   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_brand_currency" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

   UPDATE bo_aggr_config SET
           conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
   WHERE  aggr_job_name = 'bo_aggr_brand_currency'
   AND  conf_key = 'lock_record';

   -- Log
   INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
      VALUES ('bo_aggr_brand_currency', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time), v_force_end_time);

   /* ~~~ bo_aggr_player_rounds ~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   v_job_start_time := clock_timestamp();
   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_player_rounds" started'; RETURN NEXT;

   SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_player_rounds' AND conf_key = 'lock_record' FOR UPDATE;

   IF NOT FOUND THEN
      RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_player_rounds/lock_record" pair';
   END IF;

   v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                             ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                             );

    /* Automatically prevent too huge intervals */
    IF (v_force_end_time - v_last_inserted_at) > v_huge_interval_rounds THEN
    v_force_end_time := v_last_inserted_at + v_huge_interval_rounds;
    END IF;

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_rounds_finished_fresh AS
        SELECT Date_Trunc('HOUR', h.finished_at)          AS date_hour
            ,Date_Trunc('HOUR', h.finished_at)::date    AS date_day
            ,h.brand_id                                 AS brand_id
            ,h.player_code
            ,h.game_code
            ,h.currency                                 AS currency_code
            ,MAX(h.device_id)                           AS device_code    /* This is wrong! We should add this field to the primary key!!! */
            ,COUNT(h.id)::BIGINT                        AS rounds_qty
            ,SUM(h.total_events)::INTEGER               AS events_qty
            ,SUM(h.total_bet)                           AS total_bet
            ,SUM(h.total_win)                           AS total_win
            ,SUM(h.total_bet)  - SUM(h.total_win)       AS total_revenue
            ,null::numeric                              AS start_balance
            ,null::numeric                              AS end_balance
            ,min(h.started_at)                          AS first_activity
            ,MAX(h.finished_at)                         AS last_activity
            ,MAX(COALESCE(inserted_at, started_at))     AS max_inserted_at
        FROM rounds_finished AS h
        WHERE
            NOT  h.test
            AND  COALESCE(inserted_at, started_at) >= v_last_inserted_at
            AND  COALESCE(inserted_at, started_at) <  v_force_end_time
        GROUP BY h.player_code, h.brand_id, h.currency, h.game_code, date_trunc('HOUR', h.finished_at);

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_rounds_finished_deleted AS SELECT * FROM bo_aggr_player_rounds LIMIT 0;
    WITH
    cte_delete_existing_data AS (
        DELETE FROM bo_aggr_player_rounds d
        USING tmp_bo_aggr_refresh_jobs_rounds_finished_fresh f
        WHERE  d.date_hour      = f.date_hour
            AND  d.brand_id       = f.brand_id
            AND  d.game_code      = f.game_code
            AND  d.currency_code  = f.currency_code
            AND  d.player_code    = f.player_code
        RETURNING d.*
    )
    INSERT INTO tmp_bo_aggr_refresh_jobs_rounds_finished_deleted
    SELECT * FROM cte_delete_existing_data;

    INSERT INTO bo_aggr_player_rounds (date_hour, date_day, brand_id, player_code, game_code, device_code, currency_code, exchange_rate, rounds_qty, events_qty, total_bet, total_win, total_revenue, start_balance, end_balance, first_activity, last_activity)
        SELECT fd.date_hour,
            fd.date_hour::DATE,
            fd.brand_id,
            fd.player_code,
            fd.game_code,
            coalesce(fd.device_code, '') AS device_code,
            fd.currency_code,
            fd.exchange_rate,
            fd.rounds_qty,
            fd.events_qty,
            fd.total_bet,
            fd.total_win,
            fd.total_revenue,
            fd.start_balance,
            fd.end_balance,
            fd.first_activity,
            fd.last_activity
        FROM (
                SELECT date_hour
                    ,brand_id
                    ,player_code
                    ,game_code
                    ,currency_code
                    ,MAX(device_code)       AS device_code
                    ,Avg(exchange_rate)     AS exchange_rate
                    ,SUM(rounds_qty   )     AS rounds_qty
                    ,SUM(events_qty   )     AS events_qty
                    ,SUM(total_bet    )     AS total_bet
                    ,SUM(total_win    )     AS total_win
                    ,SUM(total_revenue)     AS total_revenue
                    ,(array_remove(array_agg(start_balance ORDER BY first_activity ASC), NULL))[1] AS start_balance
                    ,(array_remove(array_agg(end_balance ORDER BY last_activity DESC), NULL))[1] AS end_balance
                    ,Min(first_activity)    AS first_activity
                    ,MAX(last_activity)     AS last_activity
                FROM   (
                        SELECT date_hour
                                ,brand_id
                                ,player_code
                                ,game_code
                                ,device_code
                                ,currency_code
                                ,(SELECT cr2.rate
                                FROM   currency_rates cr2
                                WHERE  cr2.currency_code = t0.currency_code
                                    AND  cr2.rate_date <= t0.date_day
                                ORDER BY cr2.rate_date DESC LIMIT 1) AS exchange_rate
                                ,rounds_qty
                                ,events_qty
                                ,total_bet
                                ,total_win
                                ,total_revenue
                                ,start_balance
                                ,end_balance
                                ,first_activity
                                ,last_activity
                        FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_fresh t0
                        UNION ALL
                        SELECT date_hour
                                ,brand_id
                                ,player_code
                                ,game_code
                                ,NULL::VARCHAR AS device_code /* We can't use old value, because insert can fault due to current PK */
                                ,currency_code
                                ,exchange_rate
                                ,rounds_qty
                                ,events_qty
                                ,total_bet
                                ,total_win
                                ,total_revenue
                                ,start_balance
                                ,end_balance
                                ,first_activity
                                ,last_activity
                        FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_deleted
                        ) t
                GROUP BY date_hour, brand_id, game_code, currency_code, player_code
            ) fd;

    SELECT  MAX(max_inserted_at), COUNT(*)
    FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_fresh
    INTO    v_new_inserted_at, v_counter;

    DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_rounds_finished_fresh;
    DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_rounds_finished_deleted;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_player_rounds" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

        UPDATE bo_aggr_config SET
                conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
        WHERE  aggr_job_name = 'bo_aggr_player_rounds'
            AND  conf_key = 'lock_record';

    -- Log
    INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
        VALUES ('bo_aggr_player_rounds', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time), v_force_end_time);

        /* ~~~ bo_aggr_win_bets ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        v_job_start_time := clock_timestamp();
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets" started'; RETURN NEXT;

        SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_win_bets' AND conf_key = 'lock_record' FOR UPDATE;

        IF NOT FOUND THEN
            RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_win_bets/lock_record" pair';
        END IF;

        v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                                ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                                );

        /* Automatically prevent too huge intervals */
        IF (v_force_end_time - v_last_inserted_at) > v_huge_interval_win_bet THEN
            v_force_end_time := v_last_inserted_at + v_huge_interval_win_bet;
        END IF;

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh AS
        SELECT date_trunc('HOUR', b.payment_date) AS payment_date_hour,
                b.brand_id,
                b.game_code,
                b.player_code,
                b.currency AS currency_code,
                COUNT(DISTINCT b.game_id) AS played_games_qty,
                SUM(case when b.transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN bet_rollback = FALSE THEN (CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) ELSE (-1)*(CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) END) end) AS total_bets,
                SUM(case when b.transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) end) AS total_wins,
                MAX(b.payment_date)::timestamp(0) as last_payment_ts,
                SUM(case when b.transaction_type = 'jackpot' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end) as total_jp_wins,
                SUM(case when b.transaction_type = 'free_bet' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end) as total_freebet_wins,
                MAX(COALESCE(inserted_at, payment_date)) AS max_inserted_at,
                SUM(debit) AS debit,
                SUM(credit) AS credit
        FROM  wallet_win_bet b
        WHERE COALESCE(inserted_at, payment_date) >= v_last_inserted_at
            AND COALESCE(inserted_at, payment_date) < v_force_end_time
            AND COALESCE(b.is_test, false) = false
            AND b.game_code IS NOT NULL
                -- AND b.transaction_type is null
        GROUP BY b.player_code, b.brand_id, b.currency, b.game_code, date_trunc('HOUR', b.payment_date);

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted AS SELECT * FROM bo_aggr_win_bets LIMIT 0;
    WITH cte_delete_existing_data AS (
            DELETE FROM bo_aggr_win_bets d
            USING tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh f
            WHERE d.payment_date_hour = f.payment_date_hour
            AND d.brand_id          = f.brand_id
            AND d.game_code         = f.game_code
            AND d.currency_code     = f.currency_code
            AND d.player_code       = f.player_code
            RETURNING d.*
        )
    INSERT INTO tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted
    SELECT * FROM cte_delete_existing_data;

    INSERT INTO bo_aggr_win_bets (payment_date_hour, payment_date_day, brand_id, game_code, player_code,
            currency_code, played_games_qty, total_bets, total_wins, last_payment_ts,
            exchange_rate, total_jp_wins, total_freebet_wins, debit, credit)
    SELECT
        d.payment_date_hour,
        d.payment_date_hour::DATE AS payment_date_day,
        d.brand_id,
        d.game_code,
        d.player_code,
        d.currency_code,
        /* bo_aggr_player_rounds - contains only finished rounds. TLU: This value is incorrect and should be fixed. wallet_win_bet should also has finished flags */
        COALESCE( (SELECT pr.rounds_qty
                    FROM   bo_aggr_player_rounds pr
                    WHERE  d.payment_date_hour = pr.date_hour
                    AND  d.brand_id = pr.brand_id
                    AND  d.game_code = pr.game_code
                    AND  d.currency_code = pr.currency_code
                    AND  d.player_code = pr.player_code
                    LIMIT 1), 0) AS played_games_qty,
        d.total_bets,
        d.total_wins,
        d.last_payment_ts,
        (SELECT cr2.rate
        FROM   currency_rates cr2
        WHERE  cr2.currency_code = d.currency_code
            AND  cr2.rate_date <= d.payment_date_hour::DATE
        ORDER BY cr2.rate_date DESC LIMIT 1) AS exchange_rate,
        d.total_jp_wins,
        d.total_freebet_wins,
        d.debit,
        d.credit
        FROM (
                SELECT payment_date_hour
                    ,brand_id
                    ,game_code
                    ,player_code
                    ,currency_code
                    --,SUM(played_games_qty) AS played_games_qty
                    ,SUM(total_bets) AS total_bets
                    ,SUM(total_wins) AS total_wins
                    ,MAX(last_payment_ts) AS last_payment_ts
                    ,SUM(total_jp_wins     ) AS total_jp_wins
                    ,SUM(total_freebet_wins) AS total_freebet_wins
                    ,SUM(debit) AS debit
                    ,SUM(credit) AS credit
                FROM   (
                        SELECT payment_date_hour
                                ,brand_id
                                ,game_code
                                ,player_code
                                ,currency_code
                                --,played_games_qty
                                ,total_bets
                                ,total_wins
                                ,last_payment_ts
                                ,total_jp_wins
                                ,total_freebet_wins
                                ,debit
                                ,credit
                        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh
                        UNION ALL
                        SELECT payment_date_hour
                                ,brand_id
                                ,game_code
                                ,player_code
                                ,currency_code
                                --,played_games_qty
                                ,total_bets
                                ,total_wins
                                ,last_payment_ts
                                ,total_jp_wins
                                ,total_freebet_wins
                                ,debit
                                ,credit
                        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted
                        ) uni_on
                GROUP BY payment_date_hour, brand_id, game_code, currency_code, player_code
            ) d;

        SELECT MAX(max_inserted_at), COUNT(*)
        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh
        INTO   v_new_inserted_at, v_counter;

        DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh;
        DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted;

        -- GET DIAGNOSTICS v_counter = ROW_COUNT;
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

            UPDATE bo_aggr_config SET
                conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
            WHERE  aggr_job_name = 'bo_aggr_win_bets'
            AND  conf_key = 'lock_record';

        -- Log
        INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
            VALUES ('bo_aggr_win_bets', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, v_new_inserted_at, v_force_end_time);


        /* ~~~ bo_aggr_win_bets_by_brand ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        v_job_start_time := clock_timestamp();
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets_by_brand" started'; RETURN NEXT;

        SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_win_bets_by_brand' AND conf_key = 'lock_record' FOR UPDATE;

        IF NOT FOUND THEN
            RAISE EXCEPTION 'Table "bo_aggr_win_bets_by_brand" has no valid record for "bo_aggr_win_bets_by_brand/lock_record" pair';
        END IF;

        /* To limit huge load intervals */
        v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                                ,current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)
                                ,v_last_inserted_at + v_huge_interval_win_bet
                                );


        WITH fresh_data as
        ( SELECT b.brand_id,
                b.currency as currency_code,
                SUM(case when transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN bet_rollback = FALSE THEN (CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) ELSE (-1)*(CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) END) end) as bet,
                SUM(case when transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) end) as win,
                SUM(case when transaction_type = 'jackpot' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end) as jackpot_win,
                SUM(case when transaction_type = 'free_bet' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end) as free_bet_win,
                COUNT(*) as events_count,
                MAX(b.payment_date) as last_payment_ts,
                MAX(COALESCE(inserted_at, payment_date)) AS max_inserted_at
            FROM wallet_win_bet b
            WHERE COALESCE(inserted_at, payment_date) > v_last_inserted_at
                AND COALESCE(inserted_at, payment_date) < v_force_end_time
                AND coalesce(b.is_test, false) = false
                AND b.game_code IS NOT NULL
            GROUP BY b.brand_id, b.currency
        ),
        upsert_aggr AS
        ( INSERT INTO bo_aggr_win_bets_by_brand (brand_id, currency_code, bet, win, revenue, jackpot_win, free_bet_win)
                SELECT brand_id, currency_code, bet, win, (bet - win) as revenue, jackpot_win, free_bet_win
                FROM fresh_data
        ON CONFLICT ON CONSTRAINT bo_aggr_win_bets_by_brand_pkey DO
                UPDATE SET bet = bo_aggr_win_bets_by_brand.bet + EXCLUDED.bet,
                        win = bo_aggr_win_bets_by_brand.win + EXCLUDED.win,
                        revenue = bo_aggr_win_bets_by_brand.revenue + EXCLUDED.revenue,
                        jackpot_win = bo_aggr_win_bets_by_brand.jackpot_win + EXCLUDED.jackpot_win,
                        free_bet_win = bo_aggr_win_bets_by_brand.free_bet_win + EXCLUDED.free_bet_win
        )
        SELECT MAX(max_inserted_at), SUM(events_count)
        FROM fresh_data
        INTO v_new_inserted_at, v_counter;


        -- GET DIAGNOSTICS v_counter = ROW_COUNT;
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets_by_brand" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

            UPDATE bo_aggr_config SET
                conf_value = coalesce(v_new_inserted_at, v_force_end_time - '1 msec'::INTERVAL)
            WHERE  aggr_job_name = 'bo_aggr_win_bets_by_brand'
            AND  conf_key = 'lock_record';

        -- Log
        INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
            VALUES ('bo_aggr_win_bets_by_brand', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, v_new_inserted_at, v_force_end_time);

        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job finished'; RETURN NEXT;

        /* ~~~ Maintenance ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        -- Clear old logs
        DELETE FROM bo_aggr_history WHERE started_at < (current_date - Interval '1 MONTH');

        -- Reset config parameters
        reset work_mem;

        RETURN;
    END;
$function$
;


RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_aggr_refresh_jobs(TIMESTAMP, VARCHAR);
--rollback ALTER FUNCTION fnc_bo_aggr_refresh_jobs_before_4_46_0_2 (TIMESTAMP, VARCHAR) RENAME TO fnc_bo_aggr_refresh_jobs;
--rollback RESET search_path;

--changeset valdis.akmens:2020-10-19-DEVOPS-10732-fix-aggregation-intervals endDelimiter:# stripComments:false
--comment Add aditional check for large intervals, COALESCE for bo_aggr_win_bets.total_bets, bo_aggr_win_bets.total_wins
SET search_path TO swmanagement;

ALTER FUNCTION fnc_bo_aggr_refresh_jobs (TIMESTAMP, VARCHAR) RENAME TO fnc_bo_aggr_refresh_jobs_before_4_46_0_3;

CREATE OR REPLACE FUNCTION fnc_bo_aggr_refresh_jobs(p_force_end_hour timestamp without time zone, p_work_mem character varying DEFAULT NULL::character varying)
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
*******************************************************************************

   Object Name:   fnc_bo_aggr_refresh_jobs
   Purpose    :   To perform B/O aggregation jobs
   History    :
      1.0.0
         Date    : Feb 03, 2017
         Authors : Timur Luchkin
         Notes   : Release (BYDEVO-260)

      1.0.1
         Date    : Mar 07, 2017
         Authors : Timur Luchkin
         Notes   : Add more details required for watchdog to monitor jobs
                   (BYSWBO-73)

      1.0.2
         Date    : Jun 09, 2017
         Authors : Timur Luchkin
         Notes   : Tables renamed to follow snake style
                   (BYDEVO-578)
                   Add logs history logging

      1.0.3
         Date    : Jul 17, 2017
         Authors : Andrey Shmigiro
         Notes   : Added table "bo_aggr_win_bets" for aggregation data from "wallet_win_bet"
                   (BYDEVO-513)

      1.0.4
         Date    : Jul 24, 2017
         Authors : Andrey Shmigiro
         Notes   : Added table "bo_aggr_player_rounds" for aggregation data about played
                     rounds from "bo_aggr_rounds";
                   Added column "exchange_rate" to table "bo_aggr_win_bets";
                   Changed calculation method for column "played_games_qty" in table "bo_aggr_win_bets";
                   (SWS-1561)

      1.0.5
         Date    : Aug 15, 2017
         Authors : Andrey Shmigiro
         Notes   : Added column "start_balance", "end_balance", "device_code" to table
                   "bo_aggr_player_rounds";
                   (SWS-1651 / SWS-1720)

      1.1.0
         Date    : Sep 28, 2017
         Authors : Timur Luchkin
         Notes   : Fix issues with missed spins (SWS-1873)
                   More logging details

      1.1.1
         Date    : Oct 19, 2017
         Authors : Andrey Shmigiro
         Notes   : Exclude test wallet operations from bo_aggr_win_bets (SWS-1789)
                   Exclude jackpot wins from bo_aggr_win_bets (SWS-1863)
                   Exclude free bets from bo_aggr_win_bets (SWS-1943)

      1.1.2
         Date    : Oct 19, 2017
         Authors : Andrey Shmigiro
         Notes   : Added columns for jackpot's and free_bet's wins to bo_agr_win_bets's tbl (SWS-2805)
                   Exclude NULLs values of start&end balances for bo_aggr_rounds

      1.1.3
            Date    : Jan 12, 2018
            Authors : Valdis Akmens
            Notes   : Change aggregation of wallet_win_bet from payment_date to inserted_at (BYDEVO-1280)

      1.1.4
            Date    : Mar 08, 2018
            Authors : Valdis Akmens
            Notes   : fnc_bo_aggr_refresh_jobs takes too much time after the partitioning has been installed (SWDB-24)
                Added new parameter p_work_mem to set larger work_mem for function to get rid off "Sort Method: external merge  Disk"
                Changed bo_aggr_win_bets aggregation to replace "LEFT JOIN" with sub-queries (because of partitioning, wrong estimations lead to non-optimal JOIN strategies)
                p_work_mem: NULL, 64MB, 128MB, 256MB ..

      1.1.5
            Date    : Jun 11, 2018
            Authors : Valdis Akmens
            Notes   : Change aggregation source for tables "bo_aggr_brand_currency" and "bo_aggr_player_rounds"
                         from "bo_aggr_rounds" to "rounds_history"(SWDB-49)

      1.1.6
            Date    : Sep 17, 2018
            Authors : Valdis Akmens
            Notes   : Remove "bo_aggr_rounds" from aggregation completely (SWDB-69)

      1.1.7
          Date    : Nov 06, 2018
          Authors : Timur Luchkin
          Notes   : Change aggregation logic to allow "bo_aggr_player_rounds" and "bo_aggr_win_bets" tables partitioning (SWDB-44)

      1.1.8
          Date    : Mar 22, 2019
          Authors : Timur Luchkin
          Notes   : Change aggregation source table from rounds_history to rounds_finished

      1.1.9
         Date    : Apr 30, 2019
         Authors : Andrey Shmigiro
         Notes   : Added column "debit", "credit" to table "bo_aggr_win_bets" (DEVOPS-5075);

      1.2.0
         Date    : Jul 30, 2019
         Authors : Valdis Akmens
         Notes   : Change aggregation of wins for table "bo_aggr_win_bets" and "bo_aggr_win_bets_by_brand" because of new wallet_transactions_types (SWS-11908);

      1.2.1
         Date    : Aug 22, 2019
         Authors : Valdis Akmens
         Notes   : Subtract "bet" for table "bo_aggr_win_bets" and "bo_aggr_win_bets_by_brand" aggregation when bet_rollback = TRUE  (SWS-12584);

      1.2.2
         Date    : Apr 15, 2020
         Authors : Valdis Akmens
         Notes   : Replace CTE(DELETE+INSERT) to temporary tables (SWDB-132);

      1.2.2.1
         Date    : Oct 14, 2020
         Authors : Valdis Akmens
         Notes   : Ignore transactions in wallet_win_bet where game_code is null (SWS-22290);

      1.2.3
         Date    : Oct 12, 2020
         Authors : Valdis Akmens
         Notes   : Change aggregation fields for win,bet calculation, based on wallet_win_bet.ggr_calculation (SWB365-254);

      1.2.4
         Date    : Oct 19, 2020
         Authors : Valdis Akmens
         Notes   : Add aditional check for large intervals, COALESCE for bo_aggr_win_bets.total_bets, bo_aggr_win_bets.total_wins (DEVOPS-10732, SWS-22341);

  Sample run:
      SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL);
      SELECT * FROM fnc_bo_aggr_refresh_jobs ('2016-11-28 23:00:00');

	    SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL, NULL);
      SELECT * FROM fnc_bo_aggr_refresh_jobs (NULL, '64MB');
	    SELECT * FROM fnc_bo_aggr_refresh_jobs ('2017-09-25 06:00:00', '64MB');

*******************************************************************************
*/
DECLARE
   v_last_inserted_at      TIMESTAMP;
   v_new_inserted_at       TIMESTAMP;
   v_time_back_msec        INTEGER;
   v_counter               BIGINT;
   v_job_start_time        TIMESTAMP;
   v_force_end_time        TIMESTAMP;
   v_huge_interval  INTERVAL := '12 hours'::INTERVAL;
BEGIN

     log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job started'; RETURN NEXT;

     /* Check if MDB */
     IF (SELECT pg_is_in_recovery()) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Not a Master DB. Skip'; RETURN NEXT;
        RETURN;
     END IF;

     /* Change work_mem parameter */
     IF p_work_mem IS NOT NULL THEN
        EXECUTE 'SET work_mem TO '''||p_work_mem||'''' ;
     END IF;

     /* To prevent misses of the lazy offloaded data */
     SELECT split_part(sett,'=',2)::INTEGER
     INTO   v_time_back_msec
     FROM   (SELECT unnest(useconfig) AS sett
             FROM   pg_user
            WHERE  usename = 'redis_game_offloader'
            ) t1
     WHERE sett LIKE 'statement_timeout=%';

     IF NOT FOUND THEN
        v_time_back_msec := 60 * 1000;
     END IF;

     /* manyachello */
     v_time_back_msec := v_time_back_msec + 5000;

    /* ~~~ bo_aggr_brand_currency ~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   v_job_start_time := clock_timestamp();
   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_brand_currency" started'; RETURN NEXT;

   SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_brand_currency' AND conf_key = 'lock_record' FOR UPDATE;

   IF NOT FOUND THEN
      RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_brand_currency/lock_record" pair';
   END IF;

   v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                             ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                             );

    /* Automatically prevent too huge intervals */
     IF (v_force_end_time - v_last_inserted_at) > v_huge_interval THEN
        v_force_end_time := v_last_inserted_at + v_huge_interval;
     END IF;

    WITH fresh_data AS
    (
        SELECT
        Date_Trunc('HOUR', h.finished_at)       AS date_hour
        ,h.brand_id                             AS brand_id
        ,h.currency                             AS currency_code
        ,SUM(h.total_bet)                       AS bet
        ,SUM(h.total_win)                       AS win
        ,SUM(h.total_bet)  - SUM(h.total_win)   AS revenue
        ,COUNT(h.id)::BIGINT                    AS finished_rounds
        ,MAX(COALESCE(inserted_at, started_at)) AS max_inserted_at
    FROM  rounds_finished AS h
    WHERE NOT h.test
      AND COALESCE(inserted_at, started_at) >= v_last_inserted_at
      AND COALESCE(inserted_at, started_at) <  v_force_end_time
    GROUP BY Date_Trunc('HOUR', h.finished_at)
    ,h.brand_id
    ,h.currency
    ),
    cte_upsert AS
    (
        INSERT INTO bo_aggr_brand_currency (date_hour, date_day, brand_id, currency_code, bet, win, revenue, finished_rounds)
        SELECT
                date_hour
                ,Date_Trunc('DAY', date_hour)::DATE     AS date_day
                ,brand_id
                ,currency_code
                ,bet
                ,win
                ,revenue
                ,finished_rounds
        FROM fresh_data
        ON CONFLICT (date_hour, brand_id, currency_code) DO
        UPDATE SET
                bet             = bo_aggr_brand_currency.bet            + EXCLUDED.bet,
                win             = bo_aggr_brand_currency.win            + EXCLUDED.win,
                revenue         = bo_aggr_brand_currency.revenue        + EXCLUDED.revenue,
                finished_rounds = bo_aggr_brand_currency.finished_rounds + EXCLUDED.finished_rounds
    )
   SELECT  MAX(max_inserted_at), COUNT(*)
   FROM   fresh_data
   INTO    v_new_inserted_at, v_counter;

   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_brand_currency" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

   UPDATE bo_aggr_config SET
           conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
   WHERE  aggr_job_name = 'bo_aggr_brand_currency'
   AND  conf_key = 'lock_record';

   -- Log
   INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
      VALUES ('bo_aggr_brand_currency', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time), v_force_end_time);

   /* ~~~ bo_aggr_player_rounds ~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
   v_job_start_time := clock_timestamp();
   log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_player_rounds" started'; RETURN NEXT;

   SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_player_rounds' AND conf_key = 'lock_record' FOR UPDATE;

   IF NOT FOUND THEN
      RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_player_rounds/lock_record" pair';
   END IF;

   v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                             ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                             );

    /* Automatically prevent too huge intervals */
    IF (v_force_end_time - v_last_inserted_at) > v_huge_interval THEN
    v_force_end_time := v_last_inserted_at + v_huge_interval;
    END IF;

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_rounds_finished_fresh AS
        SELECT Date_Trunc('HOUR', h.finished_at)          AS date_hour
            ,Date_Trunc('HOUR', h.finished_at)::date    AS date_day
            ,h.brand_id                                 AS brand_id
            ,h.player_code
            ,h.game_code
            ,h.currency                                 AS currency_code
            ,MAX(h.device_id)                           AS device_code    /* This is wrong! We should add this field to the primary key!!! */
            ,COUNT(h.id)::BIGINT                        AS rounds_qty
            ,SUM(h.total_events)::INTEGER               AS events_qty
            ,SUM(h.total_bet)                           AS total_bet
            ,SUM(h.total_win)                           AS total_win
            ,SUM(h.total_bet)  - SUM(h.total_win)       AS total_revenue
            ,null::numeric                              AS start_balance
            ,null::numeric                              AS end_balance
            ,min(h.started_at)                          AS first_activity
            ,MAX(h.finished_at)                         AS last_activity
            ,MAX(COALESCE(inserted_at, started_at))     AS max_inserted_at
        FROM rounds_finished AS h
        WHERE
            NOT  h.test
            AND  COALESCE(inserted_at, started_at) >= v_last_inserted_at
            AND  COALESCE(inserted_at, started_at) <  v_force_end_time
        GROUP BY h.player_code, h.brand_id, h.currency, h.game_code, date_trunc('HOUR', h.finished_at);

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_rounds_finished_deleted AS SELECT * FROM bo_aggr_player_rounds LIMIT 0;
    WITH
    cte_delete_existing_data AS (
        DELETE FROM bo_aggr_player_rounds d
        USING tmp_bo_aggr_refresh_jobs_rounds_finished_fresh f
        WHERE  d.date_hour      = f.date_hour
            AND  d.brand_id       = f.brand_id
            AND  d.game_code      = f.game_code
            AND  d.currency_code  = f.currency_code
            AND  d.player_code    = f.player_code
        RETURNING d.*
    )
    INSERT INTO tmp_bo_aggr_refresh_jobs_rounds_finished_deleted
    SELECT * FROM cte_delete_existing_data;

    INSERT INTO bo_aggr_player_rounds (date_hour, date_day, brand_id, player_code, game_code, device_code, currency_code, exchange_rate, rounds_qty, events_qty, total_bet, total_win, total_revenue, start_balance, end_balance, first_activity, last_activity)
        SELECT fd.date_hour,
            fd.date_hour::DATE,
            fd.brand_id,
            fd.player_code,
            fd.game_code,
            coalesce(fd.device_code, '') AS device_code,
            fd.currency_code,
            fd.exchange_rate,
            fd.rounds_qty,
            fd.events_qty,
            fd.total_bet,
            fd.total_win,
            fd.total_revenue,
            fd.start_balance,
            fd.end_balance,
            fd.first_activity,
            fd.last_activity
        FROM (
                SELECT date_hour
                    ,brand_id
                    ,player_code
                    ,game_code
                    ,currency_code
                    ,MAX(device_code)       AS device_code
                    ,Avg(exchange_rate)     AS exchange_rate
                    ,SUM(rounds_qty   )     AS rounds_qty
                    ,SUM(events_qty   )     AS events_qty
                    ,SUM(total_bet    )     AS total_bet
                    ,SUM(total_win    )     AS total_win
                    ,SUM(total_revenue)     AS total_revenue
                    ,(array_remove(array_agg(start_balance ORDER BY first_activity ASC), NULL))[1] AS start_balance
                    ,(array_remove(array_agg(end_balance ORDER BY last_activity DESC), NULL))[1] AS end_balance
                    ,Min(first_activity)    AS first_activity
                    ,MAX(last_activity)     AS last_activity
                FROM   (
                        SELECT date_hour
                                ,brand_id
                                ,player_code
                                ,game_code
                                ,device_code
                                ,currency_code
                                ,(SELECT cr2.rate
                                FROM   currency_rates cr2
                                WHERE  cr2.currency_code = t0.currency_code
                                    AND  cr2.rate_date <= t0.date_day
                                ORDER BY cr2.rate_date DESC LIMIT 1) AS exchange_rate
                                ,rounds_qty
                                ,events_qty
                                ,total_bet
                                ,total_win
                                ,total_revenue
                                ,start_balance
                                ,end_balance
                                ,first_activity
                                ,last_activity
                        FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_fresh t0
                        UNION ALL
                        SELECT date_hour
                                ,brand_id
                                ,player_code
                                ,game_code
                                ,NULL::VARCHAR AS device_code /* We can't use old value, because insert can fault due to current PK */
                                ,currency_code
                                ,exchange_rate
                                ,rounds_qty
                                ,events_qty
                                ,total_bet
                                ,total_win
                                ,total_revenue
                                ,start_balance
                                ,end_balance
                                ,first_activity
                                ,last_activity
                        FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_deleted
                        ) t
                GROUP BY date_hour, brand_id, game_code, currency_code, player_code
            ) fd;

    SELECT  MAX(max_inserted_at), COUNT(*)
    FROM   tmp_bo_aggr_refresh_jobs_rounds_finished_fresh
    INTO    v_new_inserted_at, v_counter;

    DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_rounds_finished_fresh;
    DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_rounds_finished_deleted;

    log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_player_rounds" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

        UPDATE bo_aggr_config SET
                conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
        WHERE  aggr_job_name = 'bo_aggr_player_rounds'
            AND  conf_key = 'lock_record';

    -- Log
    INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
        VALUES ('bo_aggr_player_rounds', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time), v_force_end_time);

        /* ~~~ bo_aggr_win_bets ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        v_job_start_time := clock_timestamp();
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets" started'; RETURN NEXT;

        SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_win_bets' AND conf_key = 'lock_record' FOR UPDATE;

        IF NOT FOUND THEN
            RAISE EXCEPTION 'Table "bo_aggr_config" has no valid record for "bo_aggr_win_bets/lock_record" pair';
        END IF;

        v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec))))
                                ,Date_Trunc('HOUR', (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                                );
        -- Add aditional check to prevent aggregating data of wallet_win_bet for period where data for rounds_finished is not aggregated
        v_force_end_time:= LEAST(v_force_end_time, COALESCE((SELECT MAX(date_hour) FROM bo_aggr_player_rounds) + '1 HOUR'::INTERVAL,v_force_end_time));

        /* Automatically prevent too huge intervals */
        IF (v_force_end_time - v_last_inserted_at) > v_huge_interval THEN
            v_force_end_time := v_last_inserted_at + v_huge_interval;

        END IF;

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh AS
        SELECT date_trunc('HOUR', b.payment_date) AS payment_date_hour,
                b.brand_id,
                b.game_code,
                b.player_code,
                b.currency AS currency_code,
                COUNT(DISTINCT b.game_id) AS played_games_qty,
                COALESCE(SUM(case when b.transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN bet_rollback = FALSE THEN (CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) ELSE (-1)*(CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) END) end), 0) AS total_bets,
                COALESCE(SUM(case when b.transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) end),0) AS total_wins,
                MAX(b.payment_date)::timestamp(0) as last_payment_ts,
                COALESCE(SUM(case when b.transaction_type = 'jackpot' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end),0) as total_jp_wins,
                COALESCE(SUM(case when b.transaction_type = 'free_bet' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end),0) as total_freebet_wins,
                MAX(COALESCE(inserted_at, payment_date)) AS max_inserted_at,
                SUM(debit) AS debit,
                SUM(credit) AS credit
        FROM  wallet_win_bet b
        WHERE COALESCE(inserted_at, payment_date) >= v_last_inserted_at
            AND COALESCE(inserted_at, payment_date) < v_force_end_time
            AND COALESCE(b.is_test, false) = false
            AND b.game_code IS NOT NULL
                -- AND b.transaction_type is null
        GROUP BY b.player_code, b.brand_id, b.currency, b.game_code, date_trunc('HOUR', b.payment_date);

    CREATE TEMP TABLE tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted AS SELECT * FROM bo_aggr_win_bets LIMIT 0;
    WITH cte_delete_existing_data AS (
            DELETE FROM bo_aggr_win_bets d
            USING tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh f
            WHERE d.payment_date_hour = f.payment_date_hour
            AND d.brand_id          = f.brand_id
            AND d.game_code         = f.game_code
            AND d.currency_code     = f.currency_code
            AND d.player_code       = f.player_code
            RETURNING d.*
        )
    INSERT INTO tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted
    SELECT * FROM cte_delete_existing_data;

    INSERT INTO bo_aggr_win_bets (payment_date_hour, payment_date_day, brand_id, game_code, player_code,
            currency_code, played_games_qty, total_bets, total_wins, last_payment_ts,
            exchange_rate, total_jp_wins, total_freebet_wins, debit, credit)
    SELECT
        d.payment_date_hour,
        d.payment_date_hour::DATE AS payment_date_day,
        d.brand_id,
        d.game_code,
        d.player_code,
        d.currency_code,
        /* bo_aggr_player_rounds - contains only finished rounds. TLU: This value is incorrect and should be fixed. wallet_win_bet should also has finished flags */
        COALESCE( (SELECT pr.rounds_qty
                    FROM   bo_aggr_player_rounds pr
                    WHERE  d.payment_date_hour = pr.date_hour
                    AND  d.brand_id = pr.brand_id
                    AND  d.game_code = pr.game_code
                    AND  d.currency_code = pr.currency_code
                    AND  d.player_code = pr.player_code
                    LIMIT 1), 0) AS played_games_qty,
        d.total_bets,
        d.total_wins,
        d.last_payment_ts,
        (SELECT cr2.rate
        FROM   currency_rates cr2
        WHERE  cr2.currency_code = d.currency_code
            AND  cr2.rate_date <= d.payment_date_hour::DATE
        ORDER BY cr2.rate_date DESC LIMIT 1) AS exchange_rate,
        d.total_jp_wins,
        d.total_freebet_wins,
        d.debit,
        d.credit
        FROM (
                SELECT payment_date_hour
                    ,brand_id
                    ,game_code
                    ,player_code
                    ,currency_code
                    --,SUM(played_games_qty) AS played_games_qty
                    ,SUM(total_bets) AS total_bets
                    ,SUM(total_wins) AS total_wins
                    ,MAX(last_payment_ts) AS last_payment_ts
                    ,SUM(total_jp_wins     ) AS total_jp_wins
                    ,SUM(total_freebet_wins) AS total_freebet_wins
                    ,SUM(debit) AS debit
                    ,SUM(credit) AS credit
                FROM   (
                        SELECT payment_date_hour
                                ,brand_id
                                ,game_code
                                ,player_code
                                ,currency_code
                                --,played_games_qty
                                ,total_bets
                                ,total_wins
                                ,last_payment_ts
                                ,total_jp_wins
                                ,total_freebet_wins
                                ,debit
                                ,credit
                        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh
                        UNION ALL
                        SELECT payment_date_hour
                                ,brand_id
                                ,game_code
                                ,player_code
                                ,currency_code
                                --,played_games_qty
                                ,total_bets
                                ,total_wins
                                ,last_payment_ts
                                ,total_jp_wins
                                ,total_freebet_wins
                                ,debit
                                ,credit
                        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted
                        ) uni_on
                GROUP BY payment_date_hour, brand_id, game_code, currency_code, player_code
            ) d;

        SELECT MAX(max_inserted_at), COUNT(*)
        FROM   tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh
        INTO   v_new_inserted_at, v_counter;

        DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_wallet_win_bet_fresh;
        DROP TABLE IF EXISTS tmp_bo_aggr_refresh_jobs_wallet_win_bet_deleted;

        -- GET DIAGNOSTICS v_counter = ROW_COUNT;
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

            UPDATE bo_aggr_config SET
                conf_value = coalesce(Date_trunc('HOUR', v_new_inserted_at) + '1 HOUR'::INTERVAL, v_force_end_time)
            WHERE  aggr_job_name = 'bo_aggr_win_bets'
            AND  conf_key = 'lock_record';

        -- Log
        INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
            VALUES ('bo_aggr_win_bets', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, v_new_inserted_at, v_force_end_time);


        /* ~~~ bo_aggr_win_bets_by_brand ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        v_job_start_time := clock_timestamp();
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets_by_brand" started'; RETURN NEXT;

        SELECT conf_value INTO v_last_inserted_at FROM bo_aggr_config WHERE aggr_job_name = 'bo_aggr_win_bets_by_brand' AND conf_key = 'lock_record' FOR UPDATE;

        IF NOT FOUND THEN
            RAISE EXCEPTION 'Table "bo_aggr_win_bets_by_brand" has no valid record for "bo_aggr_win_bets_by_brand/lock_record" pair';
        END IF;

        /* To limit huge load intervals */
        v_force_end_time := Least( Coalesce(Date_Trunc('HOUR', p_force_end_hour), (current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)))
                                ,current_timestamp - (Interval '1 MILLISECOND' * v_time_back_msec)
                                ,v_last_inserted_at + v_huge_interval
                                );


        WITH fresh_data as
        ( SELECT b.brand_id,
                b.currency as currency_code,
                 COALESCE(SUM(case when transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN bet_rollback = FALSE THEN (CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) ELSE (-1)*(CASE WHEN ggr_calculation = 'round' THEN b.round_bets ELSE b.bet END) END) end),0) as bet,
                 COALESCE(SUM(case when transaction_type IN ('jackpot', 'free_bet') then 0 else (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) end),0) as win,
                 COALESCE(SUM(case when transaction_type = 'jackpot' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end),0) as jackpot_win,
                 COALESCE(SUM(case when transaction_type = 'free_bet' then (CASE WHEN ggr_calculation = 'round' THEN b.round_wins ELSE b.win END) else 0 end),0) as free_bet_win,
                COUNT(*) as events_count,
                MAX(b.payment_date) as last_payment_ts,
                MAX(COALESCE(inserted_at, payment_date)) AS max_inserted_at
            FROM wallet_win_bet b
            WHERE COALESCE(inserted_at, payment_date) > v_last_inserted_at
                AND COALESCE(inserted_at, payment_date) < v_force_end_time
                AND coalesce(b.is_test, false) = false
                AND b.game_code IS NOT NULL
            GROUP BY b.brand_id, b.currency
        ),
        upsert_aggr AS
        ( INSERT INTO bo_aggr_win_bets_by_brand (brand_id, currency_code, bet, win, revenue, jackpot_win, free_bet_win)
                SELECT brand_id, currency_code, bet, win, (bet - win) as revenue, jackpot_win, free_bet_win
                FROM fresh_data
        ON CONFLICT ON CONSTRAINT bo_aggr_win_bets_by_brand_pkey DO
                UPDATE SET bet = bo_aggr_win_bets_by_brand.bet + EXCLUDED.bet,
                        win = bo_aggr_win_bets_by_brand.win + EXCLUDED.win,
                        revenue = bo_aggr_win_bets_by_brand.revenue + EXCLUDED.revenue,
                        jackpot_win = bo_aggr_win_bets_by_brand.jackpot_win + EXCLUDED.jackpot_win,
                        free_bet_win = bo_aggr_win_bets_by_brand.free_bet_win + EXCLUDED.free_bet_win
        )
        SELECT MAX(max_inserted_at), SUM(events_count)
        FROM fresh_data
        INTO v_new_inserted_at, v_counter;


        -- GET DIAGNOSTICS v_counter = ROW_COUNT;
        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation of "bo_aggr_win_bets_by_brand" finished. Rows processed: '||quote_nullable(Coalesce(v_counter, 0)); RETURN NEXT;

            UPDATE bo_aggr_config SET
                conf_value = coalesce(v_new_inserted_at, v_force_end_time - '1 msec'::INTERVAL)
            WHERE  aggr_job_name = 'bo_aggr_win_bets_by_brand'
            AND  conf_key = 'lock_record';

        -- Log
        INSERT INTO bo_aggr_history (aggr_job_name, started_at, finished_at, records_processed, records_from_ts, records_to_ts, force_limit_to_ts)
            VALUES ('bo_aggr_win_bets_by_brand', v_job_start_time, clock_timestamp(), Coalesce(v_counter, 0), v_last_inserted_at, v_new_inserted_at, v_force_end_time);

        log_time := clock_timestamp(); log_msg := 'INFO: Aggregation job finished'; RETURN NEXT;

        /* ~~~ Maintenance ~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
        -- Clear old logs
        DELETE FROM bo_aggr_history WHERE started_at < (current_date - Interval '1 MONTH');

        -- Reset config parameters
        reset work_mem;

        RETURN;
    END;
$function$
;


RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP FUNCTION IF EXISTS fnc_bo_aggr_refresh_jobs(TIMESTAMP, VARCHAR);
--rollback ALTER FUNCTION fnc_bo_aggr_refresh_jobs_before_4_46_0_3 (TIMESTAMP, VARCHAR) RENAME TO fnc_bo_aggr_refresh_jobs;
--rollback RESET search_path;


--changeset aleh.rudzko:2020-10-19-SWS-21809-game-group-filter
--comment Add filter field to game group
SET search_path = swmanagement;
ALTER TABLE game_groups ADD COLUMN filter_eur JSONB;
COMMENT ON COLUMN game_groups.filter_eur IS 'Filter for game group limits in EUR';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE game_groups DROP COLUMN filter_eur;
--rollback RESET search_path;

--changeset aleh.rudzko:2020-10-19-SWS-21809-game-group-filter-refactoring
--comment Remove filter field from game group & create new table - game group filter

SET search_path = swmanagement;
ALTER TABLE game_groups DROP COLUMN filter_eur;

CREATE TABLE IF NOT EXISTS game_group_filters (
    id SERIAL PRIMARY KEY NOT NULL,
    group_id INTEGER not null,

    win_capping NUMERIC,
    max_total_bet NUMERIC,
    def_total_bet NUMERIC,
    min_total_bet NUMERIC,
    games JSONB,
  	currencies JSONB,

    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW(),

    FOREIGN KEY (group_id) REFERENCES game_groups (id) ON DELETE CASCADE
);

CREATE INDEX idx_game_group_filters_group_id ON game_group_filters (group_id);

COMMENT ON TABLE game_group_filters IS 'Game group filters';
COMMENT ON COLUMN game_group_filters.id IS 'Game group filter ID';
COMMENT ON COLUMN game_group_filters.group_id IS 'Game group foreign key';
COMMENT ON COLUMN game_group_filters.win_capping IS 'Filter for win capping';
COMMENT ON COLUMN game_group_filters.max_total_bet IS 'Filter for max total bet';
COMMENT ON COLUMN game_group_filters.min_total_bet IS 'Filter for min total bet';
COMMENT ON COLUMN game_group_filters.def_total_bet IS 'Filter for default total bet';
COMMENT ON COLUMN game_group_filters.games IS 'List of games which should be filtered';
COMMENT ON COLUMN game_group_filters.currencies IS 'List of currencies which should be filtered';
COMMENT ON COLUMN game_group_filters.created_at IS 'CreatedAt timestamp';
COMMENT ON COLUMN game_group_filters.updated_at IS 'UpdatedAt timestamp';

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE game_groups ADD COLUMN filter_eur JSONB;
--rollback COMMENT ON COLUMN game_groups.filter_eur IS 'Filter for game group limits in EUR';
--rollback DROP TABLE game_group_filters;
--rollback RESET search_path;

--changeset aleksey.stepanov:2020-10-21-SWS-22399-add-new-field
--comment Add new "enableChat" field into Physical Table
SET search_path = swmanagement;
ALTER TABLE pht_physical_tables ADD COLUMN is_enable_chat BOOLEAN DEFAULT false;
COMMENT ON COLUMN pht_physical_tables.is_enable_chat IS 'Is chat enable';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE pht_physical_tables DROP COLUMN is_enable_chat;
--rollback RESET search_path;

--changeset aleh.rudzko:2020-10-29-SWS-22331-add-bet-info-to-jp-win-log
--comment Add new field betAmount to jp win log
SET search_path = swjackpot;
ALTER TABLE jp_win_log ADD COLUMN IF NOT EXISTS bet_amount NUMERIC;
COMMENT ON COLUMN jp_win_log.bet_amount IS 'Bet amount which triggered jp win';
RESET search_path;

--rollback SET search_path = swjackpot;
--rollback ALTER TABLE jp_win_log DROP COLUMN IF EXISTS bet_amount;
--rollback RESET search_path;

--changeset aleh.rudzko:2020-11-02-SWS-22331-add-bet-info-to-remote-jp-win-log
--comment Add new field betAmount to remote jp win log
SET search_path = swjackpot;
ALTER TABLE remote_jp_win_log ADD COLUMN IF NOT EXISTS bet_amount NUMERIC;
COMMENT ON COLUMN remote_jp_win_log.bet_amount IS 'Bet amount which triggered jp win';
RESET search_path;

--rollback SET search_path = swjackpot;
--rollback ALTER TABLE remote_jp_win_log DROP COLUMN IF EXISTS bet_amount;
--rollback RESET search_path;