--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset pavel.shamshurov:2022-03-21-SWS-33514-add-new-physical-table-type runInTransaction:false
--comment Add new physical table type
SET search_path = swmanagement;
ALTER TYPE enum_pht_provider_game_codes_game_type ADD VALUE IF NOT EXISTS 'andarBahar';
RESET search_path;
--rollback SELECT NOW();

--changeset georgiy.kovrey:2022-03-23-MICGMG-251-wallet-adapter-performance runInTransaction:false
SET search_path = swadaptermgm;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_micgaming_rounds_sw_transaction_id ON micgaming_rounds USING btree (sw_transaction_id);
RESET search_path;
--rollback SET search_path = swadaptermgm;
--rollback DROP INDEX idx_micgaming_rounds_sw_transaction_id;
--rollback RESET search_path;