--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset pavel.shamshurov:2021-12-03-SWS-31553-create-table-to-store-entity-settings
--comment Create table to store entity settings
SET search_path TO swmanagement;
CREATE TABLE IF NOT EXISTS entity_settings
(
  entity_path text PRIMARY KEY,
  settings jsonb NOT NULL,
  inherited boolean NOT NULL DEFAULT true,
  created_at timestamp without time zone DEFAULT now() NOT NULL,
  updated_at timestamp without time zone NOT NULL
);
COMMENT ON TABLE entity_settings IS 'Entity settings transferred from Redis';
COMMENT ON COLUMN entity_settings.entity_path IS 'Entity path';
COMMENT ON COLUMN entity_settings.settings IS 'Entity settings';
COMMENT ON COLUMN entity_settings.inherited IS 'Flag to define whether settings are inherited from parent or not';
COMMENT ON COLUMN entity_settings.created_at IS 'Timestamp when settings were transferred';
COMMENT ON COLUMN entity_settings.updated_at IS 'Timestamp when settings were updated';

RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback DROP TABLE IF EXISTS entity_settings;
--rollback RESET search_path;
