--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset dmitriy.palaznik:2025-01-30-SWS-47887-add-bo-permissions-for-new-analytics-freebets-bi-reports
--comment Create new permission for the Analytics and Freebets BI reports
SET search_path = swmanagement;
UPDATE roles SET permissions = permissions - 'bi:report:analytics' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:analytics' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'bi:report:freebets' WHERE id = 1;
UPDATE roles SET permissions = permissions - 'keyentity:bi:report:freebets' WHERE id = 1;
UPDATE roles SET permissions = permissions || '["bi:report:analytics", "keyentity:bi:report:analytics", "bi:report:freebets", "keyentity:bi:report:freebets"]'::jsonb WHERE id = 1;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:analytics' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:analytics' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'bi:report:freebets' WHERE id = 1;
--rollback UPDATE roles SET permissions = permissions - 'keyentity:bi:report:freebets' WHERE id = 1;
--rollback RESET search_path;
