--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset emanuel-alin.raileanu:2025-05-12-SWS-45605-index-round_id
--comment Index the round_id column of the game_contexts table
CREATE INDEX IF NOT EXISTS idx_game_contexts_round_id ON swgameserver.game_contexts USING btree (round_id);
CREATE INDEX IF NOT EXISTS jp_wallet_operation_log_int4_idx ON swjackpot.jp_wallet_operation_log (((params->>'brandId')::INT));
CREATE INDEX IF NOT EXISTS idx_wallet_operation_log_brand_id ON swmanagement.wallet_operation_log ((regexp_replace((data->0->>'walletKey'), 'player:([0-9]+):.*', '\1')));
CREATE INDEX IF NOT EXISTS idx_wallet_free_bet_brand_id ON swmanagement.wallet_free_bet(brand_id);

--rollback DROP INDEX IF EXISTS swgameserver.idx_game_contexts_round_id;
--rollback DROP INDEX IF EXISTS swjackpot.jp_wallet_operation_log_int4_idx;
--rollback DROP INDEX IF EXISTS swmanagement.idx_wallet_operation_log_brand_id;
--rollback DROP INDEX IF EXISTS swmanagement.idx_wallet_free_bet_brand_id;
