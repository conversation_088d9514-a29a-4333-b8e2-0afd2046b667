--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset emanuel-alin.railean:2022-05-23_SWS-34121-add-ext_session_id-column
--comment Add the ext_session_id column to the sessions_history table
SET search_path = swmanagement;
ALTER TABLE sessions_history ADD COLUMN ext_session_id VARCHAR(255);
COMMENT ON COLUMN sessions_history.ext_session_id IS 'Operator''s session id';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE sessions_history DROP COLUMN ext_session_id;
--rollback RESET search_path;

--changeset mikhail.ivanov:2022-05-23_SWS-34258_return-free-bet-coin-value-within-get-entities-history
--comment Add free_bet_coin column in spins_history table
SET search_path = swmanagement;
ALTER TABLE spins_history ADD IF NOT EXISTS free_bet_coin numeric;
COMMENT ON COLUMN spins_history.free_bet_coin IS 'Information about real coin bet value';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE spins_history DROP IF EXISTS free_bet_coin;
--rollback RESET search_path;

--changeset vladimir.minakov:2022-06-03_SWS-33829-bo-api-ip-whitelist-add-link-to-jira-ticket
--comment Add initiator_issue_id column in audits table
SET search_path = swmanagement;
ALTER TABLE audits ADD IF NOT EXISTS initiator_issue_id varchar(255);
COMMENT ON COLUMN audits.initiator_issue_id IS 'Initiator Jira issue';
ALTER TABLE audits ALTER COLUMN initiator_issue_id SET STORAGE EXTERNAL;
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE audits DROP IF EXISTS initiator_issue_id;
--rollback RESET search_path;
