--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement, public;
--example: CREATE TABLE tmp(id int);
--example: --rollback DROP TABLE tmp;


--changeset valdis.akmens:2020-10-07-SWS-XXXX-start-release-4.45.0
--comment label for 4.45.0
select now();
--rollback select now();

--changeset aleh.rudzko:SWS-22060-remove-game_id-not-null-archive-cluster
--comment Need to remove constraint by game_id from wallet win bet table

SET search_path = swmanagement;
ALTER TABLE wallet_win_bet ALTER COLUMN game_id DROP NOT NULL;
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE wallet_win_bet ALTER COLUMN game_id SET NOT NULL;
--rollback RESET search_path;

--changeset sergey.malkov:2020-10-09-SWB365-252-Trx-unloader:-add-additional-fields-round_bets-round_wins-ggrl_calculation
--comment Add additional fields to wallet_win_bet table for other way of ggr report calculation
SET search_path = swmanagement;
CREATE TYPE enum_wallet_win_bet_ggr_calculation AS ENUM ('round', 'wallet');
ALTER TABLE wallet_win_bet ADD COLUMN ggr_calculation enum_wallet_win_bet_ggr_calculation;
COMMENT ON COLUMN wallet_win_bet.ggr_calculation IS 'Type of ggr calculation';
ALTER TABLE wallet_win_bet ADD COLUMN round_wins numeric;
COMMENT ON COLUMN wallet_win_bet.round_wins IS 'Total win of round (excluding jackpot wins)';
ALTER TABLE wallet_win_bet ADD COLUMN round_bets numeric;
COMMENT ON COLUMN wallet_win_bet.round_bets IS 'Total bet of round';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE wallet_win_bet DROP COLUMN ggr_calculation;
--rollback ALTER TABLE wallet_win_bet DROP COLUMN round_wins;
--rollback ALTER TABLE wallet_win_bet DROP COLUMN round_bets;
--rollback DROP TYPE IF EXISTS enum_wallet_win_bet_ggr_calculation;
--rollback RESET search_path;
