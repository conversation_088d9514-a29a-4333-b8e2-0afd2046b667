--liquibase formatted sql

--changeset vladimir.minakov:2023-07-20_SWS-41085-analyse-how-we-calculate-player_country-for-session
--comment Add 'operator_player_country' fields for 'sessions_history'
SET search_path TO swmanagement;
ALTER TABLE sessions_history ADD COLUMN operator_player_country VARCHAR(6);
COMMENT ON COLUMN sessions_history.operator_player_country IS 'Player country, provided by operator or IP';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_player_country;
--rollback RESET search_path;
