--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset timur.luchkin:2022-06-14-DEVOPS-19694-hist endDelimiter:# stripComments:false
--comment Fix SecondarySnapshot issue
SET search_path = swsystem;

   CREATE OR REPLACE FUNCTION swsystem.get_sw_hashid (IN p_hash_project VARCHAR, OUT po_hash_salt TEXT, OUT po_hash_length INTEGER)
   AS
   $BODY$
   BEGIN
      SELECT hash_salt, hash_length FROM swsystem.sw_hashid_secret WHERE hash_project = p_hash_project INTO po_hash_salt, po_hash_length;
   END;
   $BODY$
   LANGUAGE plpgsql STABLE PARALLEL UNSAFE SECURITY DEFINER;

   CREATE OR REPLACE FUNCTION public.sw_get_public_id (IN internal_id BIGINT, OUT public_id TEXT)
   AS
   $BODY$
   BEGIN
      SELECT public.id_encode(internal_id, po_hash_salt, po_hash_length)
      FROM   swsystem.get_sw_hashid ('sw-falcon') INTO public_id;
   END;
   $BODY$
   LANGUAGE plpgsql STABLE PARALLEL UNSAFE SECURITY DEFINER RETURNS NULL ON NULL INPUT;

   CREATE OR REPLACE FUNCTION public.sw_get_internal_id (IN public_id TEXT, OUT internal_id BIGINT)
   AS
   $BODY$
   BEGIN
      SELECT (public.id_decode(public_id, po_hash_salt, po_hash_length))[1]
      FROM   swsystem.get_sw_hashid ('sw-falcon') INTO internal_id;
   END;
   $BODY$
   LANGUAGE plpgsql STABLE PARALLEL UNSAFE SECURITY DEFINER RETURNS NULL ON NULL INPUT;

RESET search_path;
--rollback SELECT now();
