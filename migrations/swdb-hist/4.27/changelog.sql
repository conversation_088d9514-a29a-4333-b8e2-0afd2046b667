--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement, public;
--example: CREATE TABLE tmp(id int);
--example: --rollback DROP TABLE tmp;


--changeset valdis.akmens:2020-09-09-SWS-XXXX-start-release-4.27.0
--comment label for 4.27.0
select now();
--rollback select now();


--changeset valdis.akmens:2020-09-09-SWS-XXXX-release-hist_cluster-4.27 endDelimiter:# stripComments:false
--comment Inital Release for Historical Cluster

-- Schemas
CREATE SCHEMA IF NOT EXISTS hist_cluster;
ALTER SCHEMA hist_cluster OWNER TO postgres;
CREATE SCHEMA IF NOT EXISTS pglogical;
ALTER SCHEMA pglogical OWNER TO postgres;
CREATE SCHEMA IF NOT EXISTS swadaptergos;
ALTER SCHEMA swadaptergos OWNER TO swadaptergos;
CREATE SCHEMA IF NOT EXISTS swadapterqs;
ALTER SCHEMA swadapterqs OWNER TO swadapterqs;
CREATE SCHEMA IF NOT EXISTS swgameserver;
ALTER SCHEMA swgameserver OWNER TO swgameserver;
CREATE SCHEMA IF NOT EXISTS swjackpot;
ALTER SCHEMA swjackpot OWNER TO swjackpot;
CREATE SCHEMA IF NOT EXISTS swmanagement;
ALTER SCHEMA swmanagement OWNER TO swmanagement;
CREATE SCHEMA IF NOT EXISTS swsrt;
ALTER SCHEMA swsrt OWNER TO swsrt;

-- Extensions
CREATE EXTENSION IF NOT EXISTS plpgsql WITH SCHEMA pg_catalog;
COMMENT ON EXTENSION plpgsql IS 'PL/pgSQL procedural language';
CREATE EXTENSION IF NOT EXISTS dblink WITH SCHEMA public;
COMMENT ON EXTENSION dblink IS 'connect to other PostgreSQL databases from within a database';
CREATE EXTENSION IF NOT EXISTS pg_hashids WITH SCHEMA public;
COMMENT ON EXTENSION pg_hashids IS 'pg_hashids';
CREATE EXTENSION IF NOT EXISTS pg_pathman WITH SCHEMA public;
COMMENT ON EXTENSION pg_pathman IS 'Partitioning tool for PostgreSQL';
CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA public;
COMMENT ON EXTENSION pg_stat_statements IS 'track execution statistics of all SQL statements executed';
CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA public;
COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';
CREATE EXTENSION IF NOT EXISTS postgres_fdw WITH SCHEMA public;
COMMENT ON EXTENSION postgres_fdw IS 'foreign-data wrapper for remote PostgreSQL servers';
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';

CREATE TYPE swmanagement.enum_audits_initiator_type AS ENUM (
    'user',
    'player',
    'system'
);
ALTER TYPE swmanagement.enum_audits_initiator_type OWNER TO swmanagement;

CREATE TYPE swmanagement.enum_recovery_type AS ENUM (
    'force-finish',
    'revert',
    'finalize'
);
ALTER TYPE swmanagement.enum_recovery_type OWNER TO swmanagement;

CREATE TYPE swmanagement.enum_sessions_history_interruption_reason AS ENUM (
    'session_expired',
    'pending_payment',
    'pending_jackpot',
    'broken_game_context'
);
ALTER TYPE swmanagement.enum_sessions_history_interruption_reason OWNER TO swmanagement;

CREATE TYPE swmanagement.enum_wallet_entity_payment_log_transfer_type AS ENUM (
    'ent-ent',
    'ent-plr',
    'plr-ent',
    'ent-plr_ext',
    'plr-ent_ext',
    'free-rebet',
    'bns-redeem',
    'free-bet'
);
ALTER TYPE swmanagement.enum_wallet_entity_payment_log_transfer_type OWNER TO swmanagement;

CREATE TYPE swmanagement.enum_wallet_win_bet_transaction_type AS ENUM (
    'free_bet',
    'jackpot',
    'free_rebet',
    'redeem',
    'transfer',
    'bonus'
);
ALTER TYPE swmanagement.enum_wallet_win_bet_transaction_type OWNER TO swmanagement;


-- Tables
CREATE TABLE hist_cluster.hist_jobs (
    job_id bigint NOT NULL,
    action text NOT NULL,
    schema_name character varying(64) NOT NULL,
    table_name character varying(64) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    started_at timestamp without time zone,
    completed_at timestamp without time zone,
    CONSTRAINT hist_jobs_action_check CHECK ((action = ANY (ARRAY['analyze'::text, 'reindex'::text])))
);


ALTER TABLE hist_cluster.hist_jobs OWNER TO swsystem;

CREATE SEQUENCE hist_cluster.hist_jobs_job_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE hist_cluster.hist_jobs_job_id_seq OWNER TO swsystem;

ALTER SEQUENCE hist_cluster.hist_jobs_job_id_seq OWNED BY hist_cluster.hist_jobs.job_id;

CREATE TABLE public.pathman_allowed_tspaces (
    ts_name text NOT NULL
);

ALTER TABLE public.pathman_allowed_tspaces OWNER TO postgres;

CREATE VIEW public.tham_locks AS
 SELECT ( SELECT pg_user.usename
           FROM pg_user
          WHERE (pg_user.usesysid = cl.relowner)) AS obj_owner,
    cl.relname AS obj_name,
        CASE cl.relkind
            WHEN 'r'::"char" THEN 'table'::character varying
            WHEN 'i'::"char" THEN 'index'::character varying
            WHEN 'v'::"char" THEN 'view'::character varying
            ELSE (cl.relkind)::character varying
        END AS obj_type,
    l.pid AS procpid,
    l.locktype,
    l.mode,
    l.granted,
    l.page,
    l.tuple,
    l.transactionid,
    l.virtualtransaction
   FROM (pg_locks l
     LEFT JOIN pg_class cl ON ((cl.oid = l.relation)))
  ORDER BY l.pid,
        CASE cl.relkind
            WHEN 'r'::"char" THEN (1)::smallint
            WHEN 'i'::"char" THEN (3)::smallint
            WHEN 'v'::"char" THEN (2)::smallint
            ELSE (4)::smallint
        END;


ALTER TABLE public.tham_locks OWNER TO postgres;


CREATE VIEW public.tham_not_idle AS
 SELECT sa.pid,
    sa.usename,
    (clock_timestamp() - sa.query_start) AS time_passed,
    sa.state,
    sa.query,
    (sa.wait_event_type IS NOT NULL) AS is_waiting,
    sa.wait_event_type,
    sa.wait_event,
    sa.query_start,
    sa.backend_start,
    sa.client_addr,
    sa.client_port,
    sa.datname,
    ( SELECT count(*) AS count
           FROM pg_locks l
          WHERE ((l.pid = sa.pid) AND l.granted)) AS held_locks,
    ( SELECT count(*) AS count
           FROM pg_locks l
          WHERE ((l.pid = sa.pid) AND (NOT l.granted))) AS wait_locks,
    sa.application_name
   FROM pg_stat_activity sa
  WHERE ((sa.state <> 'idle'::text) AND ((sa.usename <> 'repmgr'::name) OR (sa.usename IS NULL)) AND (sa.query !~* 'tham_not_idle'::text));


ALTER TABLE public.tham_not_idle OWNER TO postgres;

CREATE VIEW public.tham_replica AS
 SELECT pg_stat_replication.pid,
    pg_stat_replication.application_name,
    pg_stat_replication.backend_start,
    pg_stat_replication.state,
    pg_stat_replication.sync_state,
    pg_size_pretty(pg_wal_lsn_diff(pg_stat_replication.sent_lsn, pg_stat_replication.flush_lsn)) AS receiving_lag,
    pg_size_pretty(pg_wal_lsn_diff(pg_current_wal_lsn(), pg_stat_replication.sent_lsn)) AS sending_lag,
    pg_stat_replication.sent_lsn,
    pg_stat_replication.write_lsn,
    pg_stat_replication.flush_lsn,
    pg_current_wal_lsn() AS local_write_lsn,
    pg_walfile_name(pg_current_wal_lsn()) AS local_write_wal
   FROM pg_stat_replication;


ALTER TABLE public.tham_replica OWNER TO postgres;


CREATE VIEW public.tham_sizes AS
 SELECT
        CASE
            WHEN ((sub3.object_type)::text = '1.Table'::text) THEN (((('VACUUM FREEZE VERBOSE '::text || (sub3.object_owner)::text) || '."'::text) || (sub3.object_name)::text) || '";'::text)
            WHEN ((sub3.object_type)::text = '2.Index'::text) THEN (((('REINDEX INDEX '::text || (sub3.object_owner)::text) || '."'::text) || (sub3.object_name)::text) || '";'::text)
            WHEN ((sub3.object_type)::text ~~ 'TOTALS%'::text) THEN upper((sub3.object_owner)::text)
            ELSE (((COALESCE(sub3.object_owner, 'N/A'::name))::text || '.'::text) || (COALESCE(sub3.object_name, 'N/A'::name))::text)
        END AS full_name,
    sub3.object_type,
    sub3.rows_count,
    sub3.size_kb,
    sub3.tuple_size_kb,
    sub3.pages
   FROM ( SELECT sub2.object_owner,
            sub2.object_name,
            sub2.object_type,
            sub2.rows_count,
            sub2.size_kb,
            sub2.tuple_size_kb,
            sub2.pages
           FROM ( SELECT sub1.object_owner,
                    sub1.object_name,
                    sub1.object_type,
                    sub1.rows_count,
                    sub1.size_kb,
                    sub1.tuple_size_kb,
                    sub1.pages
                   FROM ( SELECT ns.nspname AS object_owner,
                            cl.relname AS object_name,
                                CASE
                                    WHEN (cl.relkind = 'r'::"char") THEN '1.Table'::character varying
                                    WHEN (cl.relkind = 'i'::"char") THEN '2.Index'::character varying
                                    WHEN (cl.relkind = 'v'::"char") THEN '5.View'::character varying
                                    WHEN (cl.relkind = 'S'::"char") THEN '4.Sequence'::character varying
                                    WHEN (cl.relkind = 't'::"char") THEN '3.Toast'::character varying
                                    ELSE NULL::character varying
                                END AS object_type,
                            (cl.reltuples)::bigint AS rows_count,
                            ((cl.relpages)::bigint * 8) AS size_kb,
                                CASE
                                    WHEN (cl.reltuples = (0)::real) THEN (0)::double precision
                                    ELSE ((((cl.relpages)::bigint * (8)::bigint))::double precision / cl.reltuples)
                                END AS tuple_size_kb,
                            (cl.relpages)::bigint AS pages
                           FROM (pg_class cl
                             JOIN pg_namespace ns ON ((cl.relnamespace = ns.oid)))
                          WHERE (((cl.relkind = 'r'::"char") OR (cl.relkind = 'i'::"char") OR (cl.relkind = 't'::"char")) AND (cl.reltuples > (9)::double precision) AND ((cl.relpages)::bigint > 9) AND (NOT (cl.relname ~~ '%_lut'::text)))) sub1
                UNION ALL
                 SELECT sub1.object_owner,
                    NULL::name AS object_name,
                    ((('TOTALS ('::text || (sub1.object_owner)::text) || '):'::text))::character varying AS object_type,
                    sum(sub1.rows_count) AS rows_count,
                    sum(sub1.size_kb) AS size_kb,
                    sum(sub1.tuple_size_kb) AS tuple_size_kb,
                    sum(sub1.pages) AS pages
                   FROM ( SELECT ns.nspname AS object_owner,
                            cl.relname AS object_name,
                                CASE
                                    WHEN (cl.relkind = 'r'::"char") THEN '1.Table'::character varying
                                    WHEN (cl.relkind = 'i'::"char") THEN '2.Index'::character varying
                                    WHEN (cl.relkind = 'v'::"char") THEN '5.View'::character varying
                                    WHEN (cl.relkind = 'S'::"char") THEN '4.Sequence'::character varying
                                    WHEN (cl.relkind = 't'::"char") THEN '3.Toast'::character varying
                                    ELSE NULL::character varying
                                END AS object_type,
                            (cl.reltuples)::bigint AS rows_count,
                            ((cl.relpages)::bigint * 8) AS size_kb,
                                CASE
                                    WHEN (cl.reltuples = (0)::real) THEN (0)::double precision
                                    ELSE ((((cl.relpages)::bigint * 8))::double precision / cl.reltuples)
                                END AS tuple_size_kb,
                            (cl.relpages)::bigint AS pages
                           FROM (pg_class cl
                             JOIN pg_namespace ns ON ((cl.relnamespace = ns.oid)))
                          WHERE (((cl.relkind = 'r'::"char") OR (cl.relkind = 'i'::"char") OR (cl.relkind = 't'::"char")) AND (cl.reltuples > (9)::double precision) AND ((cl.relpages)::bigint > 9) AND (NOT (cl.relname ~~ '%_lut'::text)))) sub1
                  GROUP BY sub1.object_owner
          ORDER BY 3, 6 DESC) sub2) sub3;


ALTER TABLE public.tham_sizes OWNER TO postgres;

CREATE SEQUENCE swjackpot.jp_contribution_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE swjackpot.jp_contribution_id_seq OWNER TO swjackpot;

CREATE TABLE swjackpot.jp_contribution_log (
    id bigint DEFAULT nextval('swjackpot.jp_contribution_id_seq'::regclass) NOT NULL,
    trx_id character(28) NOT NULL,
    trx_date timestamp without time zone NOT NULL,
    external_id character varying(255),
    jackpot_id character varying(255) NOT NULL,
    pool character varying(255) NOT NULL,
    currency character(3) NOT NULL,
    seed numeric DEFAULT 0 NOT NULL,
    progressive numeric DEFAULT 0 NOT NULL,
    brand_id integer NOT NULL,
    game_id character varying(255) NOT NULL,
    player_code character varying(255) NOT NULL,
    player_currency character(3) NOT NULL,
    contribution_amount numeric NOT NULL,
    currency_rate numeric NOT NULL,
    game_data jsonb NOT NULL,
    inserted_at timestamp without time zone DEFAULT now(),
    game_code character varying(255),
    region character varying(255),
    player_seed numeric,
    player_progressive numeric,
    round_id bigint,
    remote_trx_id character(28),
    remote_trx_region character varying(10),
    total_seed numeric,
    total_progressive numeric
)
WITH (fillfactor='100');
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN external_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN jackpot_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN pool SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN player_currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN game_data SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN region SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN remote_trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN remote_trx_region SET STORAGE EXTERNAL;


ALTER TABLE swjackpot.jp_contribution_log OWNER TO swjackpot;
COMMENT ON TABLE swjackpot.jp_contribution_log IS 'List of contributions on jackpot pools';
COMMENT ON COLUMN swjackpot.jp_contribution_log.trx_id IS 'Transaction id';
COMMENT ON COLUMN swjackpot.jp_contribution_log.trx_date IS 'Transaction timestamp';
COMMENT ON COLUMN swjackpot.jp_contribution_log.external_id IS 'Transaction external id, e.g. game payment id';
COMMENT ON COLUMN swjackpot.jp_contribution_log.jackpot_id IS 'Jackpot id';
COMMENT ON COLUMN swjackpot.jp_contribution_log.pool IS 'Jackpot pool';
COMMENT ON COLUMN swjackpot.jp_contribution_log.currency IS 'Jackpot currency code';
COMMENT ON COLUMN swjackpot.jp_contribution_log.seed IS 'Jackpot pool seed change amount';
COMMENT ON COLUMN swjackpot.jp_contribution_log.progressive IS 'Jackpot pool progressive change amount';
COMMENT ON COLUMN swjackpot.jp_contribution_log.brand_id IS 'Brand whose player did contribution';
COMMENT ON COLUMN swjackpot.jp_contribution_log.game_id IS 'Game which contributed';
COMMENT ON COLUMN swjackpot.jp_contribution_log.player_code IS 'Player who contributed';
COMMENT ON COLUMN swjackpot.jp_contribution_log.player_currency IS 'Player currency';
COMMENT ON COLUMN swjackpot.jp_contribution_log.contribution_amount IS 'Amount of contribution in player currency';
COMMENT ON COLUMN swjackpot.jp_contribution_log.currency_rate IS 'Rate from player currency to jackpot currency';
COMMENT ON COLUMN swjackpot.jp_contribution_log.game_data IS 'Additional data for contribution';
COMMENT ON COLUMN swjackpot.jp_contribution_log.game_code IS 'Game code';
COMMENT ON COLUMN swjackpot.jp_contribution_log.region IS 'Identifier of system region, e.g. europe or asia';
COMMENT ON COLUMN swjackpot.jp_contribution_log.player_seed IS 'Seed in player currency';
COMMENT ON COLUMN swjackpot.jp_contribution_log.player_progressive IS 'Progressive in player currency';
COMMENT ON COLUMN swjackpot.jp_contribution_log.round_id IS 'Round id where jackpot is contributed';
COMMENT ON COLUMN swjackpot.jp_contribution_log.remote_trx_id IS 'Id of transaction executed on remote jackpot server';
COMMENT ON COLUMN swjackpot.jp_contribution_log.remote_trx_region IS 'Region code of remote jackpot server where transaction is executed';
COMMENT ON COLUMN swjackpot.jp_contribution_log.total_seed IS 'Jackpot seed amount before win';
COMMENT ON COLUMN swjackpot.jp_contribution_log.total_progressive IS 'Jackpot progressive amount before win';


CREATE TABLE swjackpot.jp_wallet_operation_log (
    id bigint NOT NULL,
    operation_id integer NOT NULL,
    operation_name character varying(255),
    public_id character(28) NOT NULL,
    external_trx_id character varying(255),
    is_external boolean NOT NULL,
    game_id character varying(255),
    ts timestamp without time zone NOT NULL,
    version integer NOT NULL,
    data jsonb NOT NULL,
    params jsonb,
    inserted_at timestamp without time zone DEFAULT now(),
    committed_at timestamp without time zone
)
WITH (fillfactor='100');
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN operation_name SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN public_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN external_trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN data SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN params SET STORAGE EXTERNAL;


ALTER TABLE swjackpot.jp_wallet_operation_log OWNER TO swjackpot;

COMMENT ON COLUMN swjackpot.jp_wallet_operation_log.committed_at IS 'Timestamp when operation was committed';

CREATE SEQUENCE swjackpot.remote_jp_contribution_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE swjackpot.remote_jp_contribution_log_id_seq OWNER TO swjackpot;


CREATE TABLE swjackpot.remote_jp_contribution_log (
    id bigint DEFAULT nextval('swjackpot.remote_jp_contribution_log_id_seq'::regclass) NOT NULL,
    trx_id character(28) NOT NULL,
    trx_date timestamp without time zone NOT NULL,
    external_id character varying(255),
    remote_trx_id character(28),
    remote_trx_region character varying(10),
    round_id bigint NOT NULL,
    jackpot_id character varying(255) NOT NULL,
    pool character varying(255) NOT NULL,
    currency character(3) NOT NULL,
    seed numeric DEFAULT 0 NOT NULL,
    progressive numeric DEFAULT 0 NOT NULL,
    brand_id integer NOT NULL,
    region character varying(255),
    game_id character varying(255) NOT NULL,
    game_code character varying(255) NOT NULL,
    player_code character varying(255) NOT NULL,
    player_currency character(3) NOT NULL,
    contribution_amount numeric NOT NULL,
    currency_rate numeric NOT NULL,
    game_data jsonb NOT NULL,
    player_seed numeric,
    player_progressive numeric,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    total_seed numeric,
    total_progressive numeric
);
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN external_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN remote_trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN remote_trx_region SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN jackpot_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN pool SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN region SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN player_currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN game_data SET STORAGE EXTERNAL;


ALTER TABLE swjackpot.remote_jp_contribution_log OWNER TO swjackpot;


COMMENT ON TABLE swjackpot.remote_jp_contribution_log IS 'List of contributions on jackpot pools received from remote jackpot server';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.trx_id IS 'Transaction id';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.trx_date IS 'Transaction timestamp';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.external_id IS 'Transaction external id, e.g. game payment id';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.remote_trx_id IS 'Id of transaction executed on remote jackpot server';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.remote_trx_region IS 'Region code of remote jackpot server where transaction is executed';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.round_id IS 'Round id where jackpot is contributed';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.jackpot_id IS 'Jackpot id';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.pool IS 'Jackpot pool';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.currency IS 'Jackpot currency code';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.seed IS 'Jackpot pool seed change amount';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.progressive IS 'Jackpot pool progressive change amount';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.brand_id IS 'Brand whose player did contribution';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.region IS 'Identifier of system region, e.g. europe or asia';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.game_id IS 'Game which contributed';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.game_code IS 'Game code';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.player_code IS 'Player who contributed';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.player_currency IS 'Player currency';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.contribution_amount IS 'Amount of contribution in player currency';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.currency_rate IS 'Rate from player currency to jackpot currency';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.game_data IS 'Additional data for contribution';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.player_seed IS 'Seed in player currency';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.player_progressive IS 'Progressive in player currency';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.inserted_at IS 'When transaction is inserted';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.total_seed IS 'Jackpot seed amount before win';
COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.total_progressive IS 'Jackpot progressive amount before win';


CREATE TABLE swmanagement.audits (
    audit_id integer NOT NULL,
    entity_id integer NOT NULL,
    ts timestamp without time zone NOT NULL,
    audit_type smallint NOT NULL,
    history jsonb,
    initiator_type swmanagement.enum_audits_initiator_type NOT NULL,
    initiator_name character varying(255) NOT NULL,
    ip inet NOT NULL,
    user_agent character varying(2048) NOT NULL,
    initiator_service_name character varying(255)
)
WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.audits ALTER COLUMN history SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.audits ALTER COLUMN initiator_name SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.audits ALTER COLUMN user_agent SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.audits ALTER COLUMN initiator_service_name SET STORAGE EXTERNAL;


ALTER TABLE swmanagement.audits OWNER TO swmanagement;


CREATE SEQUENCE swmanagement.audits_audit_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE swmanagement.audits_audit_id_seq OWNER TO swmanagement;

ALTER SEQUENCE swmanagement.audits_audit_id_seq OWNED BY swmanagement.audits.audit_id;

CREATE TABLE swmanagement.currency_rates (
    currency_code character(3) NOT NULL,
    rate_date date NOT NULL,
    rate numeric NOT NULL,
    ts integer NOT NULL
);

ALTER TABLE swmanagement.currency_rates OWNER TO swmanagement;

CREATE TABLE swmanagement.games (
    id integer NOT NULL,
    code character varying(255),
    type character varying NOT NULL,
    url character varying(1024),
    provider_id integer,
    provider_game_code character varying(255) NOT NULL,
    status character varying NOT NULL,
    default_info jsonb,
    info jsonb,
    limits jsonb,
    version integer,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    comment character varying(255),
    title character varying(255),
    features jsonb,
    history_render_type integer DEFAULT 0 NOT NULL,
    history_url character varying(255),
    limits_group character varying(255),
    countries jsonb,
    schema_definition_id integer,
    total_bet_multiplier numeric,
    client_features jsonb
);

ALTER TABLE swmanagement.games OWNER TO swmanagement;

COMMENT ON COLUMN swmanagement.games.comment IS 'Game comment';
COMMENT ON COLUMN swmanagement.games.features IS 'Game specific features';
COMMENT ON COLUMN swmanagement.games.history_render_type IS 'Game history render type';
COMMENT ON COLUMN swmanagement.games.history_url IS 'Priority history url of game';
COMMENT ON COLUMN swmanagement.games.limits_group IS 'The criteria for grouping games with equal limits';
COMMENT ON COLUMN swmanagement.games.countries IS 'list of allowed countries for game. If empty/null - no restrictions';
COMMENT ON COLUMN swmanagement.games.schema_definition_id IS 'Schema definitions reference';
COMMENT ON COLUMN swmanagement.games.total_bet_multiplier IS 'Total bet multiplier';
COMMENT ON COLUMN swmanagement.games.client_features IS 'Stores client features (turbo, fastPlay) which will be used when starting the game';


CREATE TABLE swmanagement.payments (
    id bigint NOT NULL,
    trx_id character(28) NOT NULL,
    ext_trx_id character varying(255),
    brand_id character varying(255) NOT NULL,
    player_code character varying(255) NOT NULL,
    order_id character varying(255),
    order_date timestamp without time zone NOT NULL,
    order_info jsonb,
    order_status character varying(255) NOT NULL,
    order_type character varying(255) NOT NULL,
    payment_method_code character varying(255),
    currency_code character(3) NOT NULL,
    amount numeric NOT NULL,
    start_date timestamp without time zone NOT NULL,
    end_date timestamp without time zone,
    processed_by character varying(255),
    marks jsonb,
    is_test boolean DEFAULT false NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    player_balance_after numeric
);

ALTER TABLE swmanagement.payments OWNER TO swmanagement;

COMMENT ON COLUMN swmanagement.payments.player_balance_after IS 'Player balance after payment';

CREATE SEQUENCE swmanagement.payments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE swmanagement.payments_id_seq OWNER TO swmanagement;

ALTER SEQUENCE swmanagement.payments_id_seq OWNED BY swmanagement.payments.id;

CREATE TABLE swmanagement.rounds_finished (
    id bigint NOT NULL,
    brand_id integer NOT NULL,
    player_code character varying(255) NOT NULL,
    game_id character varying(255) NOT NULL,
    game_code character varying(255) NOT NULL,
    device_id character varying(255) NOT NULL,
    currency character(3) NOT NULL,
    total_win numeric DEFAULT 0 NOT NULL,
    total_bet numeric DEFAULT 0 NOT NULL,
    balance_before numeric,
    balance_after numeric,
    total_events numeric DEFAULT 0 NOT NULL,
    started_at timestamp without time zone NOT NULL,
    finished_at timestamp without time zone NOT NULL,
    test boolean DEFAULT false NOT NULL,
    session_id bigint,
    inserted_at timestamp without time zone DEFAULT now(),
    recovery_type swmanagement.enum_recovery_type,
    total_jp_contribution numeric,
    total_jp_win numeric,
    debit numeric,
    credit numeric,
    ctrl integer
);
ALTER TABLE ONLY swmanagement.rounds_finished ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_finished ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_finished ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_finished ALTER COLUMN device_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_finished ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE swmanagement.rounds_finished OWNER TO swmanagement;

COMMENT ON TABLE swmanagement.rounds_finished IS 'Finisched rounds statistics';
COMMENT ON COLUMN swmanagement.rounds_finished.id IS 'Round serial identifier. Generated inside gameserver';
COMMENT ON COLUMN swmanagement.rounds_finished.brand_id IS 'Brand identifier';
COMMENT ON COLUMN swmanagement.rounds_finished.player_code IS 'Player code';
COMMENT ON COLUMN swmanagement.rounds_finished.game_id IS 'Game identifier. Is used to load appropriate game module';
COMMENT ON COLUMN swmanagement.rounds_finished.game_code IS 'Game code in management system';
COMMENT ON COLUMN swmanagement.rounds_finished.device_id IS 'Device from user was playing.';
COMMENT ON COLUMN swmanagement.rounds_finished.currency IS 'Currency code';
COMMENT ON COLUMN swmanagement.rounds_finished.total_win IS 'Total win amount in round';
COMMENT ON COLUMN swmanagement.rounds_finished.total_bet IS 'Total bet amount in round';
COMMENT ON COLUMN swmanagement.rounds_finished.balance_before IS 'Balance at the begining of round';
COMMENT ON COLUMN swmanagement.rounds_finished.balance_after IS 'Balance at the end of round';
COMMENT ON COLUMN swmanagement.rounds_finished.total_events IS 'Total count of player events/actions';
COMMENT ON COLUMN swmanagement.rounds_finished.started_at IS 'Timestamp when round was started';
COMMENT ON COLUMN swmanagement.rounds_finished.finished_at IS 'Timestamp when round was finished';
COMMENT ON COLUMN swmanagement.rounds_finished.test IS 'Is it test game';
COMMENT ON COLUMN swmanagement.rounds_finished.recovery_type IS 'The recovery type if the round was recoveried by operator/support';
COMMENT ON COLUMN swmanagement.rounds_finished.debit IS 'Debit amount (transfer-in amount, etc)';
COMMENT ON COLUMN swmanagement.rounds_finished.credit IS 'Credit amount (redeem bns amount, transfer-out amount, etc)';
COMMENT ON COLUMN swmanagement.rounds_finished.ctrl IS 'Control sum (random) to check logical duplicates';


CREATE TABLE swmanagement.rounds_history (
    id bigint NOT NULL,
    brand_id integer NOT NULL,
    player_code character varying(255) NOT NULL,
    game_id character varying(255) NOT NULL,
    game_code character varying(255) NOT NULL,
    device_id character varying(255) NOT NULL,
    currency character(3) NOT NULL,
    total_win numeric DEFAULT 0 NOT NULL,
    total_bet numeric DEFAULT 0 NOT NULL,
    balance_before numeric,
    balance_after numeric,
    total_events numeric DEFAULT 0 NOT NULL,
    started_at timestamp without time zone NOT NULL,
    finished_at timestamp without time zone,
    test boolean DEFAULT false NOT NULL,
    session_id bigint,
    inserted_at timestamp without time zone DEFAULT now(),
    recovery_type swmanagement.enum_recovery_type,
    total_jp_contribution numeric,
    total_jp_win numeric,
    debit numeric,
    credit numeric,
    ctrl integer
);
ALTER TABLE ONLY swmanagement.rounds_history ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_history ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_history ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_history ALTER COLUMN device_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_history ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE swmanagement.rounds_history OWNER TO swmanagement;

COMMENT ON TABLE swmanagement.rounds_history IS 'Rounds statistics';
COMMENT ON COLUMN swmanagement.rounds_history.id IS 'Round serial identifier. Generated inside gameserver.';
COMMENT ON COLUMN swmanagement.rounds_history.brand_id IS 'Brand identifier';
COMMENT ON COLUMN swmanagement.rounds_history.player_code IS 'Player code';
COMMENT ON COLUMN swmanagement.rounds_history.game_id IS 'Game identifier. Is used to load appropriate game module';
COMMENT ON COLUMN swmanagement.rounds_history.game_code IS 'Game code in management system';
COMMENT ON COLUMN swmanagement.rounds_history.device_id IS 'Device from user was playing.';
COMMENT ON COLUMN swmanagement.rounds_history.currency IS 'Currency code';
COMMENT ON COLUMN swmanagement.rounds_history.total_win IS 'Total win amount in round';
COMMENT ON COLUMN swmanagement.rounds_history.total_bet IS 'Total bet amount in round';
COMMENT ON COLUMN swmanagement.rounds_history.balance_before IS 'Balance at the begining of round';
COMMENT ON COLUMN swmanagement.rounds_history.balance_after IS 'Balance at the end of round';
COMMENT ON COLUMN swmanagement.rounds_history.total_events IS 'Total count of player events/actions';
COMMENT ON COLUMN swmanagement.rounds_history.started_at IS 'Timestamp when round was started';
COMMENT ON COLUMN swmanagement.rounds_history.finished_at IS 'Timestamp when round was finished';
COMMENT ON COLUMN swmanagement.rounds_history.test IS 'Is it test game';
COMMENT ON COLUMN swmanagement.rounds_history.recovery_type IS 'The recovery type if the round was recoveried by operator/support';
COMMENT ON COLUMN swmanagement.rounds_history.total_jp_contribution IS 'Total jackpot contribution per round in player currency';
COMMENT ON COLUMN swmanagement.rounds_history.total_jp_win IS 'Total jackpot win per round in player currency';
COMMENT ON COLUMN swmanagement.rounds_history.debit IS 'Debit amount (transfer-in amount, etc)';
COMMENT ON COLUMN swmanagement.rounds_history.credit IS 'Credit amount (redeem bns amount, transfer-out amount, etc)';
COMMENT ON COLUMN swmanagement.rounds_history.ctrl IS 'Control sum (random) to check logical duplicates';

CREATE TABLE swmanagement.sessions_history (
    id bigint NOT NULL,
    brand_id integer NOT NULL,
    player_code character varying(255) NOT NULL,
    game_id character varying(255) NOT NULL,
    game_version character varying(255),
    game_code character varying(255) NOT NULL,
    device_id character varying(255) NOT NULL,
    currency_code character(3) NOT NULL,
    player_country character varying(32),
    player_language character varying(32),
    user_agent_id integer,
    user_agent character varying(2048),
    screen_size character varying(255),
    is_broken boolean DEFAULT false NOT NULL,
    is_test boolean DEFAULT false NOT NULL,
    started_at timestamp without time zone NOT NULL,
    finished_at timestamp without time zone,
    inserted_at timestamp without time zone DEFAULT now(),
    browser character varying(255),
    os character varying(255),
    platform character varying(255),
    played_from_country character(2),
    ip inet,
    browser_version character varying(255),
    ctrl integer,
    interruption_reason swmanagement.enum_sessions_history_interruption_reason,
    referrer character varying(255)
)
WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN game_version SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN device_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN currency_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN player_country SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN player_language SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN user_agent SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN screen_size SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN browser SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN os SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN platform SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN played_from_country SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN browser_version SET STORAGE EXTERNAL;
ALTER TABLE swmanagement.sessions_history OWNER TO swmanagement;

COMMENT ON TABLE swmanagement.sessions_history IS 'Player session info';
COMMENT ON COLUMN swmanagement.sessions_history.id IS 'Session serial identifier. Generated inside gameserver.';
COMMENT ON COLUMN swmanagement.sessions_history.brand_id IS 'Brand identifier';
COMMENT ON COLUMN swmanagement.sessions_history.player_code IS 'Player code';
COMMENT ON COLUMN swmanagement.sessions_history.game_id IS 'Game identifier. Is used to load appropriate game module';
COMMENT ON COLUMN swmanagement.sessions_history.game_version IS 'Game module version';
COMMENT ON COLUMN swmanagement.sessions_history.game_code IS 'Game code in management system';
COMMENT ON COLUMN swmanagement.sessions_history.device_id IS 'Device from which user was playing';
COMMENT ON COLUMN swmanagement.sessions_history.currency_code IS 'Player currency code';
COMMENT ON COLUMN swmanagement.sessions_history.player_country IS 'Player country';
COMMENT ON COLUMN swmanagement.sessions_history.player_language IS 'Player language';
COMMENT ON COLUMN swmanagement.sessions_history.user_agent_id IS 'Reference to user_agents dictionary';
COMMENT ON COLUMN swmanagement.sessions_history.user_agent IS 'Full user agent string. To be used to extract detailed device info';
COMMENT ON COLUMN swmanagement.sessions_history.screen_size IS 'Screen size of player device';
COMMENT ON COLUMN swmanagement.sessions_history.is_broken IS 'If session closed with unfinished round or broken payment';
COMMENT ON COLUMN swmanagement.sessions_history.is_test IS 'Is it test game';
COMMENT ON COLUMN swmanagement.sessions_history.started_at IS 'Timestamp when session was started';
COMMENT ON COLUMN swmanagement.sessions_history.finished_at IS 'Timestamp when session was finished';
COMMENT ON COLUMN swmanagement.sessions_history.browser IS 'Game client browser';
COMMENT ON COLUMN swmanagement.sessions_history.os IS 'Game client operating system';
COMMENT ON COLUMN swmanagement.sessions_history.platform IS 'Game client platform';
COMMENT ON COLUMN swmanagement.sessions_history.played_from_country IS 'Country player played from';
COMMENT ON COLUMN swmanagement.sessions_history.ip IS 'IP player played from';
COMMENT ON COLUMN swmanagement.sessions_history.browser_version IS 'Version of the browser';
COMMENT ON COLUMN swmanagement.sessions_history.ctrl IS 'Control sum (random) to check logical duplicates';
COMMENT ON COLUMN swmanagement.sessions_history.interruption_reason IS 'The reason why session has been interrupted';
COMMENT ON COLUMN swmanagement.sessions_history.referrer IS 'Operator''s site url that player comes from';

CREATE TABLE swmanagement.special_force_wal (
    id bigint,
    brand_id integer,
    player_code character varying(255),
    game_id character varying(255),
    game_code character varying(255),
    device_id character varying(255),
    currency character(3),
    total_win numeric,
    total_bet numeric,
    balance_before numeric,
    balance_after numeric,
    total_events numeric,
    started_at timestamp without time zone,
    finished_at timestamp without time zone,
    test boolean,
    session_id bigint,
    inserted_at timestamp without time zone,
    recovery_type swmanagement.enum_recovery_type
);

ALTER TABLE swmanagement.special_force_wal OWNER TO swmanagement;

CREATE SEQUENCE swmanagement.spinhistories_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE swmanagement.spinhistories_seq OWNER TO swmanagement;

CREATE TABLE swmanagement.spins_history (
    brand_id integer NOT NULL,
    player_code character varying(255) NOT NULL,
    game_id character varying(255) NOT NULL,
    game_code character varying(255) NOT NULL,
    device_id character varying(255) NOT NULL,
    round_ended boolean NOT NULL,
    spin_serial_number integer NOT NULL,
    wallet_transaction_id character varying(255),
    currency character(3) NOT NULL,
    win numeric NOT NULL,
    bet numeric NOT NULL,
    round_id bigint NOT NULL,
    ts timestamp without time zone NOT NULL,
    result jsonb NOT NULL,
    test boolean DEFAULT false NOT NULL,
    spin_history_id bigint DEFAULT nextval('swmanagement.spinhistories_seq'::regclass),
    game_type character varying(50),
    user_agent_id integer,
    balance_before numeric,
    balance_after numeric,
    game_version character varying(20),
    inserted_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    session_id bigint,
    total_jp_contribution numeric,
    total_jp_win numeric,
    extra_data jsonb,
    debit numeric,
    credit numeric,
    ctrl integer
)
WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN device_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN wallet_transaction_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN result SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN game_type SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN game_version SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN extra_data SET STORAGE EXTERNAL;
ALTER TABLE swmanagement.spins_history OWNER TO swmanagement;

COMMENT ON COLUMN swmanagement.spins_history.user_agent_id IS 'user agent link from user_agents dictionary';
COMMENT ON COLUMN swmanagement.spins_history.balance_before IS 'Balance befor player action';
COMMENT ON COLUMN swmanagement.spins_history.balance_after IS 'Balance after player action';
COMMENT ON COLUMN swmanagement.spins_history.game_version IS 'Game module version';
COMMENT ON COLUMN swmanagement.spins_history.session_id IS 'Reference to game session';
COMMENT ON COLUMN swmanagement.spins_history.total_jp_contribution IS 'Total jackpot contribution per spin in player currency';
COMMENT ON COLUMN swmanagement.spins_history.total_jp_win IS 'Total jackpot win in spin per player currency';
COMMENT ON COLUMN swmanagement.spins_history.extra_data IS 'Additional spin/round data to store';
COMMENT ON COLUMN swmanagement.spins_history.debit IS 'Debit amount (transfer-in amount, etc)';
COMMENT ON COLUMN swmanagement.spins_history.credit IS 'Credit amount (redeem bns amount, transfer-out amount, etc)';
COMMENT ON COLUMN swmanagement.spins_history.ctrl IS 'Control sum (random) to check logical duplicates';

CREATE SEQUENCE swmanagement.wallet_entity_payment_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE swmanagement.wallet_entity_payment_log_id_seq OWNER TO swmanagement;

CREATE TABLE swmanagement.wallet_entity_payment_log (
    id bigint DEFAULT nextval('swmanagement.wallet_entity_payment_log_id_seq'::regclass) NOT NULL,
    transfer_from character varying(255),
    transfer_to character varying(255),
    amount numeric DEFAULT 0 NOT NULL,
    currency character(3) NOT NULL,
    ts timestamp without time zone NOT NULL,
    transfer_type swmanagement.enum_wallet_entity_payment_log_transfer_type NOT NULL,
    is_test boolean DEFAULT false NOT NULL,
    inserted_at timestamp without time zone DEFAULT now(),
    initiator_name character varying(255)
);
ALTER TABLE ONLY swmanagement.wallet_entity_payment_log ALTER COLUMN transfer_from SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_entity_payment_log ALTER COLUMN transfer_to SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_entity_payment_log ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_entity_payment_log ALTER COLUMN initiator_name SET STORAGE EXTERNAL;

ALTER TABLE swmanagement.wallet_entity_payment_log OWNER TO swmanagement;

COMMENT ON TABLE swmanagement.wallet_entity_payment_log IS 'Table for storing money transfers from and to entity';
COMMENT ON COLUMN swmanagement.wallet_entity_payment_log.transfer_from IS 'Entity id or player code of money transferred from';
COMMENT ON COLUMN swmanagement.wallet_entity_payment_log.transfer_to IS 'Entity id or player code of money transferred to';
COMMENT ON COLUMN swmanagement.wallet_entity_payment_log.transfer_type IS 'Constraint to mark transfer direction (entity->player & etc...)';
COMMENT ON COLUMN swmanagement.wallet_entity_payment_log.initiator_name IS 'Username of account, that initiated transfer';

CREATE TABLE swmanagement.wallet_operation_log (
    id bigint NOT NULL,
    operation_id integer NOT NULL,
    operation_name character varying(255),
    public_id character(28) NOT NULL,
    external_trx_id character varying(255),
    is_external boolean NOT NULL,
    game_id character varying(255),
    ts timestamp without time zone NOT NULL,
    version integer NOT NULL,
    data jsonb NOT NULL,
    params jsonb,
    inserted_at timestamp without time zone DEFAULT now(),
    committed_at timestamp without time zone
)
WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN operation_name SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN public_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN external_trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN data SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN params SET STORAGE EXTERNAL;
ALTER TABLE swmanagement.wallet_operation_log OWNER TO swmanagement;
COMMENT ON COLUMN swmanagement.wallet_operation_log.params IS 'List of optional params';
COMMENT ON COLUMN swmanagement.wallet_operation_log.committed_at IS 'Timestamp when operation was committed';

CREATE TABLE swmanagement.wallet_win_bet (
    id bigint NOT NULL,
    trx_id character(28),
    brand_id integer,
    currency character(3),
    payment_date timestamp without time zone NOT NULL,
    bet numeric DEFAULT 0 NOT NULL,
    win numeric DEFAULT 0 NOT NULL,
    bet_rollback boolean DEFAULT false NOT NULL,
    game_id character varying(255) NOT NULL,
    player_code character varying,
    game_code character varying(255),
    transaction_type swmanagement.enum_wallet_win_bet_transaction_type,
    is_test boolean DEFAULT false,
    inserted_at timestamp without time zone DEFAULT now(),
    debit numeric,
    credit numeric,
    round_ended boolean DEFAULT false,
    sub_trx_type character varying(255)
)
WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE swmanagement.wallet_win_bet OWNER TO swmanagement;

COMMENT ON COLUMN swmanagement.wallet_win_bet.bet_rollback IS 'Marks transaction as rollback transaction - win and bet were restored on customer account';
COMMENT ON COLUMN swmanagement.wallet_win_bet.game_id IS 'Holds game id on a spin';
COMMENT ON COLUMN swmanagement.wallet_win_bet.player_code IS 'Stores player codes. Not a reference';
COMMENT ON COLUMN swmanagement.wallet_win_bet.game_code IS 'Game code needed for filtering by games';
COMMENT ON COLUMN swmanagement.wallet_win_bet.is_test IS 'Is it a test player';
COMMENT ON COLUMN swmanagement.wallet_win_bet.debit IS 'Debit amount (transfer-in amount, etc)';
COMMENT ON COLUMN swmanagement.wallet_win_bet.credit IS 'Credit amount (redeem bns amount, transfer-out, grc redeem amount, etc)';
COMMENT ON COLUMN swmanagement.wallet_win_bet.round_ended IS 'Flat that is true if round was ended on this bet/win operation';
COMMENT ON COLUMN swmanagement.wallet_win_bet.sub_trx_type IS 'Sub type of bonus payment (ph-tournament, bonus_coins_redeem, etc)';

CREATE SEQUENCE swmanagement.wallet_win_bet_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE swmanagement.wallet_win_bet_id_seq OWNER TO swmanagement;
ALTER SEQUENCE swmanagement.wallet_win_bet_id_seq OWNED BY swmanagement.wallet_win_bet.id;

CREATE TABLE swsystem.hist_arch_01_tables (
    schema_name character varying(255),
    parent_name character varying(255),
    table_name character varying(255),
    table_size_with_idx character varying(50),
    table_size character varying(50),
    partition_nr integer,
    status character varying(50)
);

ALTER TABLE swsystem.hist_arch_01_tables OWNER TO swsystem;

CREATE TABLE swsystem.replica_ticker (
    ts timestamp without time zone
);

ALTER TABLE swsystem.replica_ticker OWNER TO swsystem;

CREATE TABLE swsystem.sw_hashid_secret (
    hash_project character varying(20) NOT NULL,
    hash_salt text NOT NULL,
    hash_length integer NOT NULL
);

ALTER TABLE swsystem.sw_hashid_secret OWNER TO swsystem;
ALTER TABLE ONLY hist_cluster.hist_jobs ALTER COLUMN job_id SET DEFAULT nextval('hist_cluster.hist_jobs_job_id_seq'::regclass);
ALTER TABLE ONLY swmanagement.audits ALTER COLUMN audit_id SET DEFAULT nextval('swmanagement.audits_audit_id_seq'::regclass);
ALTER TABLE ONLY swmanagement.payments ALTER COLUMN id SET DEFAULT nextval('swmanagement.payments_id_seq'::regclass);
ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN id SET DEFAULT nextval('swmanagement.wallet_win_bet_id_seq'::regclass);
ALTER TABLE ONLY hist_cluster.hist_jobs ADD CONSTRAINT hist_jobs_pkey PRIMARY KEY (job_id);
ALTER TABLE ONLY public.pathman_allowed_tspaces ADD CONSTRAINT pathman_allowed_tspaces_pkey PRIMARY KEY (ts_name);
ALTER TABLE ONLY swjackpot.jp_contribution_log ADD CONSTRAINT jp_contribution_log_pkey PRIMARY KEY (id) WITH (fillfactor='100');
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ADD CONSTRAINT jp_wallet_operation_log_pkey PRIMARY KEY (id, operation_id) WITH (fillfactor='100');
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ADD CONSTRAINT remote_jp_contribution_log_pkey PRIMARY KEY (id);
ALTER TABLE ONLY swmanagement.audits ADD CONSTRAINT audits_pkey PRIMARY KEY (audit_id) WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.currency_rates ADD CONSTRAINT currency_rates_pkey PRIMARY KEY (currency_code, rate_date);
ALTER TABLE ONLY swmanagement.games ADD CONSTRAINT games_code_key UNIQUE (code);
ALTER TABLE ONLY swmanagement.games ADD CONSTRAINT games_pkey PRIMARY KEY (id);
ALTER TABLE ONLY swmanagement.payments ADD CONSTRAINT payments_pkey PRIMARY KEY (id);
ALTER TABLE ONLY swmanagement.payments ADD CONSTRAINT payments_trx_id_key UNIQUE (trx_id);
ALTER TABLE ONLY swmanagement.rounds_finished ADD CONSTRAINT rounds_finished_pkey PRIMARY KEY (id);
ALTER TABLE ONLY swmanagement.rounds_history ADD CONSTRAINT rounds_history_pkey PRIMARY KEY (id) WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.sessions_history ADD CONSTRAINT sessions_history_pkey PRIMARY KEY (id) WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.spins_history ADD CONSTRAINT spins_history_pkey PRIMARY KEY (brand_id, player_code, game_code, device_id, spin_serial_number, round_id) WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.wallet_entity_payment_log ADD CONSTRAINT wallet_entity_payment_log_pkey PRIMARY KEY (id);
ALTER TABLE ONLY swmanagement.wallet_operation_log ADD CONSTRAINT wallet_operation_log_pkey PRIMARY KEY (id, operation_id) WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.wallet_win_bet ADD CONSTRAINT wallet_win_bet_pkey PRIMARY KEY (id) WITH (fillfactor='100');
ALTER TABLE ONLY swsystem.sw_hashid_secret ADD CONSTRAINT sw_hashid_secret_pkey PRIMARY KEY (hash_project);

CREATE INDEX idx_hist_jobs_completed ON hist_cluster.hist_jobs USING btree (completed_at);
CREATE INDEX idx_hist_jobs_stab_name ON hist_cluster.hist_jobs USING btree (schema_name, table_name);

CREATE INDEX idx_jp_contribution_log__func_ins_at ON swjackpot.jp_contribution_log USING btree (COALESCE(inserted_at, trx_date)) WITH (fillfactor='100');
CREATE INDEX idx_jp_contribution_log_brand_id_trx_date_game_code ON swjackpot.jp_contribution_log USING btree (brand_id, trx_date DESC, game_code) WITH (fillfactor='100');
CREATE INDEX idx_jp_contribution_log_remote_trx_id ON swjackpot.jp_contribution_log USING btree (remote_trx_id) WITH (fillfactor='100');
CREATE INDEX idx_jp_contribution_log_trx_date_btree ON swjackpot.jp_contribution_log USING btree (trx_date) WITH (fillfactor='100');
CREATE UNIQUE INDEX idx_jp_contribution_log_trx_jp_id_pool_unique ON swjackpot.jp_contribution_log USING btree (trx_id, jackpot_id, pool) WITH (fillfactor='100');
CREATE INDEX idx_jp_wallet_operation_log_ts ON swjackpot.jp_wallet_operation_log USING btree (ts) WITH (fillfactor='100');

CREATE INDEX idx_remote_jp_contribution_log__func_ins_at ON swjackpot.remote_jp_contribution_log USING btree (COALESCE(inserted_at, trx_date));
CREATE INDEX idx_remote_jp_contribution_log_brand_id_and_trx_date ON swjackpot.remote_jp_contribution_log USING btree (brand_id, trx_date DESC);
CREATE INDEX idx_remote_jp_contribution_log_remote_trx_id ON swjackpot.remote_jp_contribution_log USING btree (remote_trx_id);
CREATE INDEX idx_remote_jp_contribution_log_trx_date ON swjackpot.remote_jp_contribution_log USING btree (trx_date);
CREATE UNIQUE INDEX idx_remote_jp_contribution_log_unique ON swjackpot.remote_jp_contribution_log USING btree (trx_id, jackpot_id, pool);
CREATE UNIQUE INDEX jp_contribution_unique ON swjackpot.jp_contribution_log USING btree (trx_id, jackpot_id, pool);
CREATE UNIQUE INDEX remote_jp_contribution_log_unique ON swjackpot.remote_jp_contribution_log USING btree (trx_id, jackpot_id, pool);

CREATE INDEX idx_audits_atype_eid_ts ON swmanagement.audits USING btree (audit_type, entity_id, ts DESC) WITH (fillfactor='100');
CREATE INDEX idx_audits_eid_inname_ts ON swmanagement.audits USING btree (entity_id, initiator_name, ts DESC) WITH (fillfactor='100');
CREATE INDEX idx_audits_entity_id_ts ON swmanagement.audits USING btree (entity_id, ts DESC) WITH (fillfactor='100');
CREATE INDEX idx_audits_inty_ts_atype_eid ON swmanagement.audits USING btree (initiator_type, ts DESC, audit_type, entity_id) WITH (fillfactor='100');
CREATE INDEX idx_audits_ts ON swmanagement.audits USING btree (ts) WITH (fillfactor='100');

CREATE INDEX idx_games_grc_games_only ON swmanagement.games USING btree ((((features ->> 'isGRCGame'::text))::boolean)) WHERE ((features ->> 'isGRCGame'::text))::boolean;
CREATE INDEX idx_games_schema_definition_id ON swmanagement.games USING btree (schema_definition_id);

CREATE INDEX idx_payments_brand_id_player_code_start_date ON swmanagement.payments USING btree (brand_id, player_code, start_date DESC);
CREATE INDEX idx_payments_created_at ON swmanagement.payments USING btree (created_at);
CREATE INDEX idx_payments_dates_brand_id_nottest ON swmanagement.payments USING btree (start_date DESC, end_date, brand_id) WHERE (NOT is_test);
CREATE INDEX idx_payments_dates_brand_id_test ON swmanagement.payments USING btree (start_date DESC, end_date, brand_id) WHERE is_test;
CREATE UNIQUE INDEX idx_payments_ext_trx_id_brand_id_unique ON swmanagement.payments USING btree (ext_trx_id, brand_id);
CREATE INDEX idx_payments_start_date_brand_id ON swmanagement.payments USING btree (start_date DESC, brand_id);
CREATE INDEX idx_payments_start_date_ordstat_bid ON swmanagement.payments USING btree (start_date DESC, order_status, brand_id);
CREATE INDEX idx_payments_str_dt_bid_where_type ON swmanagement.payments USING btree (start_date DESC, brand_id) WHERE ((order_type)::text = ANY (ARRAY[('transfer_in'::character varying)::text, ('transfer_out'::character varying)::text]));
CREATE INDEX idx_payments_updated_at ON swmanagement.payments USING btree (updated_at);

CREATE INDEX idx_rounds_f_brand_id_started_at ON swmanagement.rounds_finished USING btree (brand_id, started_at DESC) WITH (fillfactor='100');
CREATE INDEX idx_rounds_f_brnd_id_finish_at ON swmanagement.rounds_finished USING btree (brand_id, finished_at DESC) WITH (fillfactor='100');
CREATE INDEX idx_rounds_f_brnd_ins_at ON swmanagement.rounds_finished USING btree (brand_id, inserted_at DESC) WITH (fillfactor='100');
CREATE INDEX idx_rounds_f_fat_where_recovery ON swmanagement.rounds_finished USING btree (finished_at) WITH (fillfactor='100') WHERE (recovery_type IS NOT NULL);
CREATE INDEX idx_rounds_f_finish_at ON swmanagement.rounds_finished USING btree (finished_at) WITH (fillfactor='100');
CREATE INDEX idx_rounds_f_finish_at_brnd_gc_pl ON swmanagement.rounds_finished USING btree (finished_at DESC, brand_id, game_code, player_code) WITH (fillfactor='100');
CREATE INDEX idx_rounds_f_gc_brnd_start_at ON swmanagement.rounds_finished USING btree (game_code, brand_id, started_at) WITH (fillfactor='100');
CREATE INDEX idx_rounds_f_inserted_at_started_at ON swmanagement.rounds_finished USING btree (COALESCE(inserted_at, started_at)) WITH (fillfactor='100');
CREATE INDEX idx_rounds_f_pl_brnd_finish_at ON swmanagement.rounds_finished USING btree (player_code, brand_id, finished_at DESC) WITH (fillfactor='100');
CREATE INDEX idx_rounds_f_pl_brnd_start_at ON swmanagement.rounds_finished USING btree (player_code, brand_id, started_at) WITH (fillfactor='100');
CREATE INDEX idx_rounds_f_pl_gc ON swmanagement.rounds_finished USING btree (player_code, game_code) WITH (fillfactor='100');
CREATE INDEX idx_rounds_f_start_at_brnd_pl_gc ON swmanagement.rounds_finished USING btree (started_at, brand_id, player_code, game_code) WITH (fillfactor='100');
CREATE INDEX idx_rounds_f_started_at ON swmanagement.rounds_finished USING btree (started_at) WITH (fillfactor='100');

CREATE INDEX idx_rounds_history_brand_id_finished_at ON swmanagement.rounds_history USING btree (brand_id, finished_at DESC) WITH (fillfactor='100');
CREATE INDEX idx_rounds_history_brand_id_ins_at ON swmanagement.rounds_history USING btree (brand_id, inserted_at DESC) WITH (fillfactor='100');
CREATE INDEX idx_rounds_history_brand_id_ins_at_finished_false ON swmanagement.rounds_history USING btree (brand_id, inserted_at DESC) WITH (fillfactor='100') WHERE (finished_at IS NULL);
CREATE INDEX idx_rounds_history_finish_at_br_gc_pl ON swmanagement.rounds_history USING btree (finished_at DESC, brand_id, game_code, player_code) WITH (fillfactor='100');
CREATE INDEX idx_rounds_history_finished_at ON swmanagement.rounds_history USING btree (finished_at) WITH (fillfactor='100');
CREATE INDEX idx_rounds_history_gc_br_start_at ON swmanagement.rounds_history USING btree (game_code, brand_id, started_at) WITH (fillfactor='100');
CREATE INDEX idx_rounds_history_inserted_at_started_at ON swmanagement.rounds_history USING btree (COALESCE(inserted_at, started_at)) WITH (fillfactor='100');
CREATE INDEX idx_rounds_history_pl_br_finish_at ON swmanagement.rounds_history USING btree (player_code, brand_id, finished_at DESC) WITH (fillfactor='100');
CREATE INDEX idx_rounds_history_pl_br_start_at ON swmanagement.rounds_history USING btree (player_code, brand_id, started_at) WITH (fillfactor='100');
CREATE INDEX idx_rounds_history_player_game ON swmanagement.rounds_history USING btree (player_code, game_code) WITH (fillfactor='100');
CREATE INDEX idx_rounds_history_start_at_br_pl_gc ON swmanagement.rounds_history USING btree (started_at, brand_id, player_code, game_code) WITH (fillfactor='100');
CREATE INDEX idx_rounds_history_started_at ON swmanagement.rounds_history USING btree (started_at) WITH (fillfactor='100');
CREATE INDEX idx_sessions_history_inserted_at ON swmanagement.sessions_history USING btree (inserted_at) WITH (fillfactor='100');
CREATE INDEX idx_sessions_history_started_at ON swmanagement.sessions_history USING btree (started_at) WITH (fillfactor='100');
CREATE INDEX idx_spins_history_brand_player_game_ts ON swmanagement.spins_history USING btree (brand_id, player_code, game_code, ts DESC) WITH (fillfactor='100');
CREATE INDEX idx_spins_history_hid_ts ON swmanagement.spins_history USING btree (COALESCE(spin_history_id, (1)::bigint), ts) WITH (fillfactor='100');
CREATE INDEX idx_spins_history_inserted_at ON swmanagement.spins_history USING btree (COALESCE(inserted_at, ts)) WITH (fillfactor='100');
CREATE INDEX idx_spins_history_rid_ssn ON swmanagement.spins_history USING btree (round_id, spin_serial_number) WITH (fillfactor='100');
CREATE INDEX idx_spins_history_ts ON swmanagement.spins_history USING btree (ts) WITH (fillfactor='100');

CREATE INDEX idx_wallet_entity_payment_log_inserted_at ON swmanagement.wallet_entity_payment_log USING btree (inserted_at);
CREATE INDEX idx_wallet_entity_payment_log_ts ON swmanagement.wallet_entity_payment_log USING btree (ts);
CREATE INDEX idx_wallet_entity_payment_log_type_tf ON swmanagement.wallet_entity_payment_log USING btree (transfer_type, transfer_from);
CREATE INDEX idx_wallet_entity_payment_log_type_tt ON swmanagement.wallet_entity_payment_log USING btree (transfer_type, transfer_to);
CREATE INDEX idx_wallet_operation_log_game_id ON swmanagement.wallet_operation_log USING btree (game_id) WITH (fillfactor='100');
CREATE INDEX idx_wallet_operation_log_public_id ON swmanagement.wallet_operation_log USING btree (public_id) WITH (fillfactor='100');
CREATE INDEX idx_wallet_operation_log_ts ON swmanagement.wallet_operation_log USING btree (ts) WITH (fillfactor='100');
CREATE INDEX idx_wallet_win_bet_brand_id_payment_date_currency ON swmanagement.wallet_win_bet USING btree (brand_id, payment_date, currency) WITH (fillfactor='100');
CREATE INDEX idx_wallet_win_bet_game_id ON swmanagement.wallet_win_bet USING btree (game_id) WITH (fillfactor='100');
CREATE INDEX idx_wallet_win_bet_inserted_at ON swmanagement.wallet_win_bet USING btree (COALESCE(inserted_at, payment_date)) WITH (fillfactor='100');
CREATE INDEX idx_wallet_win_bet_paydate ON swmanagement.wallet_win_bet USING btree (payment_date) WITH (fillfactor='100');
CREATE INDEX idx_wallet_win_bet_trx_id ON swmanagement.wallet_win_bet USING btree (trx_id) WITH (fillfactor='100');
CREATE INDEX wallet_win_bet_brand_id_payment_date_currency ON swmanagement.wallet_win_bet USING btree (brand_id, payment_date, currency);

-- Functions
CREATE FUNCTION hist_cluster.fnc_hist_run_background_jobs() RETURNS void
    LANGUAGE plpgsql
    SET client_min_messages TO 'log'
    AS $_$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:
   Purpose    :
   History    :
      1.0.0
         Date    : Jan 11, 2019
         Authors : Timur Luchkin
         Notes   : Initial release

   Sample run:
      SELECT hist_cluster.fnc_hist_run_background_jobs();

   PG Cron:
      SELECT cron.schedule('* * * * *', $$SELECT hist_cluster.fnc_hist_run_background_jobs()$$);

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   v_lock_id   BIGINT := 7777777775555555;
   rec_jobs    hist_cluster.hist_jobs%ROWTYPE;
BEGIN
-- Check if MDB
   IF (SELECT pg_is_in_recovery()) THEN
      RAISE LOG 'HIST-ETL: Not a Master DB. Skip and exit now'; RETURN;
   END IF;

-- Singleton
   IF NOT pg_try_advisory_xact_lock(v_lock_id) THEN
      RAISE LOG 'HIST-ETL: Can''t get exclusive lock. Exit now'; RETURN;
   END IF;

   -- Fetch JOB to process
   BEGIN
      SELECT *
      FROM   hist_cluster.hist_jobs
      WHERE  completed_at IS NULL
      ORDER  BY job_id
      LIMIT 1
      INTO   STRICT rec_jobs;
   EXCEPTION
      WHEN no_data_found THEN
         RAISE LOG 'HIST-ETL: No any jobs to process. Exit now'; RETURN;
      WHEN others THEN
         RAISE EXCEPTION 'HIST-ETL: Unexpected error during JOB load: %', SQLERRM;
   END;

   -- Check there is no active analyze on this table
   IF EXISTS (SELECT NULL FROM pg_stat_activity WHERE query ~* 'analyze' AND query ~* (rec_jobs.schema_name||'.'||rec_jobs.table_name) AND pid != pg_backend_pid()) THEN
      RAISE LOG 'HIST-ETL: Active system analyze of %.% is in progress. Skip', rec_jobs.schema_name, rec_jobs.table_name; RETURN;
   END IF;

   -- Do job
   IF rec_jobs.action = 'analyze' THEN
      IF (SELECT last_autoanalyze IS NULL from pg_stat_all_tables where schemaname = rec_jobs.schema_name and relname = rec_jobs.table_name) THEN
         EXECUTE FORMAT('ANALYZE "%s"."%s"', rec_jobs.schema_name, rec_jobs.table_name);
      ELSE
         RAISE LOG 'HIST-ETL: Table %.% is already analyzed', rec_jobs.schema_name, rec_jobs.table_name;
      END IF;

   ELSIF rec_jobs.action = 'reindex' THEN
      EXECUTE FORMAT($sql1$ SELECT public.fnc_transfer_finish_table(p_schema_name:='%s', p_table_name:= '%s') $sql1$, rec_jobs.schema_name, rec_jobs.table_name);
   ELSE
      RAISE EXCEPTION 'HIST-ETL: Unknown JOB action: "%"', rec_jobs.action;
   END IF;

   UPDATE hist_cluster.hist_jobs SET
          started_at    = current_timestamp AT TIME ZONE 'UTC'
         ,completed_at  = clock_timestamp() AT TIME ZONE 'UTC'
   WHERE  job_id = rec_jobs.job_id;

   RAISE LOG 'HIST-ETL: Completed % for %.%', rec_jobs.action, rec_jobs.schema_name, rec_jobs.table_name; RETURN;
END;
$_$;
ALTER FUNCTION hist_cluster.fnc_hist_run_background_jobs() OWNER TO swsystem;


CREATE FUNCTION public.fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   fnc_transfer_finish_table
   Purpose    :   Reindex disable indexes
   History    :
      1.0.0
         Date    : Oct 01, 2018
         Authors : Timur Luchkin
         Notes   : Initial release

     1.0.1
         Date    : Jan 07, 2019
         Authors : Timur Luchkin
         Notes   : Reindex disabled indexes only (due to always enabled PK index)

   Sample run:
      SELECT * FROM public.fnc_transfer_finish_table (p_schema_name := 'swmanagement', p_table_name := 'rounds_history_30');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   v_lock_id   BIGINT := 7777777777777777;
   rec_idx     RECORD;
BEGIN
   -- Lets lock the logic to prevent a mess
   IF NOT pg_try_advisory_xact_lock(v_lock_id) THEN
      RETURN 'ALREADY_RUN';
   END IF;

   -- Get all table indexes
   FOR rec_idx IN SELECT i.indexrelid, i.indisready, i.indisvalid, i.indisprimary, i.indrelid
                        ,si.nspname AS idx_schema, ci.relname AS idx_name
                  FROM   pg_index i
                         INNER JOIN pg_class ct ON i.indrelid = ct.oid
                         INNER JOIN pg_class ci ON i.indexrelid = ci.oid
                         INNER JOIN pg_namespace st ON ct.relnamespace = st.oid
                         INNER JOIN pg_namespace si ON ci.relnamespace = si.oid
                  WHERE  ct.relname = p_table_name
                    AND  st.nspname = p_schema_name
                    AND NOT (i.indisready AND i.indisvalid)
   LOOP
      -- Enable table indexes
      UPDATE pg_index u SET
             indisready = TRUE
            ,indisvalid = TRUE
      WHERE u.indexrelid = rec_idx.indexrelid;

      EXECUTE FORMAT('REINDEX INDEX %s.%s',
                        rec_idx.idx_schema,
                        rec_idx.idx_name
                    );
   END LOOP;

   RETURN 'OK';
END
$$;

ALTER FUNCTION public.fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) OWNER TO postgres;

CREATE FUNCTION public.fnc_transfer_get_random_tblspcs(p_number integer, p_exclude_tblspc character varying) RETURNS text[]
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   fnc_transfer_get_random_tblspcs
   Purpose    :   To get array of tablespaces ordered by random, but take its size into account
   History    :
      1.0.0
         Date    : Jan 25, 2019
         Authors : Timur Luchkin
         Notes   : Initial release

      1.0.1
         Date    : Jul 04, 2019
         Authors : Timur Luchkin
         Notes   : Fix division by zero

   Sample run:
      SELECT public.fnc_transfer_get_random_tblspcs (p_number := 10, p_exclude_tblspc := NULL);

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
BEGIN
   RETURN
   (
      WITH cte_source AS (
                        SELECT tablespace
                              --,disk_size_pretty
                              ,size_percent
                              ,distance
                              ,CASE WHEN distance <= 15 THEN distance ^ 3.0
                                                  ELSE 5000
                               END::INTEGER AS distance_power
                        FROM   (
                                 SELECT tablespace
                                       --,disk_size_pretty
                                       ,size_percent
                                       ,Abs(Min(1 - size_percent) OVER () - (1 - size_percent)) * 100 + 4  AS distance
                                 FROM  (
                                          SELECT tablespace
                                                ,disk_size
                                                --,pg_size_pretty(disk_size) AS disk_size_pretty
                                                -- ,Round(disk_size / Sum(disk_size) OVER (), 2) AS size_percent
                                                ,Round(CASE WHEN (Sum(disk_size) OVER ()) = 0 THEN 0 ELSE disk_size / (Sum(disk_size) OVER ()) END, 2) AS size_percent
                                          FROM  (
                                                   SELECT ts.spcname AS tablespace
                                                         ,pg_tablespace_size(ts.spcname) AS disk_size
                                                   FROM   pg_tablespace ts
                                                          INNER JOIN
                                                          pathman_allowed_tspaces alts ON ts.spcname = alts.ts_name
                                                   --WHERE  ts.spcname != p_exclude_tblspc
                                                   WHERE  ts.spcname != Coalesce(p_exclude_tblspc, 'N/A')
                                                ) t
                                       ) t2
                              ) t3
                     )
      SELECT array_agg(tbs_array order by random())
      FROM   (
               SELECT *
               FROM   (
                        SELECT unnest(array_remove (string_to_array (repeat(tablespace||'|', distance_power), '|'), '')) AS tbs_array
                        FROM   cte_source
                      ) t99
               ORDER BY random()
               LIMIT p_number
             ) t100
   );
END
$$;
ALTER FUNCTION public.fnc_transfer_get_random_tblspcs(p_number integer, p_exclude_tblspc character varying) OWNER TO swsystem;

CREATE FUNCTION public.fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   fnc_transfer_prepare_table
   Purpose    :   To randomize tablespaces for table and its indexes and disable indexes
   History    :
      1.0.0
         Date    : Oct 01, 2018
         Authors : Timur Luchkin
         Notes   : Initial release

     1.0.1
         Date    : Jan 07, 2019
         Authors : Timur Luchkin
         Notes   : Primary key index will be not disabled to support logical replication

     1.0.2
         Date    : Jan 25, 2019
         Authors : Timur Luchkin
         Notes   : Add a smarter way to choose random tablespaces (see function fnc_transfer_get_random_tblspcs)
                   Do not allow to change tablespaces for non-empty table

   Sample run:
      SELECT * FROM public.fnc_transfer_prepare_table (p_schema_name := 'swmanagement', p_table_name := 'audits_72');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   v_lock_id         BIGINT := 7777777777777777;
   rec1              RECORD;
   ts_name_all       TEXT := '';
   records_count     BIGINT;
   obj_count         INTEGER;
   arr_tablespaces   TEXT[];
   i                 INTEGER;
   err_msg           TEXT;
BEGIN
   -- Lets lock the logic to prevent a mess
   IF NOT pg_try_advisory_xact_lock(v_lock_id) THEN
      RETURN 'ALREADY_RUN';
   END IF;

   EXECUTE FORMAT('SELECT Count(*) FROM "%s"."%s"', p_schema_name, p_table_name) INTO records_count;
   IF records_count > 0 THEN
      RETURN 'Table is not empty. Exit';
   END IF;

   -- Get total number of objects to split among tablespaces
   EXECUTE FORMAT ('SELECT count(*) FROM pg_indexes WHERE schemaname = ''%s'' and tablename = ''%s'' ', p_schema_name, p_table_name) INTO obj_count;
   obj_count := obj_count + 1; -- table itself

   BEGIN
      arr_tablespaces := public.fnc_transfer_get_random_tblspcs (p_number := obj_count, p_exclude_tblspc := NULL::VARCHAR);

      IF array_length(arr_tablespaces, 1) = obj_count THEN
         i := 1;
      ELSE
         i := -1;
      END IF;
   EXCEPTION
      WHEN others THEN
         i := -1;
         err_msg := SQLERRM;
   END;

   -- set random tablespace from the list of allowed
   IF i = 1 THEN
      -- move partition to the tablespace
      EXECUTE FORMAT('ALTER TABLE "%s"."%s" SET TABLESPACE %s',
         p_schema_name,
         p_table_name,
         arr_tablespaces[i]);

      ts_name_all := arr_tablespaces[i];
      i := i + 1;

      -- move indexes
      FOR rec1 IN EXECUTE FORMAT ('SELECT schemaname, indexname FROM pg_indexes WHERE schemaname = ''%s'' and tablename = ''%s'' ',
                                  p_schema_name,
                                  p_table_name
                                 )
      LOOP

         EXECUTE FORMAT('ALTER INDEX %s.%s SET TABLESPACE %s',
            rec1.schemaname,
            rec1.indexname,
            arr_tablespaces[i]);

         ts_name_all := ts_name_all ||'; '|| arr_tablespaces[i];
         i := i + 1;
      END LOOP;
   END IF;

   -- Disable table indexes
   UPDATE pg_index SET
          indisready = FALSE
         ,indisvalid = FALSE
   WHERE  indrelid = (
                      SELECT oid
                      FROM   pg_class
                      WHERE  relname = p_table_name
                        AND  relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = p_schema_name)
                     )
     AND NOT indisprimary
   ;

   IF i = -1 THEN
      RETURN FORMAT ('Tablespace part has been failed: %s', err_msg);
   ELSE
      RETURN ts_name_all;
   END IF;
END
$$;
ALTER FUNCTION public.fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) OWNER TO postgres;

CREATE FUNCTION public.fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   fnc_zfs_change_storage
   Purpose    :   Change storage type from EXTENDED to EXTERNAL for all columns
                  We do not need additional compression in PG on ZFS
   History    :
      1.0.0
         Date    : Nov 11, 2019
         Authors : Timur Luchkin
         Notes   : Initial release


   Sample run:
      SELECT * FROM public.fnc_zfs_change_storage (p_schema_name := 'swmanagement', p_table_name := 'rounds_history_30');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   rec_loop       RECORD;
   v_lock_id      BIGINT := 7777777777777777;
BEGIN
   -- Lets lock the logic to prevent a mess
   IF NOT pg_try_advisory_xact_lock(v_lock_id) THEN
      RETURN 'ALREADY_RUN';
   END IF;

   FOR rec_loop IN   SELECT ns.nspname, tbl.relname, att.attname
                     FROM   pg_attribute att
                            INNER JOIN pg_class tbl ON tbl.oid = att.attrelid
                            INNER JOIN pg_namespace ns ON tbl.relnamespace = ns.oid
                     WHERE tbl.relname = p_table_name
                       AND ns.nspname = p_schema_name
                       AND att.attstorage = 'x' -- EXTENDED
                       AND NOT att.attisdropped
   LOOP
      EXECUTE FORMAT('ALTER TABLE "%s"."%s" ALTER "%s" SET STORAGE EXTERNAL'
                     ,rec_loop.nspname
                     ,rec_loop.relname
                     ,rec_loop.attname
                    );

      SET client_min_messages TO INFO;
      RAISE INFO 'Change storage for %', rec_loop.attname;
      RESET client_min_messages;
   END LOOP;

   RETURN 'OK';
END
$$;
ALTER FUNCTION public.fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) OWNER TO swsystem;

CREATE FUNCTION public.special_fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   special_fnc_transfer_finish_table
   Purpose    :   Reindex disable indexes
   History    :
      1.0.0
         Date    : Oct 01, 2018
         Authors : Timur Luchkin
         Notes   : Initial release

     1.0.1
         Date    : Jan 07, 2019
         Authors : Timur Luchkin
         Notes   : Reindex disabled indexes only (due to always enabled PK index)

   Sample run:
      SELECT * FROM public.special_fnc_transfer_finish_table (p_schema_name := 'swmanagement', p_table_name := 'rounds_history_30');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   v_lock_id   BIGINT := 7777777777777777;
   rec_idx     RECORD;
BEGIN
   -- Get all table indexes
   -- FOR rec_idx IN SELECT i.indexrelid, i.indisready, i.indisvalid, i.indisprimary, i.indrelid
   --                      ,si.nspname AS idx_schema, ci.relname AS idx_name
   --                FROM   pg_index i
   --                       INNER JOIN pg_class ct ON i.indrelid = ct.oid
   --                       INNER JOIN pg_class ci ON i.indexrelid = ci.oid
   --                       INNER JOIN pg_namespace st ON ct.relnamespace = st.oid
   --                       INNER JOIN pg_namespace si ON ci.relnamespace = si.oid
   --                WHERE  ct.relname = p_table_name
   --                  AND  st.nspname = p_schema_name
   --                  AND NOT (i.indisready AND i.indisvalid)
   -- LOOP
   --    -- Enable table indexes
   --    UPDATE pg_index u SET
   --           indisready = TRUE
   --          ,indisvalid = TRUE
   --    WHERE u.indexrelid = rec_idx.indexrelid;
   --
   -- END LOOP;

   EXECUTE FORMAT('REINDEX TABLE %s.%s',
                     p_schema_name,
                     p_schema_name
               );



   RETURN 'OK';
END
$$;

ALTER FUNCTION public.special_fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) OWNER TO swsystem;

CREATE FUNCTION public.special_fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   special_fnc_transfer_prepare_table
   Purpose    :   To randomize tablespaces for table and its indexes and disable indexes
   History    :
      1.0.0
         Date    : Oct 01, 2018
         Authors : Timur Luchkin
         Notes   : Initial release

     1.0.1
         Date    : Jan 07, 2019
         Authors : Timur Luchkin
         Notes   : Primary key index will be not disabled to support logical replication

     1.0.2
         Date    : Jan 25, 2019
         Authors : Timur Luchkin
         Notes   : Add a smarter way to choose random tablespaces (see function fnc_transfer_get_random_tblspcs)
                   Do not allow to change tablespaces for non-empty table

   Sample run:
      SELECT * FROM public.special_fnc_transfer_prepare_table (p_schema_name := 'swmanagement', p_table_name := 'audits_72');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   v_lock_id         BIGINT := 7777777777777777;
   rec1              RECORD;
   ts_name_all       TEXT := '';
   records_count     BIGINT;
   obj_count         INTEGER;
   arr_tablespaces   TEXT[];
   i                 INTEGER;
   err_msg           TEXT;
BEGIN

   -- TLU: TODO: Optimize next check with just LIMIT 1 - NOT FOUND
   EXECUTE FORMAT('SELECT Count(*) FROM "%s"."%s"', p_schema_name, p_table_name) INTO records_count;
   IF records_count > 0 THEN
      RETURN 'Table is not empty. Exit';
   END IF;

   -- Get total number of objects to split among tablespaces
   EXECUTE FORMAT ('SELECT count(*) FROM pg_indexes WHERE schemaname = ''%s'' and tablename = ''%s'' ', p_schema_name, p_table_name) INTO obj_count;
   obj_count := obj_count + 1; -- table itself

   BEGIN
      arr_tablespaces := public.fnc_transfer_get_random_tblspcs (p_number := obj_count, p_exclude_tblspc := NULL::VARCHAR);

      IF array_length(arr_tablespaces, 1) = obj_count THEN
         i := 1;
      ELSE
         i := -1;
      END IF;
   EXCEPTION
      WHEN others THEN
         i := -1;
         err_msg := SQLERRM;
   END;

   -- set random tablespace from the list of allowed
   IF i = 1 THEN
      -- move partition to the tablespace
      EXECUTE FORMAT('ALTER TABLE "%s"."%s" SET TABLESPACE %s',
         p_schema_name,
         p_table_name,
         arr_tablespaces[i]);

      ts_name_all := arr_tablespaces[i];
      i := i + 1;

      -- move indexes
      FOR rec1 IN EXECUTE FORMAT ('SELECT schemaname, indexname FROM pg_indexes WHERE schemaname = ''%s'' and tablename = ''%s'' ',
                                  p_schema_name,
                                  p_table_name
                                 )
      LOOP

         EXECUTE FORMAT('ALTER INDEX %s.%s SET TABLESPACE %s',
            rec1.schemaname,
            rec1.indexname,
            arr_tablespaces[i]);

         ts_name_all := ts_name_all ||'; '|| arr_tablespaces[i];
         i := i + 1;
      END LOOP;
   END IF;

   -- Disable table indexes
   UPDATE pg_index SET
          indisready = FALSE
         ,indisvalid = FALSE
   WHERE  indrelid = (
                      SELECT oid
                      FROM   pg_class
                      WHERE  relname = p_table_name
                        AND  relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = p_schema_name)
                     )
   ;

   IF i = -1 THEN
      RETURN FORMAT ('Tablespace part has been failed: %s', err_msg);
   ELSE
      RETURN ts_name_all;
   END IF;
END
$$;
ALTER FUNCTION public.special_fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) OWNER TO swsystem;


CREATE FUNCTION public.special_fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   special_fnc_zfs_change_storage
   Purpose    :   Change storage type from EXTENDED to EXTERNAL for all columns
                  We do not need additional compression in PG on ZFS
   History    :
      1.0.0
         Date    : Nov 11, 2019
         Authors : Timur Luchkin
         Notes   : Initial release


   Sample run:
      SELECT * FROM public.special_fnc_zfs_change_storage (p_schema_name := 'swmanagement', p_table_name := 'rounds_history_30');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   rec_loop       RECORD;
   v_lock_id      BIGINT := 7777777777777777;
BEGIN

   FOR rec_loop IN   SELECT ns.nspname, tbl.relname, att.attname
                     FROM   pg_attribute att
                            INNER JOIN pg_class tbl ON tbl.oid = att.attrelid
                            INNER JOIN pg_namespace ns ON tbl.relnamespace = ns.oid
                     WHERE tbl.relname = p_table_name
                       AND ns.nspname = p_schema_name
                       AND att.attstorage = 'x' -- EXTENDED
                       AND NOT att.attisdropped
   LOOP
      EXECUTE FORMAT('ALTER TABLE "%s"."%s" ALTER "%s" SET STORAGE EXTERNAL'
                     ,rec_loop.nspname
                     ,rec_loop.relname
                     ,rec_loop.attname
                    );

      SET client_min_messages TO INFO;
      RAISE INFO 'Change storage for %', rec_loop.attname;
      RESET client_min_messages;
   END LOOP;

   RETURN 'OK';
END
$$;
ALTER FUNCTION public.special_fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) OWNER TO swsystem;

CREATE FUNCTION public.sw_get_internal_id(public_id text, OUT internal_id bigint) RETURNS bigint
    LANGUAGE plpgsql STABLE SECURITY DEFINER PARALLEL RESTRICTED
    AS $$
BEGIN
   SELECT (public.id_decode(public_id, po_hash_salt, po_hash_length))[1]
   FROM   swsystem.get_sw_hashid ('sw-falcon') INTO internal_id;
END;
$$;
ALTER FUNCTION public.sw_get_internal_id(public_id text, OUT internal_id bigint) OWNER TO swsystem;

CREATE FUNCTION public.sw_get_public_id(internal_id bigint, OUT public_id text) RETURNS text
    LANGUAGE plpgsql STABLE SECURITY DEFINER PARALLEL RESTRICTED
    AS $$
BEGIN
   SELECT public.id_encode(internal_id, po_hash_salt, po_hash_length)
   FROM   swsystem.get_sw_hashid ('sw-falcon') INTO public_id;
END;
$$;
ALTER FUNCTION public.sw_get_public_id(internal_id bigint, OUT public_id text) OWNER TO swsystem;

CREATE FUNCTION public.test_acid() RETURNS integer
    LANGUAGE plpgsql IMMUTABLE
    AS $$BEGIN RETURN 2; END; $$;


ALTER FUNCTION public.test_acid() OWNER TO postgres;

CREATE FUNCTION public.tham_get_random(OUT random_out numeric) RETURNS numeric
    LANGUAGE sql
    AS $$
         SELECT random()::NUMERIC;
      $$;


ALTER FUNCTION public.tham_get_random(OUT random_out numeric) OWNER TO postgres;

CREATE FUNCTION public.tham_random_date(date_from timestamp without time zone, date_to timestamp without time zone, OUT date_out timestamp without time zone) RETURNS timestamp without time zone
    LANGUAGE sql
    AS $$
            SELECT date_from + random() * (date_to - date_from);
         $$;


ALTER FUNCTION public.tham_random_date(date_from timestamp without time zone, date_to timestamp without time zone, OUT date_out timestamp without time zone) OWNER TO postgres;

CREATE FUNCTION swsystem.fnc_call_explain()
 RETURNS TABLE(log_time timestamp without time zone, log_msg text)
 LANGUAGE plpgsql
AS $function$
/*
*******************************************************************************
    Object Name:   fnc_call_explain
    Purpose    :   For historical culsters manually call EXPLAIN command
    History    :
        1.0.1
            Date    :  Sep 09, 2020
            Authors : Valdis Akmens
            Notes   : Improve "fnc_call_explain" (SWDB-158)

        1.0.0
            Date    :  Jul 06, 2020
            Authors : Valdis Akmens
            Notes   : Release

    Sample run:
      SELECT * FROM swsystem.fnc_call_explain();
*******************************************************************************
*/
DECLARE
v_exec_sql          VARCHAR;
v_lock_id           BIGINT := 3334444555555666666;
v_tables            VARCHAR[]:='{"swmanagement.rounds_finished","swmanagement.rounds_history","swmanagement.spins_history"}';
v_index             INTEGER;
v_rounds_ids        VARCHAR;
v_brands_ids        VARCHAR;
V_round_id_column   VARCHAR;
v_brand_column      VARCHAR;
BEGIN
    log_time := clock_timestamp(); log_msg := 'INFO: EXPLAIN job started'; RETURN NEXT;

        /* Check if another job is running*/
    IF NOT (SELECT pg_try_advisory_xact_lock(v_lock_id)) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Another function is running. Skip'; RETURN NEXT;
                RETURN;
    END IF;

    /* Get index to choose table that will be analyzed */
    v_index:= (SELECT (random()*100)::INTEGER % (SELECT array_length(v_tables,1))+1);

        /* Check if ETL job on affected table is running*/
    IF EXISTS(SELECT * FROM hist_cluster.hist_jobs WHERE (completed_at IS NULL OR NOW() - completed_at < '5 minutes'::INTERVAL) AND table_name LIKE split_part(v_tables[v_index],'.',2)||'%') THEN
        log_time := clock_timestamp(); log_msg := 'INFO: ETL job is running. Skip'; RETURN NEXT;
                RETURN;
    END IF;

    /*Set varibles for pg_stats*/
    CASE WHEN v_tables[v_index] = 'swmanagement.rounds_finished' OR v_tables[v_index] = 'swmanagement.rounds_history' THEN
        V_round_id_column:='id';
        v_brand_column:='brand_id';
        WHEN v_tables[v_index] = 'swmanagement.spins_history' THEN
        V_round_id_column:='round_id';
        v_brand_column:='brand_id';
    END CASE;

    /*Get round_ids*/
    WITH cte AS (
        SELECT unnest(histogram_bounds::TEXT::BIGINT[]) AS ids
        FROM pg_stats
        WHERE tablename LIKE split_part(v_tables[v_index],'.',2)||'%' AND attname = V_round_id_column
    )
    SELECT string_agg(ids::VARCHAR,',')
    INTO v_rounds_ids
    FROM (
        SELECT *
        FROM cte
        ORDER BY random()
        LIMIT ROUND(random() * (10 - 1)) + 1
    ) AS x ;
    v_rounds_ids:=COALESCE(v_rounds_ids, '1');
    --RAISE INFO 'v_rounds_ids = %', v_rounds_ids;

    /*Get brand_ids*/
    WITH cte AS (
        SELECT DISTINCT unnest(histogram_bounds::TEXT::BIGINT[]) AS ids
        FROM pg_stats
        WHERE tablename LIKE split_part(v_tables[v_index],'.',2)||'%' AND attname = v_brand_column
    )
    SELECT string_agg(ids::VARCHAR,',')
    INTO v_brands_ids
    FROM (
        SELECT *
        FROM cte
        ORDER BY random()
        LIMIT ROUND(random() * (10 - 1)) + 1
    ) AS x ;
    v_brands_ids:=COALESCE(v_brands_ids, '1');
    --RAISE INFO 'v_brands_ids = %', v_brands_ids;

    v_exec_sql:='EXPLAIN SELECT * FROM '||v_tables[v_index]||' WHERE '||v_brand_column||' IN ('||v_brands_ids||') AND '||V_round_id_column||' IN ('||v_rounds_ids||');';
    --RAISE INFO 'v_exec_sql = %',v_exec_sql;

    log_time := clock_timestamp(); log_msg := 'INFO: Start '||v_tables[v_index]||' EXPLAIN. '; RETURN NEXT;
    EXECUTE v_exec_sql;
    log_time := clock_timestamp(); log_msg := 'INFO: Finish '||v_tables[v_index]||' EXPLAIN. '; RETURN NEXT;
    RETURN;
END;
$function$;

ALTER FUNCTION swsystem.fnc_call_explain() OWNER TO swsystem;

CREATE FUNCTION swsystem.get_sw_hashid(p_hash_project character varying, OUT po_hash_salt text, OUT po_hash_length integer) RETURNS record
    LANGUAGE plpgsql STABLE SECURITY DEFINER PARALLEL RESTRICTED
    AS $$
BEGIN
   SELECT hash_salt, hash_length FROM swsystem.sw_hashid_secret WHERE hash_project = p_hash_project INTO po_hash_salt, po_hash_length;
END;
$$;
ALTER FUNCTION swsystem.get_sw_hashid(p_hash_project character varying, OUT po_hash_salt text, OUT po_hash_length integer) OWNER TO swsystem;

ALTER TABLE swmanagement.rounds_finished ADD COLUMN IF NOT EXISTS extra_data jsonb;
ALTER TABLE swmanagement.rounds_history ADD COLUMN IF NOT EXISTS extra_data jsonb;
ALTER TABLE swmanagement.audits RENAME COLUMN audit_type TO audits_summary_id;
ALTER TABLE swmanagement.audits ADD COLUMN audit_type smallint;
ALTER TABLE swmanagement.audits ALTER COLUMN audits_summary_id DROP NOT NULL;

GRANT USAGE ON SCHEMA pglogical TO PUBLIC;
GRANT USAGE ON SCHEMA swmanagement TO redis_game_offloader;
GRANT USAGE ON SCHEMA swmanagement TO kafka_offloader;
REVOKE ALL ON FUNCTION hist_cluster.fnc_hist_run_background_jobs() FROM PUBLIC;
GRANT ALL ON FUNCTION public.fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) TO swjackpot;
GRANT ALL ON FUNCTION public.fnc_transfer_get_random_tblspcs(p_number integer, p_exclude_tblspc character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.fnc_transfer_get_random_tblspcs(p_number integer, p_exclude_tblspc character varying) TO swjackpot;
GRANT ALL ON FUNCTION public.fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) TO swjackpot;
GRANT ALL ON FUNCTION public.fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) TO swjackpot;
GRANT ALL ON FUNCTION public.special_fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.special_fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) TO swjackpot;
GRANT ALL ON FUNCTION public.special_fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.special_fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) TO swjackpot;
GRANT ALL ON FUNCTION public.special_fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.special_fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) TO swjackpot;
REVOKE ALL ON FUNCTION swsystem.get_sw_hashid(p_hash_project character varying, OUT po_hash_salt text, OUT po_hash_length integer) FROM PUBLIC;
GRANT SELECT ON TABLE public.pathman_allowed_tspaces TO PUBLIC;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.audits TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.audits TO kafka_offloader;
GRANT USAGE ON SEQUENCE swmanagement.audits_audit_id_seq TO redis_game_offloader;
GRANT USAGE ON SEQUENCE swmanagement.audits_audit_id_seq TO kafka_offloader;
GRANT USAGE ON SEQUENCE swmanagement.payments_id_seq TO redis_game_offloader;
GRANT USAGE ON SEQUENCE swmanagement.payments_id_seq TO kafka_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.rounds_finished TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.rounds_finished TO kafka_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.sessions_history TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.sessions_history TO kafka_offloader;
GRANT USAGE ON SEQUENCE swmanagement.spinhistories_seq TO redis_game_offloader;
GRANT USAGE ON SEQUENCE swmanagement.spinhistories_seq TO kafka_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.spins_history TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.spins_history TO kafka_offloader;
GRANT USAGE ON SEQUENCE swmanagement.wallet_entity_payment_log_id_seq TO redis_game_offloader;
GRANT USAGE ON SEQUENCE swmanagement.wallet_entity_payment_log_id_seq TO kafka_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_entity_payment_log TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_entity_payment_log TO kafka_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_operation_log TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_operation_log TO kafka_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_win_bet TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_win_bet TO kafka_offloader;
GRANT USAGE ON SEQUENCE swmanagement.wallet_win_bet_id_seq TO redis_game_offloader;
GRANT USAGE ON SEQUENCE swmanagement.wallet_win_bet_id_seq TO kafka_offloader;

--#
--rollback select now();

