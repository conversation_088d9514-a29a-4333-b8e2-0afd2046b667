--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement, public;
--example: CREATE TABLE tmp(id int);
--example: --rollback DROP TABLE tmp;

--changeset aleh.rudzko:2021-03-20-SWS-26118-operator-domain-management
--comment write infomation about operator site id to session history
SET search_path = swmanagement;
ALTER TABLE sessions_history ADD COLUMN operator_site_id INTEGER;
COMMENT ON COLUMN sessions_history.operator_site_id IS 'Operator''s site id';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_site_id;
--rollback RESET search_path;

--changeset aleh.rudzko:2021-03-22-SWS-26134-add-operator-site-id-to-history
--comment write infomation about operator site id to round history
SET search_path = swmanagement;
ALTER TABLE rounds_finished ADD COLUMN operator_site_id INTEGER;
ALTER TABLE rounds_history ADD COLUMN operator_site_id INTEGER;
COMMENT ON COLUMN rounds_finished.operator_site_id IS 'Operator''s site id';
COMMENT ON COLUMN rounds_history.operator_site_id IS 'Operator''s site id';
RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE rounds_finished DROP COLUMN operator_site_id;
--rollback ALTER TABLE rounds_history DROP COLUMN operator_site_id;
--rollback RESET search_path;