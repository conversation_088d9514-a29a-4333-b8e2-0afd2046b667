--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset emanuel-alin.raileanu:2022-12-01-SWS-37984-contribution-and-win-status
--comment add status column
SET search_path = swjackpot;
CREATE TYPE enum_jackpot_operation_status AS ENUM ('pending', 'resolved', 'rejected');
ALTER TABLE jp_contribution_log ADD COLUMN IF NOT EXISTS status enum_jackpot_operation_status;
COMMENT ON COLUMN jp_contribution_log.status IS 'JP contribution status';
RESET search_path;
--rollback SET search_path = swjackpot;
--rollback ALTER TABLE jp_contribution_log DROP COLUMN status;
--rollback DROP TYPE IF EXISTS enum_jackpot_operation_status;
--rollback RESET search_path;
