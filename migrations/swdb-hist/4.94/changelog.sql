--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2022-09-06_SWS-32820-include-lobbysessionid-to-spin_history
--comment Add lobby_session_id column in spins_history table
SET search_path = swmanagement;
ALTER TABLE spins_history ADD IF NOT EXISTS lobby_session_id VARCHAR(36);
COMMENT ON COLUMN spins_history.lobby_session_id IS 'Optional player lobby session Id';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE spins_history DROP IF EXISTS lobby_session_id;
--rollback RESET search_path;
