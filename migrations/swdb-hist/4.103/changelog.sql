--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset dmitriy.palaznik:2023-02-15_SWS-39140-add-is_hidden-to-spin_history
--comment Add is_hidden column in spins_history table
SET search_path = swmanagement;
ALTER TABLE spins_history ADD IF NOT EXISTS is_hidden boolean;
COMMENT ON COLUMN spins_history.is_hidden IS 'Optional flag. Hidden for history visualization';
RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE spins_history DROP IF EXISTS is_hidden;
--rollback RESET search_path;