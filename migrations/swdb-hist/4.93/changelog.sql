--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2022-09-14_SWS-36864-add-column-operator-country-to-session-history
--comment Add 'operator_country' fields for 'sessions_history'
SET search_path TO swmanagement;
ALTER TABLE sessions_history ADD COLUMN operator_country VARCHAR(6);
COMMENT ON COLUMN sessions_history.operator_country IS 'Country player from operator';
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE sessions_history DROP COLUMN operator_country;
--rollback RESET search_path;
