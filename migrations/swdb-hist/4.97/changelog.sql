--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2022-11-08-SWS-37715-update-lobby_session_id-limitation
--comment update lobby_session_id limitation to support IPM-mock
SET search_path = swmanagement;
ALTER TABLE spins_history ALTER COLUMN lobby_session_id TYPE VARCHAR(255);
RESET search_path;

--rollback SELECT NOW();
