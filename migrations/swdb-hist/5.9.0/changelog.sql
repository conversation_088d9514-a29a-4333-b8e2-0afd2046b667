--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset andrei.stefan:2023-05-22-SWS-40208-login-audit
--comment Add mirror tables for login auditing

SET search_path = swmanagement;

CREATE TABLE IF NOT EXISTS audits_login (
    audit_id integer NOT NULL,
    entity_id integer NOT NULL,
    ts timestamp without time zone NOT NULL,
    audits_summary_id smallint,
    history jsonb,
    initiator_type enum_audits_initiator_type NOT NULL,
    initiator_name character varying(255) NOT NULL,
    ip inet NOT NULL,
    user_agent character varying(2048) NOT NULL,
    initiator_service_name character varying(255),
    audits_session_id uuid,
    initiator_issue_id character varying(255)
);

CREATE SEQUENCE audits_login_audit_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE audits_login_audit_id_seq OWNED BY audits_login.audit_id;

ALTER TABLE ONLY audits_login ALTER COLUMN audit_id SET DEFAULT nextval('audits_login_audit_id_seq'::regclass);
ALTER TABLE ONLY audits_login ADD CONSTRAINT audits_login_pkey PRIMARY KEY (audit_id) WITH (fillfactor='100');
CREATE INDEX IF NOT EXISTS idx_audits_login_ts ON audits_login (ts);

CREATE INDEX idx_audits_login_entity_id_ts ON audits_login USING btree (entity_id, ts DESC) WITH (fillfactor='100');

CREATE TABLE IF NOT EXISTS audits_login_session (
    id UUID PRIMARY KEY,
    entity_id INT NOT NULL,
    initiator_name VARCHAR(255),
    started_at timestamp without time zone NOT NULL DEFAULT NOW(),
    finished_at timestamp without time zone NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT NOW(),
    updated_at timestamp without time zone NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE audits_login_session IS 'The table contains info about audited session';
COMMENT ON COLUMN audits_login_session.entity_id IS 'Entity id';
COMMENT ON COLUMN audits_login_session.initiator_name IS 'User who id using the session';
COMMENT ON COLUMN audits_login_session.started_at IS 'Session start';
COMMENT ON COLUMN audits_login_session.finished_at IS 'Session finish';

CREATE INDEX IF NOT EXISTS idx_audits_login_session_started_at ON audits_login_session (started_at);

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP TABLE audits_login;
--rollback DROP TABLE audits_login_session;
--rollback RESET search_path;
