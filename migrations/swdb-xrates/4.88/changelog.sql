--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset mikhail.ivanov:2022-06-22-SWS-29890-align-currencies-to-eur-multipliers
--comment add new currencies into game_limits_currencies table
SET search_path = swxrates, public;

DELETE FROM game_limits_currencies WHERE currency IN ('XXX', 'LRD') AND version = 1;

INSERT INTO game_limits_currencies(currency, to_eur_multiplier, copy_limits_from, version, created_at, updated_at) VALUES
('XXX', 1, NULL, 1, now(), now()),
('LRD', 100, NULL, 1, now(), now());

--rollback SET search_path = swxrates, public;
--rollback DELETE FROM game_limits_currencies WHERE currency IN ('XXX', 'LRD') AND version = 1;
