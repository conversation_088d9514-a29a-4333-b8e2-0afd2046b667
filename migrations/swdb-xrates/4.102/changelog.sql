--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2023-01-23-SWS-38863-add-mbt-currency
--comment add new currencies into game_limits_currencies table
SET search_path = swxrates, public;

UPDATE game_limits_currencies SET to_eur_multiplier = 50 WHERE currency = 'UBT' AND version = 1;

INSERT INTO game_limits_currencies(currency, to_eur_multiplier, copy_limits_from, version, created_at, updated_at) VALUES
('MBT', 1, NULL, 1, now(), now());

--rollback SET search_path = swxrates, public;
--rollback UPDATE game_limits_currencies SET to_eur_multiplier = 100 WHERE currency = 'UBT' AND version = 1;
--rollback DELETE FROM game_limits_currencies WHERE currency = 'MBT' AND version = 1;
