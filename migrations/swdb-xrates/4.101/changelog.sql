--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2023-01-13-SWS-37813-save-toeurmultiplier-to-db-for-cryptocurrencies
--comment add new currencies into game_limits_currencies table
SET search_path = swxrates, public;

INSERT INTO game_limits_currencies(currency, to_eur_multiplier, copy_limits_from, version, created_at, updated_at) VALUES
('UBT', 100, NULL, 1, now(), now());

--rollback SET search_path = swxrates, public;
--rollback DELETE FROM game_limits_currencies WHERE currency IN ('UBT') AND version = 1;
