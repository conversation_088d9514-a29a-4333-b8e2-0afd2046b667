--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset mikhail.ivanov:2022-06-02-SWS-34403-move-game-limits-currencies-methods-from-mapi-to-currency-exchange-api
--comment create game limits currencies table
SET search_path = swxrates, public;
CREATE TABLE IF NOT EXISTS game_limits_currencies (
    currency CHAR(3) NOT NULL,
    to_eur_multiplier INTEGER,
    copy_limits_from CHAR(3),
    version INTEGER NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    PRIMARY KEY (currency, version)
);
COMMENT ON TABLE game_limits_currencies IS 'Table to keep game limits currencies parameters';
COMMENT ON COLUMN game_limits_currencies.currency IS 'Currency code';
COMMENT ON COLUMN game_limits_currencies.to_eur_multiplier IS 'Currency toEURMultiplier';
COMMENT ON COLUMN game_limits_currencies.copy_limits_from IS 'Alternative currency from which limits can be copied';
COMMENT ON COLUMN game_limits_currencies.version IS 'Version of game limits currency parameters set';

INSERT INTO game_limits_currencies(currency, to_eur_multiplier, copy_limits_from, version, created_at, updated_at) VALUES
('EUR', 1, NULL, 1, now(), now()),
('USD', 1, 'EUR', 1, now(), now()),
('GBP', 1, NULL, 1, now(), now()),
('AUD', 1, NULL, 1, now(), now()),
('AZN', 1, NULL, 1, now(), now()),
('BGN', 1, NULL, 1, now(), now()),
('BND', 1, NULL, 1, now(), now()),
('CAD', 1, NULL, 1, now(), now()),
('CHF', 1, NULL, 1, now(), now()),
('GEL', 1, NULL, 1, now(), now()),
('NZD', 1, NULL, 1, now(), now()),
('SGD', 1, NULL, 1, now(), now()),
('BMD', 1, NULL, 1, now(), now()),
('BRL', 5, NULL, 1, now(), now()),
('ILS', 5, NULL, 1, now(), now()),
('MYR', 5, NULL, 1, now(), now()),
('PEN', 5, NULL, 1, now(), now()),
('PLN', 5, NULL, 1, now(), now()),
('TRY', 5, NULL, 1, now(), now()),
('GHS', 5, NULL, 1, now(), now()),
('TND', 5, 'ILS', 1, now(), now()),
('ARS', 10, NULL, 1, now(), now()),
('CNY', 10, NULL, 1, now(), now()),
('DKK', 10, NULL, 1, now(), now()),
('HKD', 10, NULL, 1, now(), now()),
('HRK', 10, NULL, 1, now(), now()),
('MAD', 10, NULL, 1, now(), now()),
('MOP', 10, NULL, 1, now(), now()),
('NOK', 10, NULL, 1, now(), now()),
('SEK', 10, NULL, 1, now(), now()),
('VEF', 10, NULL, 1, now(), now()),
('ZAR', 10, NULL, 1, now(), now()),
('ZMW', 10, NULL, 1, now(), now()),
('CZK', 50, NULL, 1, now(), now()),
('DOP', 50, NULL, 1, now(), now()),
('HNL', 50, NULL, 1, now(), now()),
('INR', 50, NULL, 1, now(), now()),
('KGS', 50, NULL, 1, now(), now()),
('MDL', 50, NULL, 1, now(), now()),
('NIO', 50, NULL, 1, now(), now()),
('PHP', 50, NULL, 1, now(), now()),
('RUB', 50, NULL, 1, now(), now()),
('THB', 50, NULL, 1, now(), now()),
('TWD', 50, NULL, 1, now(), now()),
('UAH', 50, NULL, 1, now(), now()),
('UYU', 50, NULL, 1, now(), now()),
('VES', 50, NULL, 1, now(), now()),
('ISK', 100, NULL, 1, now(), now()),
('JPY', 100, NULL, 1, now(), now()),
('RSD', 100, NULL, 1, now(), now()),
('KES', 100, NULL, 1, now(), now()),
('MZN', 100, 'ISK', 1, now(), now()),
('CLP', 500, NULL, 1, now(), now()),
('HUF', 500, NULL, 1, now(), now()),
('KZT', 500, NULL, 1, now(), now()),
('XOF', 500, NULL, 1, now(), now()),
('CRC', 500, NULL, 1, now(), now()),
('AMD', 500, NULL, 1, now(), now()),
('AOA', 500, 'CLP', 1, now(), now()),
('NGN', 500, 'CLP', 1, now(), now()),
('XAF', 500, 'CLP', 1, now(), now()),
('KRW', 1000, NULL, 1, now(), now()),
('COP', 1000, NULL, 1, now(), now()),
('MNT', 1000, NULL, 1, now(), now()),
('TZS', 1000, NULL, 1, now(), now()),
('MMK', 1000, NULL, 1, now(), now()),
('CDF', 1000, 'COP', 1, now(), now()),
('MWK', 1000, 'COP', 1, now(), now()),
('RWF', 1000, 'COP', 1, now(), now()),
('PYG', 5000, NULL, 1, now(), now()),
('UGX', 5000, 'PYG', 1, now(), now()),
('IDR', 10000, NULL, 1, now(), now()),
('GNF', 10000, 'IDR', 1, now(), now()),
('SLL', 10000, 'IDR', 1, now(), now()),
('VND', 25000, NULL, 1, now(), now()),
('IDS', 10, NULL, 1, now(), now()),
('RUP', 10, NULL, 1, now(), now()),
('VNS', 25, NULL, 1, now(), now()),
('VDO', 25, NULL, 1, now(), now()),
('MXN', 30, NULL, 1, now(), now()),
('BNS', 1000, NULL, 1, now(), now()),
('RON', 5, NULL, 1, now(), now()),
('GEL', 5, NULL, 2, now(), now()),
('VES', 5, NULL, 2, now(), now()),
('TRY', 10, NULL, 2, now(), now()),
('RUB', 100, NULL, 2, now(), now()),
('INR', 100, NULL, 2, now(), now()),
('KGS', 100, NULL, 2, now(), now()),
('ARS', 100, NULL, 2, now(), now()),
('CLP', 1000, NULL, 2, now(), now()),
('COP', 5000, NULL, 2, now(), now()),
('VEF', 25000, NULL, 2, now(), now()),
('AOA', 1000, NULL, 2, now(), now()),
('NGN', 1000, NULL, 2, now(), now()),
('XAF', 1000, NULL, 2, now(), now()),
('CDF', 5000, NULL, 2, now(), now()),
('MWK', 5000, NULL, 2, now(), now()),
('RWF', 5000, NULL, 2, now(), now());
--rollback SET search_path = swxrates, public;
--rollback DROP TABLE IF EXISTS game_limits_currencies;
