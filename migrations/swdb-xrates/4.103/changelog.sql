--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2023-02-07-SWS-37759-add-the-new-currency-codes-to-the-currencies.json
--comment Currency exchange rates
SET search_path = swxrates, public;

ALTER TABLE exchange_rates ALTER COLUMN from_currency TYPE CHARACTER VARYING(255);
ALTER TABLE exchange_rates ALTER COLUMN to_currency TYPE CHARACTER VARYING(255);

--rollback SET search_path = swxrates, public;
--rollback ALTER TABLE exchange_rates ALTER COLUMN currency TYPE CHAR(3);
--rollback ALTER TABLE exchange_rates ALTER COLUMN currency TYPE CHAR(3);


--changeset vladimir.minakov:2023-02-08-SWS-37759-add-the-new-currency-codes-to-the-currencies.json
--comment Currency exchange rates
SET search_path = swxrates, public;

DELETE FROM game_limits_currencies WHERE currency IN ('MEH', 'MLC', 'MCH', 'DOG', 'TET') AND version = 1;

INSERT INTO game_limits_currencies(currency, to_eur_multiplier, copy_limits_from, version, created_at, updated_at) VALUES
('MEH', 1, NULL, 1, now(), now()),
('MLC', 10, NULL, 1, now(), now()),
('MCH', 10, NULL, 1, now(), now()),
('DOG', 10, NULL, 1, now(), now()),
('TET', 10, NULL, 1, now(), now());

--rollback SET search_path = swxrates, public;
--rollback DELETE FROM game_limits_currencies WHERE currency IN ('MEH', 'MLC', 'MCH', 'DOG', 'TET') AND version = 1;

--changeset georgiy.kovrey:2023-02-16-SWS-39105-toEurMultiplier-decimal-value
--comment Alter column 'to_eur_multiplier' of 'game_limits_currencies' to be decimal type
SET search_path = swxrates, public;
ALTER TABLE game_limits_currencies ALTER COLUMN to_eur_multiplier TYPE numeric(25,15);
RESET search_path;
--rollback SET search_path TO swmanagement;
--rollback ALTER TABLE game_limits_currencies ALTER COLUMN to_eur_multiplier TYPE integer;
--rollback RESET search_path;