--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2023-03-03-SWS-39489-add-new-currency-codes-btc-eth-ltc-bch-dog-doge
--comment Currency exchange rates
SET search_path = swxrates, public;

DELETE FROM game_limits_currencies WHERE currency IN ('DOG', 'BCH', 'LTC', 'ETH', 'BTC') AND version = 1;

INSERT INTO game_limits_currencies(currency, to_eur_multiplier, copy_limits_from, version, created_at, updated_at) VALUES
('DOG', 1000000000, NULL, 1, now(), now()),
('BCH', 1000000, NULL, 1, now(), now()),
('LTC', 1000000, NULL, 1, now(), now()),
('ETH', 650000, NULL, 1, now(), now()),
('BTC', 4000, NULL, 1, now(), now());

--rollback SET search_path = swxrates, public;
--rollback DELETE FROM game_limits_currencies WHERE currency IN ('DOG', 'BCH', 'LTC', 'ETH', 'BTC') AND version = 1;


--changeset vladimir.minakov:2023-03-09-SWS-39489-add-new-currency-codes-btc-eth-ltc-bch-dog-doge
--comment Currency exchange rates
SET search_path = swxrates, public;

DELETE FROM game_limits_currencies WHERE currency IN ('ETH') AND version = 1;

INSERT INTO game_limits_currencies(currency, to_eur_multiplier, copy_limits_from, version, created_at, updated_at) VALUES
('ETH', 1000000, NULL, 1, now(), now());

--rollback SET search_path = swxrates, public;
--rollback DELETE FROM game_limits_currencies WHERE currency IN ('ETH') AND version = 1;
