--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement, public;
--example: CREATE TABLE tmp(id int);
--example: --rollback DROP TABLE tmp;


--changeset andrey.shmigiro:2019-11-01-SWS-XXXX-start-release-4.27.0
--comment label for 4.27.0
select now();
--rollback select now();


--changeset andrey.shmigiro:2019-02-05-SWS-8669-swxrates
--comment Create schema swxrates
CREATE SCHEMA swxrates AUTHORIZATION swsystem;
CREATE ROLE swxrates PASSWORD 'changeme' LOGIN;
GRANT CONNECT ON DATABASE swdb TO swxrates;
REVOKE CONNECT ON DATABASE postgres FROM swxrates;
ALTER ROLE swxrates SET search_path = swxrates, public;
GRANT USAGE ON SCHEMA swxrates TO swxrates;

ALTER DEFAULT PRIVILEGES IN SCHEMA swxrates FOR USER swsystem
  GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO swxrates;
ALTER DEFAULT PRIVILEGES IN SCHEMA swxrates FOR USER swsystem
  GRANT SELECT, USAGE ON SEQUENCES TO swxrates;
--rollback SELECT now();


--changeset vera.kruhliakova:2019-02-06-SWS-8669-exchange-rates
--comment Currency exchange rates
SET search_path = swxrates, public;
CREATE TABLE IF NOT EXISTS exchange_rates (
    rate_date DATE NOT NULL,
    from_currency CHAR(3) NOT NULL,
    to_currency CHAR(3) NOT NULL,
    rate NUMERIC NOT NULL,
    provider_date DATE NOT NULL,
    provider CHARACTER VARYING(255),
    created_at timestamp without time zone NOT NULL,
    PRIMARY KEY (rate_date, from_currency, to_currency)
);
COMMENT ON TABLE exchange_rates IS 'Currency exchange rates';
COMMENT ON COLUMN exchange_rates.from_currency IS 'Currency code of rate source';
COMMENT ON COLUMN exchange_rates.to_currency IS 'Currency code of rate target';
COMMENT ON COLUMN exchange_rates.rate IS 'Exchange rate value';
COMMENT ON COLUMN exchange_rates.rate_date IS 'Date of when currency rate is used in the system';
COMMENT ON COLUMN exchange_rates.provider_date IS 'Date of currency rate provider';
COMMENT ON COLUMN exchange_rates.provider IS 'Currency rate provider (oanda, oxr, etc)';
COMMENT ON COLUMN exchange_rates.created_at IS 'Creation time';
CREATE INDEX IF NOT EXISTS idx_exchange_rates_rate_date on exchange_rates  USING btree (rate_date);
--rollback SET search_path = swxrates, public;
--rollback DROP TABLE IF EXISTS exchange_rates;


--changeset vera.kruhliakova:2019-10-01-SWS-13072-rates-ask-bid
--comment Store bid and ask exchange rates
SET search_path = swxrates, public;
CREATE TYPE enum_exchange_rate_type AS ENUM ('bid', 'ask');
ALTER TABLE exchange_rates ADD COLUMN type enum_exchange_rate_type NOT NULL DEFAULT 'bid';
COMMENT ON COLUMN exchange_rates.type IS 'Exchange rate type - ask or bid';
ALTER TABLE exchange_rates DROP CONSTRAINT exchange_rates_pkey, ADD PRIMARY KEY (rate_date, from_currency, to_currency, type);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_rate_date_type on exchange_rates USING btree (rate_date, type);
DROP INDEX IF EXISTS idx_exchange_rates_rate_date;
--rollback SET search_path = swxrates, public;
--rollback DELETE FROM exchange_rates where type='ask';
--rollback DROP INDEX IF EXISTS idx_exchange_rates_rate_date_type; CREATE INDEX IF NOT EXISTS idx_exchange_rates_rate_date on exchange_rates USING btree (rate_date);
--rollback ALTER TABLE exchange_rates DROP CONSTRAINT exchange_rates_pkey, ADD PRIMARY KEY (rate_date, from_currency, to_currency); ALTER TABLE exchange_rates DROP COLUMN type; DROP TYPE IF EXISTS enum_exchange_rate_type;
