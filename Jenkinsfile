@Library('sw_common_library') _

pipeline {
    agent any

    options {
        disableConcurrentBuilds()
    }

    environment {
        REPO = "skywindgroup"
        APPLICATION = "sw-database"
        GC_REGION = "asia.gcr.io"
        GC_PROJECT = "gcpstg"
        REVISION = sh(returnStdout: true, script: 'git rev-parse HEAD').trim()
        BRANCH_NAME_NORMALIZED = "${BRANCH_NAME.toLowerCase().replace("/", "_")}"
        DEV_PROJECT = "sw-dev-qa"
        NAMESPACE = "dev"
        DEV_DEPLOMENTS_LIST = "pgsql-master-db"
    }

    stages {
        stage('Build') {
            steps {
                sh 'make'
            }
        }
        stage('Push Docker Image') {
            when {
                not {
                    branch 'PR-*'
                }
            }
            steps {
                sh "docker tag ${REPO}/${APPLICATION}:${BRANCH_NAME_NORMALIZED} ${GC_REGION}/${GC_PROJECT}/${APPLICATION}:${BRANCH_NAME_NORMALIZED}"
              	sh "docker tag ${REPO}/${APPLICATION}:${BR<PERSON>CH_NAME_NORMALIZED} ${GC_REGION}/${GC_PROJECT}/${APPLICATION}:${REVISION}"
                sh "docker push ${GC_REGION}/${GC_PROJECT}/${APPLICATION}:${BRANCH_NAME_NORMALIZED}"
                sh "docker push ${GC_REGION}/${GC_PROJECT}/${APPLICATION}:${REVISION}"
                script {
                        try {
                            GCR_IMAGE = sh(returnStdout: true, script: "docker image inspect '${GC_REGION}/${GC_PROJECT}/${APPLICATION}:${REVISION}' | jq .[0] | jq -c '.RepoDigests' | jq .[0]").trim()
                            currentBuild.description = "${GCR_IMAGE}"
                        } catch (Exception e) {
                            echo 'Image inspection failed!'
                    }
                }
            }
        }
        stage('Clean up') {
            steps {
                script{
                    if (BRANCH_NAME ==~ /((?!PR-).*)/) {
              	        sh "docker rmi ${GC_REGION}/${GC_PROJECT}/${APPLICATION}:${REVISION}"
                        sh "docker rmi ${GC_REGION}/${GC_PROJECT}/${APPLICATION}:${BRANCH_NAME_NORMALIZED}"
                    }
                }
                sh "docker rmi ${REPO}/${APPLICATION}:${BRANCH_NAME_NORMALIZED}"
            }
        }
        stage('Deploy to Dev Env') {
            when {
                branch 'develop'
            }
            steps {
                deployStatefulSet()
            }
        }
    }
    post {
        failure {
            emailext (
                to: '<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>',
                body: "Check console output at $BUILD_URL to view the results.",
                subject: 'Build $BUILD_STATUS! - $PROJECT_NAME',
                attachLog: true,
                compressLog: true
            )
        }
    }
}
