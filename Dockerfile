FROM ubuntu:18.04

ARG UBUNTU_CODENAME
ENV UBUNTU_CODENAME ${UBUNTU_CODENAME:-bionic}

ARG LB_VERSION
ENV LB_VERSION ${LB_VERSION:-3.5.3}

ARG PG_JDBC_VERSION
ENV PG_JDBC_VERSION ${PG_JDBC_VERSION:-42.1.1}

ARG PG_VERSION
ENV PG_VERSION ${PG_VERSION:-10}

RUN apt update && \
    echo 'debconf debconf/frontend select Noninteractive' | debconf-set-selections && \
    apt install -y curl dialog apt-utils ca-certificates openjdk-8-jre-headless locales net-tools apt-transport-https sudo gnupg && \
    locale-gen en_US.UTF-8

ENV LANG en_US.UTF-8
ENV LC_ALL en_US.UTF-8
ENV LANGUAGE en_US.UTF-8


RUN addgroup --gid 110 postgres
RUN adduser  --uid 106 --ingroup postgres --system --gecos "PostgreSQL administrator" --shell "/bin/bash" --home "/var/lib/postgresql" --no-create-home postgres
RUN curl https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor | sudo tee /etc/apt/trusted.gpg.d/apt.postgresql.org.gpg >/dev/null
RUN echo "deb http://apt-archive.postgresql.org/pub/repos/apt ${UBUNTU_CODENAME}-pgdg main" > /etc/apt/sources.list.d/pgdg.list
RUN apt update
RUN apt install -y postgresql-${PG_VERSION}
RUN apt install -y postgresql-${PG_VERSION}-cron

ADD https://ubuntu-repo:<EMAIL>/swg/ubuntu-repo-local/pool/${UBUNTU_CODENAME}/pg10/postgresql-${PG_VERSION}-pathman_1.5.12-1_amd64.deb /

RUN dpkg -i /postgresql-${PG_VERSION}-pathman_1.5.12-1_amd64.deb && \
    rm -f /postgresql-${PG_VERSION}-pathman_1.5.12-1_amd64.deb

ADD https://ubuntu-repo:<EMAIL>/swg/ubuntu-repo-local/pool/${UBUNTU_CODENAME}/pg10/postgresql-${PG_VERSION}-hashids_1.2.1-1_amd64.deb /

RUN dpkg -i /postgresql-${PG_VERSION}-hashids_1.2.1-1_amd64.deb && \
    rm -f /postgresql-${PG_VERSION}-hashids_1.2.1-1_amd64.deb

ENV PATH $PATH:/usr/lib/postgresql/$PG_VERSION/bin
ENV PGDATA /var/lib/postgresql/$PG_VERSION/main/data

VOLUME /var/lib/postgresql/$PG_VERSION/main

RUN curl -L -o /tmp/liquibase.tar.gz https://github.com/liquibase/liquibase/releases/download/liquibase-parent-${LB_VERSION}/liquibase-${LB_VERSION}-bin.tar.gz && \
    mkdir /opt/liquibase && \
    cd /tmp && tar xzf liquibase.tar.gz -C /opt/liquibase && \
    chmod +x /opt/liquibase/liquibase && \
    curl -L -o /opt/liquibase/lib/postgresql.jar https://jdbc.postgresql.org/download/postgresql-${PG_JDBC_VERSION}.jar

ADD migrations /migrations
ADD /image_files/migrate.sh /migrations/
ADD /image_files/migrate-xrates.sh /migrations/
ADD /image_files/migrate-hist.sh /migrations/
ADD /image_files/entrypoint.sh /usr/local/bin
ADD /image_files/postgresql.conf /image_files/pg_hba.conf /etc/postgresql/${PG_VERSION}/main/

RUN chmod +x /migrations/*.sh
RUN chmod +x /usr/local/bin/entrypoint.sh && \
    mkdir -p /var/run/postgresql/${PG_VERSION}-main.pg_stat_tmp && \
    chown postgres:postgres /var/run/postgresql/${PG_VERSION}-main.pg_stat_tmp && \
    echo 'postgres ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers.d/postgres

USER postgres

EXPOSE 5432

ENTRYPOINT ["entrypoint.sh"]

CMD postgres -c config_file=/etc/postgresql/$PG_VERSION/main/postgresql.conf
