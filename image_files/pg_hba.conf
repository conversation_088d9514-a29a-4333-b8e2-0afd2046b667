# PostgreSQL Client Authentication Configuration File
# ===================================================
# Last modified by TLU: Sep 25, 2020

# Database administrative login by Unix domain socket
local   all             postgres                                peer

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# Allow replication connections from localhost, by a user with the replication privilege.
local   replication     all                                     peer
host    replication     all             127.0.0.1/32            md5
host    replication     all             ::1/128                 md5

local   all             all                                     trust
host    all             all             127.0.0.1/32            trust
host    all             all             ::1/128                 trust

# host    all             all             10.0.0.0/8              reject
host    all             all             **********/24           trust
host    all             all             *********/24            md5
host    all             all             ********/16             md5
host    all             all             ***********/21          md5  # ***********-************* google (old)
host    all             all             *********/24            md5  # *********-*********** google
host    all             all             *********/16          	md5  # *********-************* private