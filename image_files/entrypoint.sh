#!/usr/bin/env bash

set -e

DB_NAME=swdb
DB_NAME_XRATES=swdbxr
DB_NAME_HIST=swdbhist

DB_OWNER=swsystem

echo "PostgreSQL server version is: " `postgres -V`
echo "Pathman version:" `apt-cache policy postgresql-10-pathman | grep "Installed"`

# Iniitalize database if $PGDATA is empty
if [ ! -s "$PGDATA/PG_VERSION" ]; then
    #sudo chown -R postgres:postgres /var/lib/postgresql/$PG_VERSION/main

    initdb -k

    pg_ctl -o "-c config_file=/etc/postgresql/$PG_VERSION/main/postgresql.conf" -w start

    # create MAIN database
    echo "Creating database $DB_NAME..."

    createdb --locale=en_US.UTF-8 -E UTF8 -T template0 -O postgres $DB_NAME

    echo "Database $DB_NAME has been created"

    # create X-RATES database
    echo "Creating database $DB_NAME_XRATES..."

    createdb --locale=en_US.UTF-8 -E UTF8 -T template0 -O postgres $DB_NAME_XRATES

    echo "Database $DB_NAME_XRATES has been created"

    # create superuser
    echo "Creating superuser $DB_OWNER..."

    createuser -lDRS $DB_OWNER --superuser

    echo "Superuser $DB_OWNER has been created"

    if [ ! -z "${SWSYSTEM_PASSWORD}" ]; then
        echo "Setting up password for $DB_OWNER"

        psql -w -c "ALTER USER $DB_OWNER WITH PASSWORD '$SWSYSTEM_PASSWORD';" -U postgres $DB_NAME
    fi

    psql -w -c "CREATE SCHEMA $DB_OWNER AUTHORIZATION $DB_OWNER; ALTER ROLE $DB_OWNER SET search_path = $DB_OWNER, public;" -U postgres $DB_NAME

    psql -w -c "CREATE SCHEMA $DB_OWNER AUTHORIZATION $DB_OWNER; ALTER ROLE $DB_OWNER SET search_path = $DB_OWNER, public;" -U postgres $DB_NAME_XRATES

    # extensions
    for ext in pgcrypto pg_pathman pg_stat_statements pg_cron pg_hashids; do
        echo "Creating extension $ext..."

        psql -w -c "CREATE EXTENSION IF NOT EXISTS $ext;" -U postgres $DB_NAME

        # psql -w -c "CREATE EXTENSION IF NOT EXISTS $ext;" -U postgres $DB_NAME_XRATES

        echo "Extension $ext has been created"
    done

    pg_ctl -o "-c config_file=/etc/postgresql/$PG_VERSION/main/postgresql.conf" stop
fi

echo "Starting database..."

pg_ctl -o "-c config_file=/etc/postgresql/$PG_VERSION/main/postgresql.conf" -w start

if [ $(psql -l | grep $DB_NAME_HIST | wc -l) -eq 0 ]; then

    # create ARCHIVE database
    echo "Creating database $DB_NAME_HIST..."

    createdb --locale=en_US.UTF-8 -E UTF8 -T template0 -O postgres $DB_NAME_HIST

    echo "Database $DB_NAME_HIST has been created"

    psql -w -c "CREATE SCHEMA $DB_OWNER AUTHORIZATION $DB_OWNER;" -U postgres $DB_NAME_HIST
fi

psql -w -c "ALTER EXTENSION pg_pathman UPDATE; SET pg_pathman.enable = t;" -U postgres $DB_NAME

echo "Starting Liquibase update..."

/migrations/migrate.sh --database=$DB_NAME --username=$DB_OWNER

/migrations/migrate-xrates.sh --database=$DB_NAME_XRATES --username=$DB_OWNER

/migrations/migrate-hist.sh --database=$DB_NAME_HIST --username=$DB_OWNER

echo "Stoping database..."

pg_ctl -o "-c config_file=/etc/postgresql/$PG_VERSION/main/postgresql.conf" -w stop

exec "$@"
