#! /bin/bash

set -e

while [[ $# -gt 0 ]]; do
    case "$1" in
        --username=*)
            USERNAME="${1#*=}"
            ;;
        --password=*)
            PASSWORD="${1#*=}"
            ;;
        --database=*)
            DATABASE="${1#*=}"
            ;;
        --host=*)
            HOST="${1#*=}"
            ;;
        --port=*)
            PORT="${1#*=}"
            ;;
        *)
            echo "Invalid argument $1"
            exit 1
    esac

    shift
done

if [[ -z "$USERNAME" ]]; then
    echo 'Username is not specified'

    exit 1;
fi

if [[ -z "$DATABASE" ]]; then
    echo 'Database is not specified'

    exit 1;
fi

LB_COMMAND=/opt/liquibase/liquibase
LB_OPTIONS="--changeLogFile=/migrations/swdb-xrates/changelog.xml --username=$USERNAME"

if [[ -z "$HOST" ]]; then
    LB_OPTIONS="$LB_OPTIONS --url=*************************************"
else
    LB_OPTIONS="$LB_OPTIONS --url=***************************************"
fi

if [[ -n "$PASSWORD" ]]; then
    LB_OPTIONS="$LB_OPTIONS --password=$PASSWORD"
fi

$LB_COMMAND $LB_OPTIONS update
